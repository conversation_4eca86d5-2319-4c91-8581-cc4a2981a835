<se-modal
  [data]="data"
  (modalOutputEvent)="closeModal()"
  (modalSecondaryButtonEvent)="closeModal()"
>
  <p>
    {{ 'SE_TRIBUTS_MF.CALCULATIONS.MODAL_INTEGRATED_QUOTA.DETAIL' | translate }}
  </p>

  <div
    class="card rounded-0 p-4 pb-3 mb-4 d-flex justify-content-between flex-row"
  >
    <p>
      {{
        'SE_TRIBUTS_MF.CALCULATIONS.MODAL_INTEGRATED_QUOTA.PAYABLE_BASE'
          | translate
      }}
      <ng-icon
        class="tooltip-icon"
        name="matInfo"
        [pTooltipAccessible]="
          'SE_TRIBUTS_MF.CALCULATIONS.MODAL_INTEGRATED_QUOTA.PAYABLE_BASE_TOOLTIP'
            | translate
        "
      ></ng-icon>
    </p>

    <p>
      {{
        modalTaxRateDetail?.baseImposable || 0
          | currency: 'EUR' : 'symbol' : '1.2-2'
      }}
    </p>
  </div>

  <div class="card rounded-0 p-4 pb-3">
    <div class="d-flex justify-content-between flex-row">
      <p>
        {{
          'SE_TRIBUTS_MF.CALCULATIONS.MODAL_INTEGRATED_QUOTA.TAXABLE_INCOME_UP_TO'
            | translate
        }}
        <span>{{
          modalTaxRateDetail?.baseImposableFinsA || 0
            | currency: 'EUR' : 'symbol' : '1.2-2'
        }}</span>
      </p>

      <p>
        {{
          modalTaxRateDetail?.baseImposableFinsACalcul || 0
            | currency: 'EUR' : 'symbol' : '1.2-2'
        }}
      </p>
    </div>
    <div class="d-flex justify-content-between flex-row">
      <p>
        {{
          'SE_TRIBUTS_MF.CALCULATIONS.MODAL_INTEGRATED_QUOTA.REMAINING_TAXABLE_INCOME'
            | translate
        }}
        <span>{{
          modalTaxRateDetail?.restaBaseImposableFinsA || 0
            | currency: 'EUR' : 'symbol' : '1.2-2'
        }}</span>
        <span> al </span>
        <span>{{
          getPercentTipusAplicable(modalTaxRateDetail?.tipusAplicable)
        }}</span>
      </p>
      <p>
        {{
          modalTaxRateDetail?.restaBaseImposableFinsACalcul || 0
            | currency: 'EUR' : 'symbol' : '1.2-2'
        }}
      </p>
    </div>
  </div>

  <div
    class="card p-4 border-top-0 rounded-0 d-flex justify-content-end flex-row align-items-center"
  >
    <p class="mb-0 fw-bolder">
      {{
        'SE_TRIBUTS_MF.CALCULATIONS.MODAL_INTEGRATED_QUOTA.QUOTA_INTEGRA'
          | translate
      }}
    </p>
    <h5 class="ps-5 mb-0 fw-bolder">
      {{
        modalTaxRateDetail?.quotaTributaria || 0
          | currency: 'EUR' : 'symbol' : '1.2-2'
      }}
    </h5>
  </div>
</se-modal>
