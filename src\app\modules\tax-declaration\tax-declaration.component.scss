@import "bootstrap/scss/functions";
@import "bootstrap/scss/variables";
@import "bootstrap/scss/mixins/breakpoints";

// RESPONSIVE
.link-style {
  color: var(--color-blue-600);
  text-decoration: underline;
}

.label-text-overflow {
  ::ng-deep .input-label label {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

/* XL - 1200px */
// @media (width >= 1200px) {
@include media-breakpoint-up(xl) {
  .total {
    max-width: 21%;
  }
}
