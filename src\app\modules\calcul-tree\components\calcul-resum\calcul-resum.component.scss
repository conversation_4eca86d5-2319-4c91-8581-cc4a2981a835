@import 'bootstrap/scss/functions';
@import 'bootstrap/scss/variables';
@import 'bootstrap/scss/variables-dark';
@import 'bootstrap/scss/mixins/breakpoints';

:host {
  ::ng-deep .p-panel-content {
    padding-bottom: 8rem !important;
    position: relative;

    @include media-breakpoint-up(sm) {
      padding-bottom: 5.75rem !important;
    }
  }
}

.calcul-resum {
  &__footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: var(--color-blue-200);
    font-size: var(--text-xl);
    line-height: var(--line-2xl);
    font-weight: var(--font-semibold);
    padding: 1rem 1.75rem;
    text-align: right;
  }
}
