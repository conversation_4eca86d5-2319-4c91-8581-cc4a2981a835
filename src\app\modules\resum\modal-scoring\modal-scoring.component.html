<se-modal
  *ngIf="data"
  class="app-modal-scoring"
  [data]="data"
  (modalOutputEvent)="trasspasarFormulari($event)"
  (modalSecondaryButtonEvent)="closeModal()"
>
  <se-exception-viewer id="pt-exceptions-container"></se-exception-viewer>
  <se-alert
    [title]="
      'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PENDENTS_TRAMITAR.MODAL_SCORING.ALERT'
        | translate
    "
    [type]="'info'"
    [closeButton]="false"
  >
  </se-alert>
  <p
    [innerHTML]="
      'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PENDENTS_TRAMITAR.MODAL_SCORING.P1'
        | translate
    "
  ></p>
  <div class="mt-4">
    <mf-seguretat-scoring
      *axLazyElement
      class="mb-4 mb-md-0"
      [taxPayerDocumentLabel]="
        'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PENDENTS_TRAMITAR.MODAL_SCORING.NIF'
          | translate
      "
      [validatorsNIF]="validatorsNIF"
      [taxPayerNameLabel]="
        'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PENDENTS_TRAMITAR.MODAL_SCORING.NOM'
          | translate
      "
      (onScoringChange)="onScoringChange($event)"
      (onValidationChange)="onScoringValidationChange($event)"
      (onTaxpayerChange)="onScoringTaxpayerChange($event)"
    >
    </mf-seguretat-scoring>
  </div>

  <hr />

  <div>
    <form *ngIf="modalScoringForm" [formGroup]="modalScoringForm">
      <se-switch
        id="mantenirOriginal"
        [label]="
          'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PENDENTS_TRAMITAR.MODAL_SCORING.GUARDEU_COPIA'
            | translate
        "
        formControlName="mantenirOriginal"
      >
      </se-switch>
      <span class="text-gray-600 text-sm" style="margin-left: 40px">
        {{
          'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PENDENTS_TRAMITAR.MODAL_SCORING.GUARDEU_COPIA_SMALL'
            | translate
        }}
      </span>
    </form>
  </div>
</se-modal>
