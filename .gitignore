# See http://help.github.com/ignore-files/ for more about ignoring files.

package-lock.json

# compiled output
/dist
/tmp
/out-tsc
/elements
# Only exists if <PERSON><PERSON> was run
/bazel-out

# Node
/node_modules
npm-debug.log
yarn-error.log

# IDEs and editors
/.idea
.project
.classpath
.vscode
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.history/*

# Miscellaneous
/.angular/cache
.sass-cache/
/connect.lock
/coverage
/libpeerconnection.log
testem.log
/typings

# System files
.DS_Store
Thumbs.db

# Mf
elements
package-lock.json
