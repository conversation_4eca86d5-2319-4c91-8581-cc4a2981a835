import {
  Component,
  EventEmitter,
  HostBinding,
  Inject,
  Input,
  LOCALE_ID,
  Output,
} from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';

import { formatNumber } from '@angular/common';
import { Tax } from '../models';
import { TaxFormGroupHeaderLabels } from '../tax-form-group';

@Component({
  selector: 'app-tax-input',
  templateUrl: './tax-input.component.html',
  styleUrls: ['./tax-input.component.scss'],
})
export class TaxInputComponent {
  @HostBinding('class') class = 'row flex-nowrap';

  @Input({ required: true }) id!: string;

  @Input() label!: string;

  @Input() labelInvisible = false;

  @Input() set value(newValue: number | undefined) {
    // el doble "==" hace que "null == undefined" sea verdadero
    if (newValue == this.value) return;
    const noEmit = { emitEvent: false };
    this.valueControl?.setValue(newValue, noEmit);
    this.totalPriceControl?.setValue(this.calculateTotalPrice(), noEmit);
  }

  get value() {
    return this.valueControl?.value;
  }

  @Input({ required: true }) units!: string;

  @Input({ required: true }) set pricePerUnit(newPricePerUnit: number) {
    newPricePerUnit ??= 0;
    if (newPricePerUnit === this.pricePerUnit) return;
    this._pricePerUnit = newPricePerUnit;
    this.totalPriceControl?.setValue(this.calculateTotalPrice(), {
      emitEvent: false,
    });
  }

  get pricePerUnit(): number {
    return this._pricePerUnit;
  }

  @Input({ required: true }) startDate!: string;

  @Input({ required: true }) endDate!: string;

  @Input({ required: true }) hideDates = false;

  @Input() headerLabels: TaxFormGroupHeaderLabels = {
    datesHeaderLabel: '',
    pricePerUnitHeaderLabel: '',
    totalPriceHeaderLabel: '',
  };

  @Output() onChange = new EventEmitter<Tax>();

  protected form: FormGroup;

  private _pricePerUnit = 0;

  private destroyed$ = new Subject<void>();

  constructor(
    private fb: FormBuilder,
    @Inject(LOCALE_ID) private locale: string
  ) {
    this.form = this.fb.group({ value: [], totalPrice: [0] });

    this.totalPriceControl?.disable();

    this.valueControl?.valueChanges
      .pipe(takeUntil(this.destroyed$))
      .subscribe((value) => {
        const totalPrice = this.calculateTotalPrice();
        this.totalPriceControl?.setValue(totalPrice);
        this.onChange.emit({
          value: value,
          pricePerUnit: this.pricePerUnit,
          totalPrice,
          startDate: this.startDate,
          endDate: this.endDate,
        });
      });
  }

  private get totalPriceControl() {
    return this.form.get('totalPrice') as FormControl<number>;
  }

  private get valueControl() {
    return this.form.get('value') as FormControl<number | undefined>;
  }

  private calculateTotalPrice(): number {
    const newTotalPrice = (this.value ?? 0) * this.pricePerUnit;
    return Number(
      formatNumber(newTotalPrice, this.locale, '1.2-2')
        .replace(/\./g, '')
        .replace(',', '.')
    );
  }
}
