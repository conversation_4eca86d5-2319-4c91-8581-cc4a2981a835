import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
  selector: 'app-modify-save-buttons',
  templateUrl: './modify-save-buttons.component.html',
})
export class ModifySaveButtonsComponent {
  @Input()
  readonly = true;

  @Output()
  readonlyChange: EventEmitter<boolean> = new EventEmitter();

  @Output()
  saveEvent: EventEmitter<boolean> = new EventEmitter();

  @Input()
  isDisabled = true;

  toggleEdit(): void {
    this.readonly = !this.readonly;
    this.readonlyChange.emit(this.readonly);
  }

  save(): void {
    this.toggleEdit();
    this.saveEvent.emit();
  }
}
