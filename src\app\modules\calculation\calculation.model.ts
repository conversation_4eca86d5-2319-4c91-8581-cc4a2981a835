import { Constant } from 'src/app/core/models/constants.enum';

export class RequestSaveSelfAssessment {
  idTramit: string = '';
  model: string = Constant.MODEL;
  dataFiTermini: string | Date = '';
  dataIniciComput: string | null | Date = null;
  dataFiComput: string | Date = '';
  requerimentAdministracio: boolean = false;
  quotasLiquidades: number = 0;
  aplicarRecarrec: boolean = false;
  percentajeRecarrec: number = 0;
  recarrec: number = 0;
  reduccioRecarrec: boolean = false;
  interessos: number = 0;
  total: number = 0;
}

export interface RequestPeriodInfo {
  impost: string;
  year: string;
  period: string;
  model: string;
}

export interface ResponsePeriodInfo {
  codiPeriode: string;
  dataIniciPeriode: Date;
  dataFiPeriode: Date;
  dataIniciPresentacio: Date;
  dataFiPresentacio: Date;
  dataFiVoluntaria: Date;
  descripcioES: string;
  descripcioCA: string;
  codiImpost: string;
  model: string;
}

export interface ResponseCalcul {
  baseImposable: number;
  quotaIntegra: number;
  bonificacion: number;
  quotaLiquida: number;
  quotesLiquidadesAnteriorment: number;
  quotaResultant: number;
}

export interface CalculInfo {
  quotaTotal: number;
  quotaLiquidades: number;
  quotaLiquidadesTrimestri?: number;
}

export interface SummaryTemplate {
  class?: string;
  translate: string;
  value?: string | null;
  lineBreak?: boolean;
}
