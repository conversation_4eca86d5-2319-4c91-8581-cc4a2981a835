@import "bootstrap/scss/functions";
@import "bootstrap/scss/variables";
@import "bootstrap/scss/mixins/breakpoints";

.label-bold {
  ::ng-deep .input.readonly {
    font-weight: bold;
  }
}

.label-text-overflow {
  ::ng-deep .input-label label {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

// RESPONSIVE

/* XL - 1200px */
// @media (width >= 1200px) {
@include media-breakpoint-up(xl) {
  .max-widht {
    max-width: 13%;
    width: 13%;
  }

  .min-widht {
    min-width: 220px;
  }
}
