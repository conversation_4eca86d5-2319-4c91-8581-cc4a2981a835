import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import { Subject, take, takeUntil } from 'rxjs';
import { SeModalService, SeProgressModal } from 'se-ui-components-mf-lib';

import { PresentacioIPagamentsEndpointsService } from './presentacio-i-pagament-endpoints.service';
import { HeaderInfoService } from 'src/app/core/services/header-info.service';
import { StoreService } from '@core/services';
import {
  SelfAssessmentState,
  SelfAssessmentStatus,
} from '@core/models/self-assessment-status.model';
import { AppRoutes } from '@core/models/app-routes.enum';

@Injectable({
  providedIn: 'root',
})
export class PresentacioIPagamentsService {
  private unsubscribe: Subject<void> = new Subject();

  constructor(
    private storeService: StoreService,
    private endpointsService: PresentacioIPagamentsEndpointsService,
    private router: Router,
    private modalService: SeModalService,
    private translateService: TranslateService,
    private header: HeaderInfoService,
  ) {}

  runPresentacio = (modalRef: NgbModalRef, navigate = true): void => {
    if (this.storeService.selfAssessmentId) {
      const idAutoliquidacio = this.storeService.selfAssessmentId;
      this.endpointsService
        .postPresentation(idAutoliquidacio)
        .pipe(take(1))
        .subscribe({
          next: () => {
            this.checkSelfAssessmentStatus(
              modalRef,
              idAutoliquidacio,
              navigate,
            );
          },
          error: () => {
            modalRef.close();
          },
        });
    }
  };

  runPagament = (): void => {
    if (this.storeService.selfAssessmentId) {
      const idAutoliquidacio = this.storeService.selfAssessmentId;
      const progressModal: SeProgressModal = {
        interval: 10,
        message: this.translateService.instant(
          'SE_GASOS_MF.MODULE_PRESENTACION_I_PAGAMENT.PAGAMENT.MODAL_MESSAGE',
        ),
      };

      const modalRef = this.modalService.openProgressModal(
        progressModal.interval!,
        progressModal.message!,
      );

      this.checkSelfAssessmentStatus(modalRef, idAutoliquidacio, true);
    }
  };

  private checkSelfAssessmentStatus = (
    modalRef: NgbModalRef,
    idAutoliquidacio: string,
    navigate = true,
  ): void => {
    modalRef.componentInstance.intervalOutput
      .pipe(takeUntil(this.unsubscribe))
      .subscribe(() => {
        this.endpointsService
          .getStatusAutoliquidacio(idAutoliquidacio)
          .pipe(takeUntil(this.unsubscribe))
          .subscribe({
            next: (response) => {
              if (response?.content) {
                this.header.status = response?.content
                  .estat as SelfAssessmentStatus;
                this.header.presentationDate =
                  response?.content.dataPresentacio;
                if (
                  this.isPresented(response?.content.estat) ||
                  this.isPaid(response?.content.estat) ||
                  this.hasError(response?.content.estat)
                ) {
                  this.closeAndDestroy(modalRef, response?.content.estat);
                  this.storeService.presentedAl = true;
                  (navigate || this.hasError(response?.content.estat)) &&
                    this.router.navigate([AppRoutes.RESULT], {
                      state: { statusAutoliquidacio: response?.content.estat },
                    });
                }
              }
            },
            error: () => {
              this.closeAndDestroy(modalRef);
            },
          });
      });
  };

  private isPresented = (estat: SelfAssessmentState | undefined): boolean =>
    estat === SelfAssessmentState.PRESENTAT ||
    estat === SelfAssessmentState.PENDENT_PAGAMENT;

  private isPaid = (estat: SelfAssessmentState | undefined): boolean =>
    estat === SelfAssessmentState.PAGAT;

  private hasError = (estat: SelfAssessmentState | undefined): boolean =>
    estat === SelfAssessmentState.PRESENTACIO_ERROR ||
    estat === SelfAssessmentState.PAGAMENT_ERROR ||
    estat === SelfAssessmentState.ERROR;

  private closeAndDestroy = (
    modalRef: NgbModalRef,
    estat: SelfAssessmentState | undefined = undefined,
  ): void => {
    //If has state and is not error
    modalRef.close(estat && !this.hasError(estat));
    this.onDestroy();
  };

  private onDestroy = (): void => {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  };
}
