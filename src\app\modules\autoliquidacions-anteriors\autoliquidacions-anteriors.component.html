<div class="app-autoliquidacions-anteriors mb-5">
  <div class="mb-4" *ngIf="selfassessmentsIds?.length">
    <mf-tributs-self-assessment-result
      [titleLabelPanel]="panelTitle | translate"
      [subtitleLabelPanel]="subtitle | translate"
      [columns]="columns"
      [actions]="actions"
      [selfassessments]="selfassessmentsIds"
      (payButtonEvent)="goToPagament($event)"
      [emitDownloadDocument]="true"
      [emitSendDocument]="true"
    >
    </mf-tributs-self-assessment-result>
  </div>

  <se-panel
    class="panel-0-padding"
    [title]="panelTitleOnGoing | translate"
    *ngIf="goingColumns?.length && goingRows?.length"
  >
    <p class="p-4">
      {{ subtitleOnGoing | translate }}
    </p>
    <se-table [columns]="goingColumns" [data]="goingRows"></se-table>
  </se-panel>

  <div class="d-flex justify-content-end align-items-end mt-4 mb-4">
    <se-button
      type="submit"
      [btnTheme]="'secondary'"
      (onClick)="newFormAction()"
    >
      {{
        'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_ANTERIORS.TABLE_COLUMNS_ONGOING.NEWFORM'
          | translate
      }}
    </se-button>
  </div>
</div>
