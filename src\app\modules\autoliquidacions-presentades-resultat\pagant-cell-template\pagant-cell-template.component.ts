/* eslint-disable @typescript-eslint/no-explicit-any */
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import {
  CellComponent,
  CellConfig,
  Column,
  FlattenedCell,
  FlattenedRow,
} from 'se-ui-components-mf-lib';

@Component({
  selector: 'app-pagant-cell-template',
  template: `
    <div class="d-flex align-items-center gap-2">
      <div class="spinner-container">
        <div class="lds-ring">
          <div class="ring-segment"></div>
          <div class="ring-segment"></div>
          <div class="ring-segment"></div>
          <div class="ring-segment"></div>
        </div>
      </div>
      <b>
        {{ 'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PRESENTADES.PAGANT' | translate }}
      </b>
    </div>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  styleUrls: ['./pagant-cell-template.component.scss'],
})
export class PagantCellComponent implements CellComponent {
  @Input() value: any;
  @Input() cell!: FlattenedCell;
  @Input() column!: Column;
  @Input() row!: FlattenedRow;
  @Input() cellConfig!: CellConfig;
}
