import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  Output,
} from '@angular/core';
import { Autoliquidacio } from '@core/models/autoliquidacio.model';
import { AutoliquidacionsEndpointsService } from '@core/services/autoliquidacions-endpoints.service';
import { NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { firstValueFrom, Subject, take, takeUntil } from 'rxjs';
import {
  Column,
  DeleteCallbackReturn,
  DownloadCallbackReturn,
  FlattenedCell,
  FlattenedRow,
  Nullable,
  Row,
  SeButton,
  SeDocumentsService,
  SeModal,
  SeModalOutputEvents,
  SeModalService,
} from 'se-ui-components-mf-lib';
import {
  ColumnPosition,
  RequestZipDec,
} from '../autoliquidacions-pendents-tramitar.model';
import { AutoPendentsTramitarService } from '../autoliquidacions-pendents-tramitar.service';
@Component({
  selector: 'app-panel-ben',
  templateUrl: './panel-ben.component.html',
  styleUrls: ['./panel-ben.component.scss'],
})
export class PanelBenComponent implements OnDestroy {
  BASE_TRANSLATE = 'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PENDENTS_TRAMITAR';

  _autoliquidacions: Autoliquidacio[] = [];

  get autoliquidacions(): Autoliquidacio[] {
    return this._autoliquidacions;
  }

  @Input() set autoliquidacions(value: Autoliquidacio[]) {
    this._autoliquidacions = value;
    this.getAutoliquidacioTable();
  }

  @Input() titlePanelAutoliquidacio = `${this.BASE_TRANSLATE}.PANEL_BE.TITLE`;
  @Input() showDescargarDec = false;
  @Input() rows: Row[] = [];
  @Input() columns: ColumnPosition[] = [];

  @Output() downloadDecZipOutput: EventEmitter<RequestZipDec[]> =
    new EventEmitter<RequestZipDec[]>();
  @Output() deleteAutoliquidacioEvent: EventEmitter<boolean> =
    new EventEmitter<boolean>();

  autoliquidacionsColumns: Column[] = [];
  autoliquidacionsRows: Row[] = [];
  importeTotal = 0;

  private readonly unsubscribe: Subject<void> = new Subject();

  get descargarDecButton(): SeButton | undefined {
    const button = {
      label:
        'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PENDENTS_TRAMITAR.PANEL_BE.BUTTON_DESCARREGAR',
    };

    return this.showDescargarDec ? button : undefined;
  }

  constructor(
    private readonly seModalService: SeModalService,
    private readonly autoliquidacionsEndpointsService: AutoliquidacionsEndpointsService,
    private readonly autoPendentsTramitarService: AutoPendentsTramitarService,
    private readonly seDocumentsService: SeDocumentsService,
  ) {}

  ngOnDestroy(): void {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }

  getAutoliquidacioTable(): void {
    const deleteCallback = async (
      _row: FlattenedRow,
      cell: FlattenedCell,
    ): Promise<DeleteCallbackReturn> => {
      const modalref = this.openModalEliminar(
        cell.rowData['idAutoliquidacio'].value,
      );
      const output: Nullable<boolean> = await firstValueFrom(modalref.closed);

      return {
        delete: !!output,
        apply: !!output,
      };
    };
    const downloadCallback = (
      _row: FlattenedRow,
      cell: FlattenedCell,
    ): DownloadCallbackReturn | Promise<DownloadCallbackReturn> => {
      return new Promise(() => {
        this.autoliquidacionsEndpointsService
          .downloadPdf(cell.rowData['idAutoliquidacio'].value)
          .pipe(takeUntil(this.unsubscribe))
          .subscribe((result) => {
            if (result.content) {
              this.seDocumentsService.openFile(
                result.content,
                'application/pdf',
                `${cell.rowData['numJustificant'].value}.pdf`,
              );
            }
          });
      });
    };

    this.autoliquidacionsColumns =
      this.autoPendentsTramitarService.getAutoliquidacionsColumns(
        deleteCallback,
        downloadCallback,
        this.columns,
      );
    this.setAutoliquidacioTable();
  }

  setAutoliquidacioTable(): void {
    this.autoliquidacionsRows =
      this.autoPendentsTramitarService.getAutoliquidacionsRows(
        this.autoliquidacions,
        this.rows,
      );
    this.importeTotal = this.autoliquidacions.reduce(
      (accumulator, autoliquidacio) => {
        return accumulator + autoliquidacio.calculs.totalIngressar;
      },
      0,
    );
  }

  openModalEliminar(idAutoliquidacio: string): NgbModalRef {
    const modalData: SeModal = {
      severity: 'error',
      title:
        'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PENDENTS_TRAMITAR.MODAL_ELIMINAR.TITLE',
      subtitle:
        'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PENDENTS_TRAMITAR.MODAL_ELIMINAR.BODY',
      closable: true,
      closableLabel:
        'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PENDENTS_TRAMITAR.BUTTONS.CONTINUE',
      secondaryButton: true,
      secondaryButtonLabel:
        'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PENDENTS_TRAMITAR.BUTTONS.CANCEL',
    };

    const modalRef: NgbModalRef = this.seModalService.openModal(modalData);
    modalRef.componentInstance.modalSecondaryButtonEvent
      .pipe(take(1))
      .subscribe(() => {
        modalRef.close();
      });

    modalRef.componentInstance.modalOutputEvent
      .pipe(take(1))
      .subscribe((acion: SeModalOutputEvents) => {
        const mainAction = SeModalOutputEvents.MAIN_ACTION === acion;
        mainAction &&
          this.autoliquidacionsEndpointsService
            .deleteAutoliquidacio(idAutoliquidacio)
            .subscribe(() => {
              this.deleteAutoliquidacioEvent.emit();
            });
        modalRef.close(mainAction);
      });
    return modalRef;
  }

  descargarDecZip(): void {
    const request: RequestZipDec[] = this.autoliquidacions.map((auto) => ({
      idAutoliquidacio: auto.idAutoliquidacio,
      numJustificant: auto.numJustificant,
    }));
    this.downloadDecZipOutput.emit(request);
  }
}
