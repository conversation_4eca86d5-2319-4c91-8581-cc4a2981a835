import {
  ErrorAutoliquidacio,
  Autoliquidacio,
} from '@core/models/autoliquidacio.model';
import { CellConfig } from 'se-ui-components-mf-lib';

export class TableAutoliquidacioData {
  justificant: { value: string } = { value: '' };
  taxpayer: { value: string } = { value: '' };
  date: { value: string } = { value: '' };
  type: { value: string } = { value: '' };
  total: { value: string; cellConfig?: CellConfig } = { value: '' };
  state: { value: string } = { value: '' };
  nif: { value: string } = { value: '' };
  idAutoliquidacio: { value: string } = { value: '' };
  idMfpt: { value: string } = { value: '' };
  errors: { value: ErrorAutoliquidacio[] } = { value: [] };

  constructor(response: Autoliquidacio) {
    this.justificant.value = response.numJustificant;
    this.taxpayer.value = `${response.subjectePassiu.nif} - ${response.subjectePassiu.nom}`;
    this.date.value = response.dataPresentacio!;
    this.type.value = response.tipus;
    this.total.value = response.quotaLiquida.toString();
    this.state.value = response.estat;
    this.nif.value = response.subjectePassiu.nif;
    this.idAutoliquidacio.value = response.idAutoliquidacio;
    this.idMfpt.value = response.idMfpt;
    this.errors.value = response.errors;
  }
}
