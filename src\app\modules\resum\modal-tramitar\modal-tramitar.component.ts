import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { Nullable, SeModal } from 'se-ui-components-mf-lib';

@Component({
  selector: 'app-modal-tramitar',
  templateUrl: './modal-tramitar.component.html',
})
export class ModalTramitarComponent implements OnInit {
  data: Nullable<SeModal>;

  @Output() modalOutput: EventEmitter<boolean> = new EventEmitter<boolean>();

  constructor(private readonly activeModal: NgbActiveModal) {}

  ngOnInit(): void {
    this.data = {
      severity: 'info',
      title:
        'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PENDENTS_TRAMITAR.MODAL_TRAMITAR.TITLE',
      closable: true,
      closableLabel:
        'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PENDENTS_TRAMITAR.BUTTONS.CONTINUE',
      secondaryButton: true,
      secondaryButtonLabel:
        'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PENDENTS_TRAMITAR.BUTTONS.CANCEL',
    };
  }

  closeModal(): void {
    this.activeModal.close();
  }

  tramitarFormulari(): void {
    this.activeModal.close();
    this.modalOutput.emit();
  }
}
