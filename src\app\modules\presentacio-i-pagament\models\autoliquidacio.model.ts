import { SelfAssessmentState } from '@core/models/self-assessment-status.model';
import { SubjectePassiu } from './subjecte-passiu.model';

export interface AutoliquidacioError {
  code: string;
  date: string; // Date
  description: string;
  technicalCode: string;
  technicalDescription: string;
  trackingId: string;
  stackTrace: string;
}

export interface Autoliquidacio {
  idAutoliquidacio: string;
  idDocuments: string[];
  numJustificant: string;
  quotaLiquida: number;
  subjectePassiu: SubjectePassiu;
  tipus: null;
  estat: SelfAssessmentState;
  dataPresentacio: null;
  dataIncompliment: null;
  dataFinTermini: null;
  dataTerminiPresentacio: null;
  errors?: AutoliquidacioError[];
}
