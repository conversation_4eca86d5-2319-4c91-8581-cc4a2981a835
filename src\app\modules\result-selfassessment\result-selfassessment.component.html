<ng-container *ngIf="selfAssessmentId">
  <se-alert
    *ngIf="alertMessage"
    [title]="alertMessage.title"
    [list]="alertMessage.list"
    [type]="alertMessage.type"
    [closeButton]="true"
  >
    <span *ngIf="alertDescription">{{ alertDescription | translate }}</span>
  </se-alert>

  <div *ngIf="dataLoaded">
    <mf-tributs-self-assessment-result
      *axLazyElement
      [selfassessments]="[selfAssessmentId]"
      [showDownloadMenu]="!!actions"
      [columns]="headers"
      [actions]="actions"
      (payButtonEvent)="goToPagament()"
    >
    </mf-tributs-self-assessment-result>
  </div>
</ng-container>
