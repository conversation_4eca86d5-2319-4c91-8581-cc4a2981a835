import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { YearPeriodComponent } from './year-period.component';
import { SeDropdownModule, SePanelModule} from 'se-ui-components-mf-lib';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';


@NgModule({
	declarations: [
		YearPeriodComponent,
	],
	imports: [
		CommonModule,
		TranslateModule.forChild(),
        FormsModule,
        ReactiveFormsModule,
        BrowserAnimationsModule,
        SePanelModule,
        SeDropdownModule 
	],
	bootstrap: [
		YearPeriodComponent
	]
})
export class YearPeriodModule { }
