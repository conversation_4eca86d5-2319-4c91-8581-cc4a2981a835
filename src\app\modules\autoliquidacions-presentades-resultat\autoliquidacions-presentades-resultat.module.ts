import { LazyElementsModule } from '@angular-extensions/elements';
import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { environment } from '@environments/environment';
import { NgIconsModule } from '@ng-icons/core';
import { matInfo } from '@ng-icons/material-icons/baseline';
import { TranslateModule } from '@ngx-translate/core';
import {
  SeAlertModule,
  SeButtonDropdownModule,
  SeButtonModule,
  SeModalModule,
  SePanelModule,
  SeTableModule,
  SeTooltipAccessibleModule,
  SpinnerComponent,
} from 'se-ui-components-mf-lib';
import { AutoliquidacionsActionsCellComponent } from './autoliquidacions-actions-cell-template/autoliquidacions-actions-cell-template.component';
import { AutoliquidacionsButtonsPanelComponent } from './autoliquidacions-buttons-panel/autoliquidacions-buttons-panel.component';
import { ModalSendDocumentationComponent } from './autoliquidacions-buttons-panel/modal-send-documentation/modal-send-documentation.component';
import { AutoPresResultatComponent } from './autoliquidacions-presentades-resultat.component';
import { InformacioPagamentComponent } from './informacio-pagament/informacio-pagament.component';
import { PagantCellComponent } from './pagant-cell-template/pagant-cell-template.component';

@NgModule({
  declarations: [
    AutoPresResultatComponent,
    InformacioPagamentComponent,
    AutoliquidacionsActionsCellComponent,
    AutoliquidacionsButtonsPanelComponent,
    ModalSendDocumentationComponent,
    PagantCellComponent,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [
    LazyElementsModule.forFeature({
      elementConfigs: [
        {
          tag: 'mf-documents-modal-send-documentation',
          url: environment.mfDocumentsURL,
          loadingComponent: SpinnerComponent,
          preload: false,
        },
      ],
    }),
    CommonModule,
    TranslateModule.forChild(),
    NgIconsModule.withIcons({ matInfo }),
    FormsModule,
    ReactiveFormsModule,
    BrowserAnimationsModule,
    SeAlertModule,
    SePanelModule,
    SeButtonModule,
    SeButtonDropdownModule,
    SeModalModule,
    SeTableModule,
    SeAlertModule,
    SeTooltipAccessibleModule,
  ],
})
export class AutoPresResultatModule {}
