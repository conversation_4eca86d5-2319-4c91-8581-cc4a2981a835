<se-panel
  [title]="
    'SE_TRIBUTS_MF.AUTOLIQUIDACIO_COMPLEMENTARIA.AUTOLIQUIDACIO_TITLE'
      | translate
  "
>
  <form [formGroup]="autoCompForm">
    <p
      [innerHTML]="
        'SE_TRIBUTS_MF.AUTOLIQUIDACIO_COMPLEMENTARIA.AUTOLIQUIDACIO_SUBTITLE'
          | translate
      "
    ></p>
    <div class="col-12 mt-2" *ngIf="perdidaFiscal">
      <P>{{
        'SE_TRIBUTS_MF.AUTOLIQUIDACIO_COMPLEMENTARIA.AUTOLIQUIDACIO_SELECTED'
          | translate
      }}</P>
      <div class="col-12 mb-2">
        <span class="text-sm mb-2">
          {{
            'SE_TRIBUTS_MF.AUTOLIQUIDACIO_COMPLEMENTARIA.FORM_FIELDS.LOSTBENEF'
              | translate
          }}
        </span>
        <div>
          <se-radio
            id="lostBenef_TRUE"
            [value]="true"
            (valueChanged)="setPerduaFiscalValidators(true)"
            [label]="
              'SE_TRIBUTS_MF.AUTOLIQUIDACIO_COMPLEMENTARIA.FORM_FIELDS.YES'
                | translate
            "
            formControlName="lostBenef"
          >
          </se-radio>
          <se-radio
            id="lostBenef_FALSE"
            [value]="false"
            (valueChanged)="setPerduaFiscalValidators(false)"
            [label]="
              'SE_TRIBUTS_MF.AUTOLIQUIDACIO_COMPLEMENTARIA.FORM_FIELDS.NO'
                | translate
            "
            formControlName="lostBenef"
          >
          </se-radio>
        </div>
        <div>
          <se-error-message
            [control]="getFormControl('lostBenef')"
          ></se-error-message>
        </div>
      </div>
      <div *ngIf="showBasicComplementary()" class="col-12 mt-2">
        <se-alert
          [title]="
            'SE_TRIBUTS_MF.AUTOLIQUIDACIO_COMPLEMENTARIA.FORM_FIELDS.ALERT'
              | translate
          "
          [type]="SeAlertType.INFO"
          [closeButton]="false"
        ></se-alert>
      </div>
    </div>
    <!-- indComplementaria -->
    <div *ngIf="showBasicComplementary()" class="row mt-2">
      <div
        class="d-flex justify-content-end my-2"
        *ngIf="urlHelpComplementaries"
      >
        <se-link [linkTheme]="'secondary'" [href]="urlHelpComplementaries">
          {{
            'SE_TRIBUTS_MF.AUTOLIQUIDACIO_COMPLEMENTARIA.FORM_FIELDS.COMPLEMENTARIA_URL_HELP'
              | translate
          }}
        </se-link>
      </div>
      <div class="col-sm-12 col-md-6 col-lg-4">
        <se-input
          id="numJustificant"
          [maxLength]="13"
          [label]="
            'SE_TRIBUTS_MF.AUTOLIQUIDACIO_COMPLEMENTARIA.FORM_FIELDS.JUSTIFICANT'
              | translate
          "
          formControlName="numJustificant"
        >
        </se-input>
      </div>
      <!-- Date -->
      <div class="col-sm-12 col-md-6 col-lg-4">
        <se-input
          id="quotaLiquida"
          [label]="
            'SE_TRIBUTS_MF.AUTOLIQUIDACIO_COMPLEMENTARIA.FORM_FIELDS.QUOTA_RESULT'
              | translate
          "
          [tooltip]="true"
          [tooltipText]="tooltipQuotaLiquida"
          type="text"
          formControlName="quotaLiquida"
          [currencyMode]="true"
          currencySymbol=""
        >
        </se-input>
        <ng-template #tooltipQuotaLiquida>
          <div
            [innerHTML]="
              'SE_TRIBUTS_MF.AUTOLIQUIDACIO_COMPLEMENTARIA.FORM_FIELDS.QUOTA_RESULT_TOOLTIP'
                | translate
            "
          ></div>
        </ng-template>
      </div>
      <!-- Date -->
      <div class="col-sm-12 col-md-6 col-lg-4">
        <se-datepicker
          id="dataPresentacio"
          [label]="
            'SE_TRIBUTS_MF.AUTOLIQUIDACIO_COMPLEMENTARIA.FORM_FIELDS.PRESENTATION_DATE'
              | translate
          "
          [placeholder]="'dd/mm/aaaa'"
          [showIcon]="true"
          formControlName="dataPresentacio"
        >
        </se-datepicker>
      </div>
    </div>
    <!-- lostBenef -->
    <div
      *ngIf="getFormControl('lostBenef')?.value === true"
      class="row align-items-end mt-2"
    >
      <!-- Date incumpliment -->
      <div class="col-sm-12 col-md-6 col-lg-4">
        <se-datepicker
          id="dataTerminiPresentacio"
          [label]="
            'SE_TRIBUTS_MF.AUTOLIQUIDACIO_COMPLEMENTARIA.FORM_FIELDS.TERMINI_PRESENTACIO_DATE'
              | translate
          "
          [placeholder]="'dd/mm/aaaa'"
          [showIcon]="true"
          formControlName="dataTerminiPresentacio"
        >
        </se-datepicker>
      </div>
      <!-- Date incumpliment -->
      <div class="col-sm-12 col-md-6 col-lg-4">
        <se-datepicker
          id="dataIncompliment"
          [label]="
            'SE_TRIBUTS_MF.AUTOLIQUIDACIO_COMPLEMENTARIA.FORM_FIELDS.INCUMPLIMENT_DATE'
              | translate
          "
          [placeholder]="'dd/mm/aaaa'"
          [tooltip]="true"
          [showIcon]="true"
          (dateSelectedEvent)="dataIncomplimentChange()"
          [tooltipText]="
            'SE_TRIBUTS_MF.AUTOLIQUIDACIO_COMPLEMENTARIA.FORM_FIELDS.INCUMPLIMENT_DATE_TOOLTIP'
              | translate
          "
          formControlName="dataIncompliment"
        >
        </se-datepicker>
      </div>
      <!-- Date termini -->
      <div class="col-sm-12 col-md-6 col-lg-4">
        <se-datepicker
          id="dataFinTermini"
          [label]="
            'SE_TRIBUTS_MF.AUTOLIQUIDACIO_COMPLEMENTARIA.FORM_FIELDS.TERMINI_DATE_AUTOLIQUIDACIO'
              | translate
          "
          [placeholder]="'dd/mm/aaaa'"
          [tooltip]="true"
          [showIcon]="true"
          [tooltipText]="
            'SE_TRIBUTS_MF.AUTOLIQUIDACIO_COMPLEMENTARIA.FORM_FIELDS.TERMINI_DATE_AUTOLIQUIDACIO_TOOLTIP'
              | translate
          "
          formControlName="dataFinTermini"
        >
        </se-datepicker>
      </div>
    </div>
  </form>
</se-panel>
<div class="d-flex flex-row justify-content-between mt-4 mb-4">
  <se-button type="button" btnTheme="secondary" (onClick)="navigateBackwards()">
    {{
      'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PENDENTS_TRAMITAR.BUTTONS.PREVIOUS'
        | translate
    }}
  </se-button>

  <se-button type="button" btnTheme="primary" (onClick)="checkAutoComp()">
    {{
      'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PENDENTS_TRAMITAR.BUTTONS.PRESENTAR'
        | translate
    }}
  </se-button>
</div>
