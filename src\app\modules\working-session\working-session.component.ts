import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';
import {
  Column,
  DateUtilsService,
  FlattenedCell,
  Row,
  SeAuthService,
} from 'se-ui-components-mf-lib';
import { MenuItem } from 'primeng/api';

import {
  WorkingSession,
  WorkingSessionKeys,
} from './model/working-session.model';
import { WorkingSessionRequestService } from './working-session-endpoints.service';
import { TaxablePerson } from '../participants/models';
import { SelfAssessment } from './model/self-assessment.model';
import { WorkingSessionService } from './working-session.service';
import { Constant } from 'src/app/core/models/constants.enum';
import { DatePipe } from '@angular/common';
import { HeaderInfoService, StoreService } from '@core/services';
import { AppRoutes } from '@core/models/app-routes.enum';

@Component({
  selector: 'app-working-session',
  templateUrl: 'working-session.component.html',
  providers: [DatePipe],
})
export class WorkingSessionComponent implements OnInit, OnDestroy {
  protected tax = Constant.NAME;
  protected workingSessions: WorkingSession[] = [];
  protected workingSessionHeaders: Column[] =
    this.workingSessionService.getWorkingSessionHeaders();
  protected workingSessionsTableData: Row[] = [];

  protected selfAssessments: SelfAssessment[] = [];
  protected selfAssessmentActions: MenuItem[] | undefined;
  protected selfAssessmentsSelected: Row[] = [];

  private destroyed$: Subject<void> = new Subject<void>();

  constructor(
    private router: Router,
    private store: StoreService,
    private workingSessionRequestService: WorkingSessionRequestService,
    private workingSessionService: WorkingSessionService,
    private authService: SeAuthService,
    private header: HeaderInfoService,
    private dateUtil: DateUtilsService,
    private datePipe: DatePipe,
  ) {
    this.header.resetCompleteSubheader();
  }

  ngOnInit(): void {
    this.setWorkingSessions();
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  protected updateWorkingSessionTable(): void {
    this.setWorkingSessions();
  }

  protected onEditWorkingSession(event: Event): void {
    const customEvent = event as CustomEvent<FlattenedCell>;
    if (customEvent.detail) {
      const selectedWorkingSession = this.workingSessions.find(
        ({ idTramit }) =>
          idTramit === customEvent.detail.rowData.idTramit.value,
      );
      this.store.hasAppearedWorkingSession = null;
      this.setParticipantsData(selectedWorkingSession?.idTramit);
    }
  }

  submit(): void {
    this.store.selectedTaxpayer = null;
    this.store.idTramit = null;
    this.store.hasAppearedWorkingSession = true;
    this.onNavigateToParticipants();
  }

  protected onNavigateTogoPayment(event: Event): void {
    const customEvent = event as CustomEvent;
    if (customEvent?.detail) {
      this.store.selfAssessmentId = (
        customEvent.detail as FlattenedCell
      ).rowData.idAutoliquidacio.value;
    }
    this.store.amountToPay = customEvent?.detail?.rowData?.quotaLiquida?.value;
    this.router.navigate([AppRoutes.PAGAMENT]);
  }

  protected onSetSelfAssessment(event: Event): void {
    const customEvent = event as CustomEvent<SelfAssessment[] | undefined>;
    if (customEvent?.detail) {
      this.selfAssessments = customEvent.detail;
      this.setSelfAssessmentActions();
    }
  }

  protected onSetSelfAssessmentSelected(event: Event): void {
    const customEvent = event as CustomEvent<Row[] | undefined>;
    if (customEvent?.detail) {
      this.selfAssessmentsSelected = customEvent.detail;
    }
  }

  private onNavigateToParticipants(): void {
    this.router.navigate([AppRoutes.PARTICIPANTS]);
  }

  private setSelfAssessmentActions = async (): Promise<void> => {
    this.selfAssessmentActions =
      await this.workingSessionService.getSelfAssessmentActions(
        this.selfAssessmentsSelected.length > 1,
      );
  };

  private setParticipantsData(procedureId: string | undefined): void {
    if (procedureId) {
      this.workingSessionRequestService
        .getParticipantData(procedureId)
        .pipe(takeUntil(this.destroyed$))
        .subscribe((response) => {
          if (response?.content) {
            const { subjectesPassius } = response.content;
            this.setTaxPayerInStore(subjectesPassius, procedureId);
          }
          this.onNavigateToParticipants();
        });
    }
  }

  private setTaxPayerInStore(
    taxablePerson: TaxablePerson,
    procedureId: string,
  ): void {
    this.store.selectedTaxpayer = {
      nif: taxablePerson.idDocument,
      name: taxablePerson.nom,
      haveAcceptedDeclarationOfResponsibility:
        taxablePerson.declaracioResponsable,
      isPresenter:
        this.authService.getSessionStorageUser().nif ===
        taxablePerson.idDocument,
      isScoringValid: true,
    };
    this.store.idTramit = procedureId;
  }

  private setWorkingSessions(): void {
    this.workingSessionRequestService
      .getWorkingSessions(this.tax)
      .pipe(takeUntil(this.destroyed$))
      .subscribe((response) => {
        if (response?.content) {
          const data = response.content.sessionsTreball;
          if (data?.length) {
            const sortedList = data.sort((a, b) =>
              this.dateUtil.sortDates(
                a?.dataSession ?? '',
                b?.dataSession ?? '',
              ),
            );
            this.workingSessions = sortedList;
            this.workingSessionsTableData =
              this.getWorkingSessionDataTableParsed(sortedList);
            this.store.hasAppearedWorkingSession = true;
          } else {
            // si no hay sesiones de trabajo, navega a la sgte pantalla
            this.store.selectedTaxpayer = null;
            this.store.hasAppearedWorkingSession = false;
            this.onNavigateToParticipants();
          }
        }
      });
  }

  private getWorkingSessionDataTableParsed(
    workingSessions: WorkingSession[],
  ): Row[] {
    return workingSessions.map((wSession) => {
      let data = {};
      Object.entries(wSession).forEach(([key, value]) => {
        if (key === WorkingSessionKeys.Declarant) {
          value = `${wSession.nifSubPas} - ${value}`;
        }
        data = { ...data, [key]: { value } };
      });

      return {
        data: {
          ...data,
          actions: {
            value: '',
          },
        },
      };
    });
  }
}
