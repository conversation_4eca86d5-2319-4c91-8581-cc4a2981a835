import { Column, Nullable, SeHttpResponse } from 'se-ui-components-mf-lib';

import { AutoliquidacioComplementaria } from '../../complementary/models';
import { ResultActionsCellComponent } from '../../self-assessment-result/actions-cell-template/actions-cell-template.component';

export enum SelfAssessmentKeys {
  VoucherNumber = 'numJustificant',
  Declarant = 'subjectePassiu',
  PresentationDate = 'dataPresentacio',
  TotalAmount = 'quotaLiquida',
  State = 'estat',
  Actions = 'actions',
}

export const selfAssessmentHeaders: Column[] = [
  {
    header:
      'SE_TRIBUTS_MF.WORKING_SESSIONS.SELF_ASSESSMENTS.TABLE_HEADERS.VOUCHER_NUMBER',
    key: SelfAssessmentKeys.VoucherNumber,
    resizable: true,
  },
  {
    header:
      'SE_TRIBUTS_MF.WORKING_SESSIONS.SELF_ASSESSMENTS.TABLE_HEADERS.DECLARANT',
    key: SelfAssessmentKeys.Declarant,
    resizable: true,
  },
  {
    header:
      'SE_TRIBUTS_MF.WORKING_SESSIONS.SELF_ASSESSMENTS.TABLE_HEADERS.PRESENTATION_DATE',
    key: SelfAssessmentKeys.PresentationDate,
    cellComponentName: 'dateCellComponent',
    cellConfig: { dateFormat: 'dd/MM/yyyy' },
    resizable: true,
  },
  {
    header:
      'SE_TRIBUTS_MF.WORKING_SESSIONS.SELF_ASSESSMENTS.TABLE_HEADERS.TOTAL_AMOUNT',
    key: SelfAssessmentKeys.TotalAmount,
    resizable: true,
    cellComponentName: 'currencyCellComponent',
  },
  {
    header:
      'SE_TRIBUTS_MF.WORKING_SESSIONS.SELF_ASSESSMENTS.TABLE_HEADERS.STATE',
    key: SelfAssessmentKeys.State,
    resizable: true,
  },
  {
    header:
      'SE_TRIBUTS_MF.WORKING_SESSIONS.SELF_ASSESSMENTS.TABLE_HEADERS.ACTIONS',
    key: SelfAssessmentKeys.Actions,
    size: 25,
    resizable: false,
    cellComponent: ResultActionsCellComponent,
  },
];

export interface SelfAssessmentError {
  code: string;
  date: string;
  description: string;
  technicalCode: string;
  technicalDescription: string;
  trackingId: string;
  stackTrace: string;
}

export interface SelfAssessment extends AutoliquidacioComplementaria {
  idMfpt: string;
  errors: SelfAssessmentError[];
}

export interface SelfAssessmentResponse extends SeHttpResponse {
  content?: SelfAssessment[];
}

export interface WorkingSessionTableMessage {
  descriptionPanel1?: Nullable<string>;
  descriptionPanel2?: Nullable<string>;
}
