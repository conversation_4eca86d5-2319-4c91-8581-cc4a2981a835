export enum SelfAssessmentEstat {
  GENERAT = 'GENERAT',
  ESBORRANY = 'ESBORRANY',
  ESBORRANY_ERROR = 'ESBORRANY_ERROR',
  ESBORRANY_VALIDAT = 'ESBORRANY_VALIDAT',
  ESBORRANY_AGRUPAT = 'ESBORRANY_AGRUPAT',
  ESBORRANY_AGRUPANT = 'ESBORRANY_AGRUPANT',
  ESBORRANY_AGRUPANT_ERROR = 'ESBORRANY_AGRUPANT_ERROR',
  NO_PRESENTAT = 'NO_PRESENTAT',
  PENDENT_PRESENTACIO = 'PENDENT_PRESENTACIO',
  PRESENTANT = 'PRESENTANT',
  PRESENTACIO_ERROR = 'PRESENTACIO_ERROR',
  PRESENTAT = 'PRESENTAT',
  PAGAT = 'PAGAT',
  PAGANT = 'PAGANT',
  PENDENT_PAGAMENT = 'PENDENT_PAGAMENT',
  PAGAMENT_ERROR = 'PAGAMENT_ERROR',
  NOTIFICACIO_ERROR = 'NOTIFICACIO_ERROR',
  ERROR = 'ERROR',
}

export enum EstatOrder {
  GENERAT,
  ESBORRANY,
  ESBORRANY_ERROR,
  ESBORRANY_VALIDANT,
  ESBORRANY_VALIDAT,

  ESBORRANY_AGRUPANT_ERROR,
  ESBORRANY_AGRUPANT,
  ESBORRANY_AGRUPAT,

  NO_PRESENTAT,
  PENDENT_PRESENTACIO,
  PRESENTACIO_ERROR,
  PRESENTANT,
  PRESENTAT,

  PENDENT_PAGAMENT,
  PAGANT,
  PAGAMENT_ERROR,
  NOTIFICACIO_ERROR,
  PAGAT,
  ERROR,
}
