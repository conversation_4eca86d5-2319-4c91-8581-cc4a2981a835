import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import {
  SeButtonModule,
  SeDatepickerModule,
  SePanelModule,
} from 'se-ui-components-mf-lib';
import { ModifySaveButtonsModule } from '../modify-save-buttons/modify-save-buttons.module';
import { DataFiTerminiComponent } from './data-fi-termini.component';

@NgModule({
  declarations: [DataFiTerminiComponent],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule,
    SePanelModule,
    SeButtonModule,
    SeDatepickerModule,
    ModifySaveButtonsModule,
  ],
  exports: [DataFiTerminiComponent],
})
export class DataFiTerminiComponentModule {}
