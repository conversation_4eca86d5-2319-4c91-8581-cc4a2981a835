# SeGasosMf

This project was generated with [Angular CLI](https://github.com/angular/angular-cli) version 16.1.4.

It serves as a template for creating microfrontends using Angular 16 and the component library se-ui-components-mf-lib. It comes with a basic structure that can be easily extended to fit your specific needs.

## Getting Started

After cloning the repository, perform the following steps to tailor the project to your requirements:

### Rename Project References

1. **Global Replace**: Execute a global search and replace in the project to change all occurrences of `se-gasos-mf` to your new microfrontend's name.

2. **Webpack Configuration**: In the `extra-webpack.config.js` file, update the `library` and `uniqueName` keys to reflect your new microfrontend's name.

3. **Angular Module & Build Script**: Modify the string `se-gasos` in both `app.module.ts` and `elements-build.js` to match your new microfrontend's name.

4. **SIC**: Modify the `description` in uppercase with the project name.

5. **.npmrc**: Modify the `_auth` value with the current key.

6. **index.html**: Modify the `<title></title>` value with the name of the project.

By following these steps, you can quickly and effectively adapt this template for your own microfrontend projects.

## Development server

Run `ng serve` for a dev server. Navigate to `http://localhost:4200/`. The application will automatically reload if you change any of the source files.

## Code scaffolding

Run `ng generate component component-name` to generate a new component. You can also use `ng generate directive|pipe|service|class|guard|interface|enum|module`.

## Build

Run `build:elements` to build the project. The build artifacts will be stored in the `dist/` directory.

## Running unit tests

Run `ng test` to execute the unit tests via [Karma](https://karma-runner.github.io).

Run `test:coverage` to generate the coverage folder. Go to ./coverage/{{project-name}} and execute the command `http-server -c-1` to know what lines need a test

## Running end-to-end tests

Run `ng e2e` to execute the end-to-end tests via a platform of your choice. To use this command, you need to first add a package that implements end-to-end testing capabilities.

## Further help

To get more help on the Angular CLI use `ng help` or go check out the [Angular CLI Overview and Command Reference](https://angular.io/cli) page.
