import { Component, EventEmitter, Input, Output } from '@angular/core';

import { TaxFormFieldChange } from '../tax-form-field';
import {
  TaxFormFieldProps,
  TaxFormGroupChange,
  TaxFormGroupHeaderLabels,
} from './models';

@Component({
  selector: 'app-tax-form-group',
  template: `
    <p
      *ngIf="title"
      [class]="'tax-form-group__title tax-form-group__title--' + titleSize"
    >
      {{ title }}
    </p>

    <header class="row fields-header">
      <!--
        Se necesitan dos columnas vacías para que las cabeceras y los campos
        estén alienados verticalmente.
      -->
      <span *ngIf="showLabel" class="col-3 fields-header__label-header">
        <!-- Intencionadamente vacío -->
      </span>
      <span class="col fields-header__value-header">
        <!-- Intencionadamente vacío -->
      </span>
      <span *ngIf="showDates" class="col fields-header__dates-header">
        {{ headerLabels.datesHeaderLabel }}
      </span>
      <span class="col fields-header__price-per-unit-header">
        {{ headerLabels.pricePerUnitHeaderLabel }}
      </span>
      <span class="col fields-header__total-price-header">
        {{ headerLabels.totalPriceHeaderLabel }}
      </span>
    </header>

    <app-tax-form-field
      *ngFor="let field of fields; trackBy: trackById"
      [id]="field.id"
      [label]="field.label!"
      [units]="field.units"
      [taxes]="field.taxes"
      [headerLabels]="headerLabels"
      (onChange)="handleChange($event)"
    ></app-tax-form-field>
  `,
  styleUrls: ['./tax-form-group.component.scss'],
})
export class TaxFormGroupComponent {
  @Input() title = '';

  @Input() titleSize!: 'small' | 'large';

  @Input() set fields(newFields: TaxFormFieldProps[]) {
    this._fields = newFields;
    this.showDates = newFields.some(({ taxes }) => taxes.length > 1);
    this.showLabel = newFields.some(({ label }) => !!label);
  }

  @Input({ required: true }) headerLabels: TaxFormGroupHeaderLabels = {
    datesHeaderLabel: '',
    pricePerUnitHeaderLabel: '',
    totalPriceHeaderLabel: '',
  };

  @Output() onChange = new EventEmitter<TaxFormGroupChange>();

  get fields() {
    return this._fields;
  }

  protected showDates = false;

  protected showLabel = false;

  private _fields: TaxFormFieldProps[] = [];

  constructor() {
    console.log('WebComponent: Tributs > TaxFormGroupComponent > constructor');
  }

  protected trackById(_index: number, item: TaxFormFieldProps): string {
    return item.id;
  }

  protected handleChange({ id, taxes }: TaxFormFieldChange): void {
    this._fields = this.fields.map((field) =>
      field.id === id ? { ...field, taxes } : field
    );

    this.onChange.emit(
      this.fields.map((field) => ({
        id: field.id,
        taxes: field.taxes.map(({ value, pricePerUnit, totalPrice }) => ({
          value,
          pricePerUnit,
          totalPrice: totalPrice ?? 0,
        })),
      }))
    );
  }
}
