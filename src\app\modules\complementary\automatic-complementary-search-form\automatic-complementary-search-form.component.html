<form class="row" [formGroup]="searchForm">

  <ng-container *ngIf="isOptional || searchComplementary">
    <se-switch [id]="'searchComplementary'"
      [label]="'SE_TRIBUTS_MF.COMPLEMENTARY.OPTIONAL.SWITCH' | translate"
      [labelPosition]="'right'" formControlName="searchComplementary"
      (onToggle)="emitSearchComplementaryChange($event)">
    </se-switch>
  </ng-container>

  <ng-container *ngIf="searchComplementary || !isOptional">

    <div class="col-12">
      <p *ngIf="!isOptional" class="fs-5 fw-semibold">{{"SE_TRIBUTS_MF.COMPLEMENTARY.OPTIONAL.TITLE" | translate}}
        <ng-icon class="tooltip-icon" name="matInfo" [pTooltipAccessible]="
        'SE_TRIBUTS_MF.COMPLEMENTARY.OPTIONAL.TOOLTIP_DESCRIPTION'
          | translate
      "></ng-icon>
      </p>
      <hr>
    </div>
    <div class="col-12">
      <p class="fw-semibold">{{"SE_TRIBUTS_MF.COMPLEMENTARY.OPTIONAL.DESCRIPTION" | translate}}</p>
    </div>
    <se-input class="col-12 col-lg"
      [label]="'SE_TRIBUTS_MF.COMPLEMENTARY.RECEIPT_ID_LABEL' | translate"
      [formControlName]="'receiptId'" />

    <se-datepicker class="col-12 col-lg"
      [label]="'SE_TRIBUTS_MF.COMPLEMENTARY.FILING_DATE_LABEL' | translate"
      [formControlName]="'filingDate'" [placeholder]="'dd/mm/aaaa'"
      [maxDate]="currentDate" />

    <se-input class="col-12 col-lg"
      [label]="'SE_TRIBUTS_MF.COMPLEMENTARY.AMOUNT_LABEL' | translate"
      [formControlName]="'amount'" [currencyMode]="true" />

    <div
      class="col-12 col-lg-auto d-sm-flex justify-content-sm-end d-lg-block automatic-complementary-search-form__submit-button-container">
      <se-button class="mt-lg-4" [disabled]="isSearchButtonDisabled"
        (onClick)="searchSelfassessment()">
        {{ "SE_TRIBUTS_MF.COMPLEMENTARY.COMPLEMENTARY_BUTTON_LABEL" | translate
        }}
      </se-button>
    </div>
  </ng-container>
</form>

<se-alert *ngIf="isErrorAlertVisible" [type]="'error'" [closeButton]="false"
  [title]="'SE_TRIBUTS_MF.COMPLEMENTARY.SEARCH_FORM_ERROR' | translate" />