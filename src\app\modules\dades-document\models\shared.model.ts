import { SeHeaderInfoItem, SeHttpResponse } from 'se-ui-components-mf-lib';

export enum DocumentTypes {
  NOTARIAL = 'NT',
  ADMINISTRATIU = 'AD',
  JUDICIAL = 'JD',
  PRIVAT = 'PR',
}

export interface BasicList {
  id: string;
  label: string;
}

export interface BasicListResponse extends SeHttpResponse {
  content: BasicList[];
}

export const SPAIN_CODE = '101';

export interface AgentInfoHeader extends Array<SeHeaderInfoItem[]> {}

export interface Ubicacio {
  pais: string;
  provincia: string;
  municipi: string;
}

export interface OperacioDraft {
  tipusOperacio: string;
  codiTipusOperacio: string;
  indPresentaPerduaBeneficioFiscal: boolean;
  tipusDretExtingit: string;
  causaDExtincio: string;
  dataExtincioDret: string;
  indObjectesFabricatsAmdMetalls: boolean;
  dataUltimDiaDeclarades: string;
  indIvaArrendaments: boolean;
  tipusArrendament: string;
  indCompradorIvaPagat: boolean;
  indVenedorActuaProfessional: boolean;
  indDocumentExempt: boolean;
  indComplementaria: boolean;
  indSenseTransmitent: boolean;
  complementaria: Complementaria;
  autoritat: string;
  referencia: string;
  dataDocument: string;
  descripcioOperacio: string;
  protocol: string;
  protocolBis: string;
  notari: Notary | undefined;
}

export interface Notary {
  nif: string;
  nom: string;
  idPers: string;
  cuv: string;
  oficina: string;
  indEstranger: boolean;
  uuid: string;
  indAnterior1970: boolean;
  tipusDocument: string;
}

export interface Complementaria {
  numJustificant: string;
  quotaLiquida?: number;
  dataPresentacio: string;
  dataTerminiPresentacio: string;
  dataIncompliment: string;
  dataFinTermini: string;
}

export enum EstatEsborrany {
  ESBORRANY_ERROR = 'ESBORRANY_ERROR',
  ESBORRANY_VALIDAT = 'ESBORRANY_VALIDAT',
  ESBORRANY_AGRUPAT = 'ESBORRANY_AGRUPAT',

  GENERAT = 'GENERAT',
  ESBORRANY = 'ESBORRANY',
  NO_PRESENTAT = 'NO_PRESENTAT',
  PENDENT_PRESENTACIO = 'PENDENT_PRESENTACIO',
  PRESENTANT = 'PRESENTANT',
  PRESENTACIO_ERROR = 'PRESENTACIO_ERROR',
  PRESENTAT = 'PRESENTAT',
  PAGAT = 'PAGAT',
  PAGANT = 'PAGANT',
  PENDENT_PAGAMENT = 'PENDENT_PAGAMENT',
  PAGAMENT_ERROR = 'PAGAMENT_ERROR',
  ERROR = 'ERROR',
}
