import { Injectable } from '@angular/core';
import { Observable, ReplaySubject, Subject } from 'rxjs';
import {
  SeHttpRequest,
  SeHttpResponse,
  SeHttpService,
} from 'se-ui-components-mf-lib';
import { environment } from 'src/environments/environment';
import {
  CalculInfo,
  Interest,
  MODEL,
  RequestPeriodInfo,
  RequestSurcharge,
  ResponseInterestCalculator,
  ResponsePeriodInfo,
  ResponseSurcharge,
} from './calculations.model';

@Injectable({
  providedIn: 'root',
})
export class CalculationsEndpointService {
  private periodDataSubject = new ReplaySubject<ResponsePeriodInfo>(1);
  periodObservable$ = this.periodDataSubject.asObservable();

  private interestDataSubject = new Subject<ResponseInterestCalculator>();
  interestObservable$ = this.interestDataSubject.asObservable();

  private surchargeDataSubject = new ReplaySubject<ResponseSurcharge>(1);
  surchargeObservable$ = this.surchargeDataSubject.asObservable();

  constructor(private httpService: SeHttpService) {}

  emitPeriodData(response: ResponsePeriodInfo): void {
    this.periodDataSubject.next(response);
  }

  emitInterestData(response: ResponseInterestCalculator): void {
    this.interestDataSubject.next(response);
  }

  emitSurchargeData(response: ResponseSurcharge): void {
    this.surchargeDataSubject.next(response);
  }

  voluntaryPeriodEnd(voluntaryEndDate: string): boolean {
    const currentDate = new Date();
    const voluntary = new Date(voluntaryEndDate);
    return currentDate > voluntary;
  }

  getLiquidateAmount(model: string, calculationInfo: CalculInfo): number {
    return model === MODEL.CINCUENTA
      ? (calculationInfo.quotaLiquidades ?? 0) +
          (calculationInfo.quotaLiquidadesTrimestri ?? 0)
      : (calculationInfo.quotaLiquidades ?? 0);
  }

  getSurcharge(body: RequestSurcharge): Observable<SeHttpResponse> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlTributs,
      url: `/recarrec`,
      method: 'post',
      body: body,
      clearExceptions: true,
    };
    return this.httpService.get(httpRequest);
  }

  getInterestCalculator(body: Interest): Observable<SeHttpResponse> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlTributs,
      url: `/interessos/calculadora`,
      method: 'post',
      body: body,
      clearExceptions: true,
    };
    return this.httpService.get(httpRequest);
  }

  getPeriodData(request: RequestPeriodInfo): Observable<SeHttpResponse> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlTributs,
      url: `/${request.impost}/exercici/${request.year}/periode/${request.period}/model/${request.model}`,
      method: 'get',
      clearExceptions: true,
    };
    return this.httpService.get(httpRequest);
  }
}
