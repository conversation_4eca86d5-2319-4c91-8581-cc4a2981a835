import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Inject,
  Input,
  LOCALE_ID,
  OnDestroy,
  Output,
} from '@angular/core';
import {
  finalize,
  firstValueFrom,
  map,
  merge,
  Observable,
  Subject,
  take,
  takeUntil,
} from 'rxjs';
import {
  Cell,
  Column,
  DeleteCallbackReturn,
  EditCallbackReturn,
  FlattenedCell,
  FlattenedRow,
  Row,
  SeAuthService,
  SeDocumentsService,
  SeModal,
  SeModalOutputEvents,
  SeModalService,
  SeUser,
  type Nullable,
} from 'se-ui-components-mf-lib';
import { TranslateService } from '@ngx-translate/core';
import { formatDate } from '@angular/common';
import { MenuItem } from 'primeng/api';

import {
  SelfAssessment,
  SelfAssessmentError,
  selfAssessmentHeaders,
  SelfAssessmentKeys,
  WorkingSessionTableMessage,
} from './model/self-assessment.model';
import { WorkingSessionsEndpointsService } from './working-sessions-endpoint.service';
import { SelfAssessmentEstat, TAX_MODEL_NUMBER } from '@core/models';
import { SelfAssessmentResultEndpointService } from '../self-assessment-result/self-assessment-result-endpoint.service';
import { SubjectePassiu } from '../complementary/models';

@Component({
  selector: 'app-working-sessions',
  templateUrl: './working-sessions.component.html',
})
export class WorkingSessionsComponent implements OnDestroy {
  @Input({ required: true }) set taxName(tax: string) {
    if (tax) {
      this.model = TAX_MODEL_NUMBER[tax as keyof typeof TAX_MODEL_NUMBER];
      this.defaultFilename = `Docs_${tax}.zip`;
      this.setSelfAssessment(tax);
    }
  }

  @Input() set selfAssessmentActions(actions: MenuItem[] | undefined) {
    this.actions = actions;
    if (actions && !this.selfAssessmentTableData.length) {
      this.selfAssessmentTableData = this.getSelfAssessmentDataTableParsed(
        this.selfAssessments,
        actions,
      );
    }
  }

  @Input() set workingSessionHeaders(headers: Column[]) {
    if (headers.length) {
      headers[headers.length - 1].cellComponentName = 'actionsCellComponent';
      this.workingSessionColumns = headers;
      this.setActionsInTheWorkingSessionTable();
    }
  }

  @Input() workingSessionDefaultNumberOfItems = 5;
  @Input() workingSessionsTableData: Row[] = [];
  @Input() workingSesionTableMessage: WorkingSessionTableMessage | undefined;

  _workingSessionsPanelDescription = '';

  @Input() set workingSessionsPanelDescription(description: Nullable<string>) {
    this._workingSessionsPanelDescription = description ?? '';
  }

  get workingSessionsPanelDescription(): string {
    return (
      this._workingSessionsPanelDescription ||
      this.translateService.instant(
        'SE_TRIBUTS_MF.WORKING_SESSIONS.W_S.DESCRIPTION',
      )
    );
  }

  @Output() handleEditWorkingSession: EventEmitter<FlattenedCell | undefined> =
    new EventEmitter();

  @Output() handleUpdateWorkingSessionTable: EventEmitter<void> =
    new EventEmitter();

  @Output() handleSelfAssessmentActionButton: EventEmitter<Cell> =
    new EventEmitter();

  @Output() handleSetSelfAssessment: EventEmitter<SelfAssessment[]> =
    new EventEmitter();

  @Output() handleSetSelfAssessmentSelected: EventEmitter<Row[]> =
    new EventEmitter();

  protected workingSessionColumns: Column[] = [];
  protected workingSessionRowsPerPageOptions = [5, 10, 20, 50];

  protected selfAssessments: SelfAssessment[] = [];
  protected actions: MenuItem[] | undefined;
  protected selfAssessmentsSelected: Row[] = [];
  protected selfAssessmentHeaders: Column[] = selfAssessmentHeaders;
  protected selfAssessmentTableData: Row[] = [];

  protected defaultFilename = '';
  protected idDocuments: string[] = [];

  protected model: number | undefined;
  private destroyed$ = new Subject<void>();

  constructor(
    private modalService: SeModalService,
    private translateService: TranslateService,
    private workingSessionService: WorkingSessionsEndpointsService,
    private selfAssessmentService: SelfAssessmentResultEndpointService,
    private seDocumentsService: SeDocumentsService,
    private cdRef: ChangeDetectorRef,
    private authService: SeAuthService,
    private seModalService: SeModalService,
    @Inject(LOCALE_ID) private locale: string,
  ) {
    console.log(
      'Webcomponent: SE Tributs > WorkingSessionsComponent > constructor',
    );
  }

  get fileName(): string {
    if (this.selfAssessmentsSelected.length) {
      return this.selfAssessmentsSelected.length > 1
        ? this.defaultFilename
        : String(this.selfAssessmentsSelected[0].data['idDocuments'].value[0]);
    } else {
      return '';
    }
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  protected onSelfAssessmentSelectionChange(list: Row[]): void {
    this.selfAssessmentsSelected = list;
    this.handleSetSelfAssessmentSelected.emit(this.selfAssessmentsSelected);
    this.idDocuments = this.getSelfAssessmentIdsForDocuments(list);
    this.updateSelfAssessmentActions();
  }

  protected onDownloadDocumentsZip(selfAssessmentsSelected: Row[]): void {
    if (selfAssessmentsSelected.length) {
      const selfAssessmentIds = this.getSelfAssessmentIdsForDocuments(
        selfAssessmentsSelected,
      );
      this.idDocuments = selfAssessmentIds;
      this.downloadDocuments(selfAssessmentIds);
    }
  }

  protected sendVoucherEmail(event: Event): void {
    const email = (event as CustomEvent<string>).detail;
    if (this.selfAssessmentsSelected.length) {
      const selfAssessmentIds = this.getSelfAssessmentIdsForDocuments(
        this.selfAssessmentsSelected,
      );

      this.selfAssessmentService
        .sendDocumentacio(email, selfAssessmentIds)
        .pipe(takeUntil(this.destroyed$))
        .subscribe();
    }
  }

  async showDeleteWorkingSessionModal(
    data: SeModal,
    processId: string,
  ): Promise<void> {
    const modalRef = this.modalService.openModal(data);

    modalRef.componentInstance.modalOutputEvent
      .pipe(takeUntil(this.destroyed$))
      .subscribe((data: SeModalOutputEvents) => {
        if (data === SeModalOutputEvents.MAIN_ACTION) {
          this.workingSessionService
            .deleteWorkingSession(processId)
            .pipe(takeUntil(this.destroyed$))
            .subscribe(() => {
              this.handleUpdateWorkingSessionTable.emit();
              modalRef.close();
            });
        } else {
          modalRef.close();
        }
      });

    modalRef.componentInstance.modalSecondaryButtonEvent
      .pipe(takeUntil(this.destroyed$))
      .subscribe(() => {
        modalRef.close();
      });
  }

  private setActionsInTheWorkingSessionTable(): void {
    const actionIndex = this.workingSessionColumns.length - 1;
    this.workingSessionColumns[actionIndex].cellConfig = {
      ...this.workingSessionColumns[actionIndex].cellConfig,
      hasEditWorkingSession: true,
      hasConfirmation: false,
      deleteCallback: (
        ...[, cell]: [FlattenedRow, FlattenedCell, Column]
      ): Promise<DeleteCallbackReturn> => {
        return new Promise(() => {
          this.showDeleteWorkingSessionModal(
            this.getDeleteWorkingSessionModalData(
              cell?.rowData?.dataSession?.value,
            ),
            cell?.rowData?.idTramit?.value,
          );
        });
      },
      editCallback: (
        ...[, cell]: [FlattenedRow, FlattenedCell, Column]
      ): Promise<EditCallbackReturn> => {
        return this.validateEditAndEmit(cell);
      },
    };
  }

  private validateEditAndEmit(
    cell: FlattenedCell,
  ): Promise<EditCallbackReturn> {
    return this.getEditValidation(cell).then((validation) => {
      if (validation) {
        this.handleEditWorkingSession.emit(cell);
      }
      return { data: {}, apply: validation };
    });
  }

  private getEditValidation(cell: FlattenedCell): Promise<boolean> {
    const user: SeUser = this.authService.getSessionStorageUser();
    const nifPresenterCell = cell?.rowData?.nifPresentador?.value;
    const indActualitzarPresentador =
      cell?.rowData?.indActualitzarPresentador?.value;

    if (user.nifPresentador === nifPresenterCell) {
      return Promise.resolve(true);
    }

    if (!indActualitzarPresentador) {
      return firstValueFrom(
        this.showModal(
          'error',
          this.translateService.instant(
            'SE_TRIBUTS_MF.WORKING_SESSIONS.W_S.EDIT.ERROR_IND_ACTUALITZAR_PRESENTADOR_FALSE',
            {
              nomPresentador:
                cell?.rowData?.nomPresentador?.value ?? nifPresenterCell,
            },
          ),
          'UI_COMPONENTS.BUTTONS.CLOSE',
        ),
      ).then(() => false);
    }

    // indActualitzarPresentador = true
    return firstValueFrom(
      this.showModal(
        'success',
        this.translateService.instant(
          'SE_TRIBUTS_MF.WORKING_SESSIONS.W_S.EDIT.REQUEST_CONTINUE',
          {
            nomPresentador:
              cell?.rowData?.nomPresentador?.value ?? nifPresenterCell,
          },
        ),
        'UI_COMPONENTS.BUTTONS.ACCEPT',
        'UI_COMPONENTS.BUTTONS.CANCEL',
      ),
    ).then((event) => event);
  }

  private showModal(
    severity: 'error' | 'success',
    subtitleKey: string,
    closeLabelKey: string,
    secondaryButtonLabelKey?: string,
  ): Observable<boolean> {
    const modalData: SeModal = {
      size: 'lg',
      severity,
      title: this.translateService.instant(
        'SE_TRIBUTS_MF.WORKING_SESSIONS.W_S.TITLE',
      ),
      subtitle: this.translateService.instant(subtitleKey),
      closable: true,
      closableLabel: this.translateService.instant(closeLabelKey),
      ...(secondaryButtonLabelKey && {
        secondaryButton: true,
        secondaryButtonLabel: this.translateService.instant(
          secondaryButtonLabelKey,
        ),
      }),
    };

    const modalRef = this.seModalService.openModal(modalData);
    const primaryAction$: Observable<boolean> =
      modalRef.componentInstance.modalOutputEvent.pipe(
        take(1),
        map((event) => !!(event === SeModalOutputEvents.MAIN_ACTION)),
      );

    const secondaryAction$: Observable<boolean> =
      modalRef.componentInstance.modalSecondaryButtonEvent?.pipe(
        take(1),
        map(() => false),
      ) || new Observable<boolean>();

    // Combinar observables y garantizar el tipo
    return merge(primaryAction$, secondaryAction$).pipe(
      take(1),
      finalize(() => modalRef.close()),
    );
  }

  private getDeleteWorkingSessionModalData = (
    modificationDate: string | Date,
  ): SeModal => {
    const title = this.translateService.instant(
      'SE_TRIBUTS_MF.WORKING_SESSIONS.W_S.MODAL_DELETE.TITLE',
    );
    const day = formatDate(modificationDate, 'dd/MM/YYYY', this.locale);
    const hour = formatDate(modificationDate, 'HH:mm', this.locale);
    const subtitle = this.translateService.instant(
      'SE_TRIBUTS_MF.WORKING_SESSIONS.W_S.MODAL_DELETE.QUESTION',
      { day, hour },
    );

    return {
      severity: 'warning',
      closable: true,
      title,
      subtitle,
      closableLabel: this.translateService.instant(
        'UI_COMPONENTS.BUTTONS.CONTINUE',
      ),
      secondaryButton: true,
      secondaryButtonLabel: this.translateService.instant(
        'UI_COMPONENTS.BUTTONS.CANCEL',
      ),
    };
  };

  private setSelfAssessment(taxName: string): void {
    this.workingSessionService
      .getSelfAssessments(taxName)
      .pipe(takeUntil(this.destroyed$))
      .subscribe((response) => {
        if (response?.content) {
          const sortedList = response.content.sort((a, b) =>
            this.sortSelfAssessmentsByPresentationDateOrNumJustificantDESC(
              a,
              b,
            ),
          );
          this.selfAssessments = sortedList;
          this.handleSetSelfAssessment.emit(sortedList);

          if (this.actions && !this.selfAssessmentTableData.length) {
            this.selfAssessmentTableData =
              this.getSelfAssessmentDataTableParsed(sortedList, this.actions);
          }
        }
      });
  }

  private sortSelfAssessmentsByPresentationDateOrNumJustificantDESC(
    a: SelfAssessment,
    b: SelfAssessment,
  ): number {
    const dateA = new Date(a?.dataPresentacio ?? '');
    const dateB = new Date(b?.dataPresentacio ?? '');

    return (
      dateB.getTime() - dateA.getTime() || +b.numJustificant - +a.numJustificant
    );
  }

  private downloadDocuments(selfAssessmentIds: string[]): void {
    this.seDocumentsService.downloadDocument(
      selfAssessmentIds,
      selfAssessmentIds.length === 1 ? selfAssessmentIds[0] : this.fileName,
    );
  }

  private getSelfAssessmentDataTableParsed(
    selfAssessments: SelfAssessment[],
    actions: MenuItem[],
  ): Row[] {
    return selfAssessments.map((selfA) => {
      let cell: {
        [key: string]: Cell;
      } = {};

      const getValue = (
        key: string,
      ):
        | string
        | number
        | string[]
        | SubjectePassiu
        | SelfAssessmentError[] => {
        switch (key) {
          case SelfAssessmentKeys.State:
            return `COMMONS.SELF_ASSESSMENT_ESTATS.${
              selfA[SelfAssessmentKeys.State]
            }`;
          case SelfAssessmentKeys.Declarant:
            return `${selfA[SelfAssessmentKeys.Declarant].nif} - ${
              selfA[SelfAssessmentKeys.Declarant].nom
            }`;

          default:
            return selfA[key as keyof SelfAssessment];
        }
      };

      Object.keys(selfA).forEach((key) => {
        cell = {
          ...cell,
          [key]: {
            value: getValue(key),
            ...(selfA[SelfAssessmentKeys.State] && {
              cellConfig: {
                ngStyle: {
                  color: this.getColorForState(
                    cell,
                    selfA[SelfAssessmentKeys.State],
                  ),
                },
              },
            }),
          },
        };
      });

      return {
        data: {
          ...cell,
          actions: this.getActionsInSelfAssessmentTable(cell, actions),
        },
      };
    });
  }

  private getActionsInSelfAssessmentTable(
    data: {
      [key: string]: Cell;
    },
    actions: MenuItem[] = [],
  ): Cell {
    return {
      value: '',
      cellConfig: {
        isDropdownDisabled: false,
        align: 'right',
        buttonCell: {
          buttonConfig: {
            label: this.translateService.instant(
              'SE_TRIBUTS_MF.SELF_ASSESSMENT_RESULT.BUTTONS.PAY',
            ),
            disabled: false,
            btnTheme: 'primary',
          },
          buttonCallback: (
            ...[, cell]: [FlattenedRow, FlattenedCell, Column]
          ) => {
            return new Promise(() => {
              this.handleSelfAssessmentActionButton.emit(cell);
            });
          },
        },
        actions: actions.map((action) => ({
          ...action,
          data,
        })),
      },
    };
  }

  private updateSelfAssessmentActions(): void {
    const list = [...this.selfAssessmentTableData];

    list.forEach((item) => {
      (item as MenuItem)['data'][
        'actions'
      ].cellConfig.buttonCell.buttonConfig.disabled =
        this.selfAssessmentsSelected.length > 1;

      (item as MenuItem)['data']['actions'].cellConfig.isDropdownDisabled =
        this.selfAssessmentsSelected.length > 1;
    });

    this.selfAssessmentTableData = [];
    this.cdRef.detectChanges();
    this.selfAssessmentTableData = [...list];
  }

  private getColorForState(
    data: { [key: string]: Cell },
    value: SelfAssessmentEstat,
  ): string {
    if (value.includes(SelfAssessmentEstat.PRESENTAT)) {
      return Number(data[SelfAssessmentKeys.TotalAmount]) > 0
        ? 'var(--color-orange-300)'
        : 'var(--color-green-300)';
    } else if (
      value.includes(SelfAssessmentEstat.PAGAMENT_ERROR) ||
      value.includes(SelfAssessmentEstat.NOTIFICACIO_ERROR)
    ) {
      return 'var(--color-red-400)';
    } else if (value.includes(SelfAssessmentEstat.PAGAT)) {
      return 'var(--color-green-300)';
    }

    return '';
  }

  private getSelfAssessmentIdsForDocuments(selfAssessments: Row[]): string[] {
    let selfAssessmentIds: string[] = [];
    selfAssessments.forEach(
      (selfA) =>
        (selfAssessmentIds = selfAssessmentIds.concat(
          selfA.data['idDocuments'].value,
        )),
    );

    return selfAssessmentIds;
  }
}
