import { Column, Nullable } from 'se-ui-components-mf-lib';

export interface RequestZipDec {
  idAutoliquidacio: string;
  numJustificant: string;
}

export interface Taxpayer {
  nif: string;
  name: string;
}
export interface ModalTrasspassar {
  taxPayer: Taxpayer;
  mantenirOriginal: boolean;
}

export interface RequestPresentacio {
  idSelfAssessment: string;
  idReceipt: string;
}

export interface ColumnPosition {
  column: Column;
  position: Nullable<number>;
}
