<se-alert
  *ngIf="hasErrors || errorCodes.length"
  [title]="
    'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PENDENTS_TRAMITAR.PANEL_BORRADOR.ERROR.TITLE'
      | translate
  "
  type="error"
>
  <ul>
    <li *ngFor="let error of errorCodes">
      {{ error }} -
      {{ 'UI_COMPONENTS.MODAL_ERRORS.MAPPED_ERRORS.' + error | translate }}
    </li>
  </ul>
  <p
    [innerHTML]="
      'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PENDENTS_TRAMITAR.PANEL_BORRADOR.ERROR.FOOTER'
        | translate
    "
  ></p>
</se-alert>
<div class="app-autoliquidacions-pendents-tramitar">
  <se-panel
    *ngIf="autoliquidacions"
    [title]="titlePanel | translate"
    [customActions]="traspassarButton"
  >
    <app-panel-ben
      [rows]="rows"
      [columns]="columns"
      [showDescargarDec]="showDescargarDec"
      [autoliquidacions]="autoliquidacions"
      [titlePanelAutoliquidacio]="titlePanelAutoliquidacio"
      (downloadDecZipOutput)="downloadDecZip()"
      (deleteAutoliquidacioEvent)="setAutoliquidacions(this.idAutoliquidacions)"
    ></app-panel-ben>
  </se-panel>
  <!-- FOOTER -->
  <div class="d-flex flex-row justify-content-between mt-4 mb-4">
    <se-button
      type="button"
      btnTheme="secondary"
      (onClick)="navigateBackwards()"
    >
      {{
        'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PENDENTS_TRAMITAR.BUTTONS.PREVIOUS'
          | translate
      }}
    </se-button>

    <se-button type="button" btnTheme="primary" (onClick)="continue()">
      {{
        'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PENDENTS_TRAMITAR.BUTTONS.PRESENTAR'
          | translate
      }}
    </se-button>
  </div>
</div>
<ng-template #traspassarButton>
  <se-button
    *ngIf="showTrasspasarFormulari"
    [btnTheme]="'secondary'"
    (onClick)="openModalTraspassar()"
    [ariaLabel]="
      'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PENDENTS_TRAMITAR.PANEL_BORRADOR.TRASPASSAR'
        | translate
    "
    [pTooltipAccessible]="
      'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PENDENTS_TRAMITAR.PANEL_BORRADOR.TRASPASSAR'
        | translate
    "
    icon="matSyncAltSharp"
  >
  </se-button>
</ng-template>
