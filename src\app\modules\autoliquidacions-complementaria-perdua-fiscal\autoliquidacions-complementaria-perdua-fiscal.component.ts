import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  Output,
} from '@angular/core';
import { Form<PERSON>uilder, FormControl, FormGroup } from '@angular/forms';
import { Subject } from 'rxjs';
import { DateUtilsService, Nullable } from 'se-ui-components-mf-lib';
import {
  AutoliquidacionsComplementariaPerduaFiscalData,
  InteressoType,
  RequestCalculInteressos,
  RequestUpdateData,
} from './autoliquidacions-complementaria-perdua-fiscal.model';
import { AutoliquidacionsComplementariaPerduaFiscalService } from './autoliquidacions-complementaria-perdua-fiscal.service';

@Component({
  selector: 'app-autoliquidacions-complementaria-perdua-fiscal',
  templateUrl: './autoliquidacions-complementaria-perdua-fiscal.component.html',
})
export class AutoliquidacionsComplementariaPerduaFiscalComponent
  implements OnDestroy
{
  _data: Nullable<AutoliquidacionsComplementariaPerduaFiscalData>;

  @Input() set data(data: AutoliquidacionsComplementariaPerduaFiscalData) {
    this._data = data;
    this.loadForm(data);
    this.setValidators(data);
    this.setShowInteressosDemoraForaTermini(data);
  }

  get data(): Nullable<AutoliquidacionsComplementariaPerduaFiscalData> {
    return this._data;
  }

  @Output() updateRequestDataEvent = new EventEmitter<RequestUpdateData>();
  @Output() calculateInteressosRequestEvent =
    new EventEmitter<RequestCalculInteressos>();

  autoPerduaFiscalForm: FormGroup = this.fb.group({
    dataTerminiPresentacio: [],
    dataIncompliment: [],
    dataFinTermini: [],
    dataIniciComput: [],
    dataFiComput: [],
  });

  readonlyPresentacio = true;
  readonlyIncumpliment = true;
  readonlyFiTermini = true;

  readonlyDataInici = true;
  readonlyDataFi = true;
  showInteressosDemoraForaTermini: boolean = false;

  private readonly unsubscribe: Subject<void> = new Subject();

  constructor(
    private readonly fb: FormBuilder,
    private readonly dateUtilsService: DateUtilsService,
    private readonly autoliquidacionsComplementariaPerduaFiscalService: AutoliquidacionsComplementariaPerduaFiscalService,
  ) {}

  ngOnDestroy(): void {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }

  loadForm(data: AutoliquidacionsComplementariaPerduaFiscalData): void {
    this.autoPerduaFiscalForm.patchValue({
      dataTerminiPresentacio: this.dateUtilsService.parseDate(
        data.complementaria?.dataTerminiPresentacio,
      ),
      dataIncompliment: this.dateUtilsService.parseDate(
        data.complementaria?.dataIncompliment,
      ),
      dataFinTermini: this.dateUtilsService.parseDate(
        data.complementaria?.dataFinTermini,
      ),
      dataIniciComput: this.dateUtilsService.parseDate(
        data.interessosForaTermini?.dataIniciComput ??
          data.interessosDemora?.dataIniciComput,
      ),
      dataFiComput: this.dateUtilsService.parseDate(
        data.interessosForaTermini?.dataFiComput ??
          data.interessosDemora?.dataFiComput,
      ),
    });
  }

  setValidators(data: AutoliquidacionsComplementariaPerduaFiscalData): void {
    this.autoliquidacionsComplementariaPerduaFiscalService.setValidators(
      this.autoPerduaFiscalForm,
      data,
    );
  }

  setShowInteressosDemoraForaTermini(
    data: AutoliquidacionsComplementariaPerduaFiscalData,
  ): void {
    this.showInteressosDemoraForaTermini =
      data.interessosForaTermini.dataFiComput !== null ||
      data.interessosDemora.dataFiComput !== null;
  }

  saveData(): void {
    if (this.data) {
      const request: RequestUpdateData = {
        dataTerminiPresentacio: this.dateUtilsService.formatDate(
          this.autoPerduaFiscalForm.get('dataTerminiPresentacio')?.value,
          'yyyy-MM-dd',
        ),
        dataIncumpliment: this.dateUtilsService.formatDate(
          this.autoPerduaFiscalForm.get('dataIncompliment')?.value,
          'yyyy-MM-dd',
        ),
        dataFinTermini: this.dateUtilsService.formatDate(
          this.autoPerduaFiscalForm.get('dataFinTermini')?.value,
          'yyyy-MM-dd',
        ),
      };
      this.updateRequestDataEvent.emit(request);
    }
  }

  calculateInteressos(): void {
    if (this.showInteressosDemoraForaTermini && this.data) {
      const request: RequestCalculInteressos = {
        dataIniciComput: this.dateUtilsService.formatDate(
          this.getFormControl('dataIniciComput').value,
          'yyyy-MM-dd',
        ),
        dataFiComput: this.dateUtilsService.formatDate(
          this.getFormControl('dataFiComput').value,
          'yyyy-MM-dd',
        ),
        interessoType:
          this.data?.interessosForaTermini?.dataIniciComput !== null
            ? InteressoType.FORA_TERMINI
            : InteressoType.DEMORA,
        indForaTermini:
          this.data?.interessosForaTermini?.dataIniciComput !== null,
      };
      this.calculateInteressosRequestEvent.emit(request);
    }
  }

  getFormControl = (name: string): FormControl =>
    this.autoPerduaFiscalForm.get(name) as FormControl;
}
