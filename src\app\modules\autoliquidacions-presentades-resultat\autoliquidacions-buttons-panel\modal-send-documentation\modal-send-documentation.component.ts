import { Component, EventEmitter, Input, Output } from '@angular/core';
import { Nullable, SeModal } from 'se-ui-components-mf-lib';

@Component({
  selector: 'app-modal-send-documentation-test',
  template: `<mf-documents-modal-send-documentation
    [data]="data"
    (modalOutput)="modalOutPutCapture($event)"
  ></mf-documents-modal-send-documentation>`,
})
export class ModalSendDocumentationComponent {
  @Input() data: Nullable<SeModal>;
  @Output() modalOutput: EventEmitter<string> = new EventEmitter<string>();
  modalOutPutCapture(event: Event): void {
    const email: string = (event as CustomEvent<string>).detail;
    this.modalOutput.emit(email);
  }
}
