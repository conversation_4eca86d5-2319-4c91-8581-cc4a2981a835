import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { LazyElementsModule } from '@angular-extensions/elements';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateModule } from '@ngx-translate/core';
import {
  SeButtonDropdownModule,
  SeButtonModule,
  SeCheckboxModule,
  SeDropdownModule,
  SeModalModule,
  SePanelModule,
  SeTableModule,
  SpinnerComponent,
} from 'se-ui-components-mf-lib';

import { WorkingSessionsComponent } from './working-sessions.component';
import { environment } from '@environments/environment';

@NgModule({
  declarations: [WorkingSessionsComponent],
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    FormsModule,
    ReactiveFormsModule,
    BrowserAnimationsModule,
    SePanelModule,
    SeDropdownModule,
    SeTableModule,
    SeCheckboxModule,
    SeButtonModule,
    SeModalModule,
    SeButtonDropdownModule,
    LazyElementsModule.forFeature({
      elementConfigs: [
        {
          tag: 'mf-documents-docs-actions',
          url: environment.mfDocumentsURL,
          loadingComponent: SpinnerComponent,
        },
      ],
    }),
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  providers: [NgbActiveModal],
})
export class WorkingSessionsModule {}
