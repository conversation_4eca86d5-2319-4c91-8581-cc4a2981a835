import { Component, EventEmitter, Input, Output, type <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Subject } from 'rxjs';

@Component({
  selector: 'app-confirm-recover-draft',
  templateUrl: './confirm-recover-draft.component.html',
  styleUrls: ['./confirm-recover-draft.component.scss'],
})
export class ConfirmRecoverDraftComponent implements OnDestroy {
  componentForm: FormGroup;
  @Input() alertMessage: string = '';
  @Output() draftOptionChange = new EventEmitter<boolean>();

  private destroyed$ = new Subject<void>();

  constructor(private fb: FormBuilder) {
    console.log('Webcomponent: SE Tributs > ConfirmRecoverDraftComponent > constructor');

    this.componentForm = this.fb.group({
      useDraft: [null, Validators.required],
    });
  }

  onValueChanged = (value: boolean): void => {
    this.draftOptionChange.emit(value);
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }
}
