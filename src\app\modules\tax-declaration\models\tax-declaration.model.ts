import { FormControl } from '@angular/forms';

export interface TaxSubGroupControls {
  baseImposable: FormControl<number | null>;
  reduccio: FormControl<number | null>;
  baseLiquidable: FormControl<number | null>;
  tipusGravament: FormControl<number | null>;
  quotaIntegra: FormControl<number | null>;
}

export type TaxSubGroupKeys =
  | 'dioxidSofre'
  | 'oxidNitrogen'
  | 'particules'
  | 'carboniOrganic';

export type CodiGravamen = 'DIOAZU' | 'OXINIT' | 'PARTIC' | 'CARORG';

export interface GravamentTaxSubGroup {
  codiGravamen: CodiGravamen;
  importTarifa: number;
}
export interface ReduccioBonificacio {
  codi: CodiGravamen;
  valor: number;
  tipus: 'REDUCCIONS' | 'BONIFICACIONS';
}

interface Contaminant {
  baseImposable: number;
  reduccio: number;
  baseLiquidable: number;
  tipusGravament: number;
  quotaIntegra: number;
}

interface BonificacioCombustible {
  indBonificacio: boolean;
  quotaIntegraBonificable: number;
  percent: number;
  percentAplicable: number;
  total: number;
}

interface BonificacioInversio {
  indBonificacio: boolean;
  inversioRealizada: number;
  percent: number;
  resultat: number;
  total: number;
}

export interface TaxDeclararion {
  idTramit: string;
  dioxidSofre: Contaminant;
  oxidNitrogen: Contaminant;
  particules: Contaminant;
  carboniOrganic: Contaminant;
  sumaQuotes: number;
  anex: Anex;
  bonificacioCombustible: BonificacioCombustible;
  bonificacioInversio: BonificacioInversio;
}

export interface Anex {
  idPadoct: string;
  nom: string;
  pes: string;
  descripcio: string;
  tipusDocument: string;
  extension: string;
}

export interface ContestableActDocument {
  type: string;
  subtype?: string;
  name?: string;
  description?: string;
  required?: boolean;
  allowedFiles?: string[];
  allowedSize?: number;
}
