import { Injectable } from '@angular/core';
import { environment } from '@environments/environment';
import { Observable } from 'rxjs';
import {
  SeHttpRequest,
  SeHttpResponse,
  SeHttpService,
} from 'se-ui-components-mf-lib';
import {
  ModalTrasspassar,
  RequestPresentacio,
} from './autoliquidacions-pendents-tramitar.model';

@Injectable({
  providedIn: 'root',
})
export class AutoPendentsTramitarEndpointsService {
  constructor(private readonly httpService: SeHttpService) {}

  agruparAutoliquidaciones(
    idsFormulari: string[],
  ): Observable<SeHttpResponse<string>> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlTributs,
      url: `/agrupar`,
      body: idsFormulari,
      spinner: false,
      method: 'post',
    };

    return this.httpService.post(httpRequest);
  }

  presentatAutoliquidaciones(
    request: RequestPresentacio[],
  ): Observable<SeHttpResponse<boolean>> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlTributs,
      url: `/presentacio`,
      method: 'post',
      spinner: false,
      body: request,
    };

    return this.httpService.post(httpRequest);
  }

  trasspasarFormulari(
    idTramit: string,
    modalTrasspassar: ModalTrasspassar,
  ): Observable<SeHttpResponse<string>> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlTributs,
      url: `/sessions-treball/${idTramit}/traspassar`,
      spinner: false,
      method: 'post',
      body: {
        presenterNif: modalTrasspassar.taxPayer.nif,
        presenterName: modalTrasspassar.taxPayer.name,
        mantenirOriginal: modalTrasspassar.mantenirOriginal,
      },
    };

    return this.httpService.post(httpRequest);
  }
}
