import { DatePipe } from '@angular/common';
import { Injectable } from '@angular/core';
//LIBRARY
import { ItemSelectableCard } from 'se-ui-components-mf-lib';
//APP
import { firstValueFrom, lastValueFrom, map, take } from 'rxjs';
import { DadesDocumentEndpointsService } from '../dades-document-endpoints.service';
import {
  FormDocumentValues,
  ListItem,
  ResponseNotaryData,
} from '../models/dades-document.model';
import { DocumentTypes, SPAIN_CODE } from '../models/shared.model';
import { AddressEndpointsService } from './address/address-endpoints.service';

const TRANSLATE_ROOT = 'SE_TRIBUTS_MF.DADES_DOCUMENT.FORM_FIELDS';

@Injectable({
  providedIn: 'root',
})
export class DadesDocumentService {
  constructor(
    private addressEndpointsService: AddressEndpointsService,
    private dadesDocumentEndpointsService: DadesDocumentEndpointsService,
    private datePipe: DatePipe
  ) {}

  async getCountryName(formValues: FormDocumentValues): Promise<string> {
    let countryName = '';
    const response = await firstValueFrom(
      this.addressEndpointsService.getCountries$
    );
    if (response) {
      const country = response.content.find(
        (country) => country.id === formValues.pais
      );
      countryName = country?.label ?? '';
    }

    return countryName;
  }

  async getProvincia(formValues: FormDocumentValues): Promise<string> {
    let provinciaName = '';
    const response = await firstValueFrom(
      this.addressEndpointsService.getSpainProvinces$
    );
    if (response) {
      const provincia = response.content.find(
        (provincia) => provincia.id === formValues.provincia
      );
      provinciaName = provincia?.label ?? '';
    }

    return provinciaName;
  }

  async getMunicipi(formValues: FormDocumentValues): Promise<string> {
    let municipiName = '';
    const response = await this.addressEndpointsService.getMunicipiList(
      formValues.provincia
    );
    if (response) {
      const municipi = response.find(
        (municipi) => municipi.id === formValues.municipi
      );
      municipiName = municipi?.label ?? '';
    }

    return municipiName;
  }

  getNotary = (
    formValues: FormDocumentValues
  ): Promise<ListItem | undefined> | ListItem => {
    const request = {
      province: formValues.provincia,
      municipality: formValues.municipi,
      date: formValues.documentDate as string,
    };

    return lastValueFrom(
      this.dadesDocumentEndpointsService.getNotary(request).pipe(
        take(1),
        map((response: ResponseNotaryData) => {
          const notary = response.content?.find(
            (notary) => notary.id === formValues.notary
          );
          return notary;
        })
      )
    );
  };

  async getNotaryName(formValues: FormDocumentValues): Promise<string> {
    let valueNotary = 'SE_TRIBUTS_MF.DADES_DOCUMENT.FORM_FIELDS.NOTARY_OLD_INPUT';
    //not SPAIN
    if (formValues.pais !== SPAIN_CODE) {
      valueNotary = 'SE_TRIBUTS_MF.DADES_DOCUMENT.FORM_FIELDS.EXTRANGER';
    } else if (!formValues.notaryOld) {
      if (formValues.notary_NAME) {
        valueNotary = formValues.notary_NAME;
      } else {
        const response = await this.getNotary(formValues);
        if (response?.label) {
          valueNotary = response.label;
        }
      }
    }
    return valueNotary;
  }

  async getResumDocumentValues(
    formValues: FormDocumentValues
  ): Promise<ItemSelectableCard[]> {
    const responseDocument = await firstValueFrom(
      this.dadesDocumentEndpointsService.getDocumentType()
    );

    const resum: ItemSelectableCard[] = [];

    if (responseDocument) {
      const documentType = responseDocument.content.find(
        (dt) => dt.id === formValues.documentType
      );
      documentType &&
        resum.push({
          title: `${TRANSLATE_ROOT}.DOC_TYPE`,
          description: documentType.label,
        });
    }

    switch (formValues.documentType) {
      case DocumentTypes.JUDICIAL:
        resum.push({
          title: `${TRANSLATE_ROOT}.TRIB_JUL`,
          description: formValues.trib_jul!,
        });
        break;
      case DocumentTypes.NOTARIAL:
        const notaryName = await this.getNotaryName(formValues);
        resum.push({
          title: `${TRANSLATE_ROOT}.NOTARY`,
          description: notaryName,
        });
        formValues.PROT &&
          resum.push({
            title: `${TRANSLATE_ROOT}.PROTOCOL`,
            description: formValues.PROT_BIS
              ? `${formValues.PROT}/${formValues.PROT_BIS}`
              : formValues.PROT ?? '',
          });
        break;
      case DocumentTypes.ADMINISTRATIU:
        resum.push({
          title: `${TRANSLATE_ROOT}.AUTORITAT_READONLY`,
          description: formValues.autor!,
        });
        break;
    }

    if (formValues.documentType !== DocumentTypes.NOTARIAL) {
      resum.push({
        title: `${TRANSLATE_ROOT}.REFERENCE`,
        description: formValues.reference!,
      });
    }
    const countryName = await this.getCountryName(formValues);
    let place = countryName;

    //SPAIN
    if (formValues.pais === SPAIN_CODE) {
      place = `${await this.getMunicipi(
        formValues
      )}, (${await this.getProvincia(formValues)}), ${countryName}`;
    }

    resum.push(
      {
        title: `${TRANSLATE_ROOT}.DATE`,
        description: this.datePipe.transform(
          formValues.documentDate,
          'dd/MM/yyyy'
        )!,
      },
      { title: `${TRANSLATE_ROOT}.PLACE`, description: place }
    );

    return resum;
  }
}
