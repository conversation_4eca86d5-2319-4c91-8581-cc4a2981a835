import {
  Component,
  EventEmitter,
  Input,
  Output,
  type OnDestroy,
} from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Subject, takeUntil } from 'rxjs';

import { Column, type Nullable, type Row } from 'se-ui-components-mf-lib';
import {
  SeComplementaryActionsCellComponent,
  type SeComplementaryActionsCellConfig,
} from '../complementary-actions-cell';
import { SeComplementaryEndpointsService } from '../complementary-endpoints.service';
import {
  automaticComplementaryTableColumns,
  type AutoliquidacioComplementaria,
  type GetAutoliquidacioCompRequest,
  type GetAutoliquidacioComplementariaResponse,
} from '../models';
import { SelfAssessmentEstat } from '@core/models';

/**
 * Componente que permite buscar autoliquidaciones previamente
 * presentadas y emitir una seleccionada para ser pagada o complementada.
 */
@Component({
  selector: 'app-automatic-complementary',
  templateUrl: './automatic-complementary.component.html',
})
export class SeAutomaticComplementaryComponent implements OnDestroy {
  /** Texto que se mostrará dentro de la caja azul de información */
  @Input() information: Nullable<string>;

  /** Texto descriptivo del panel. */
  @Input() description: Nullable<string>;

  /** Si es true aparecerá un switch que le permitirá al usuario decidir si
   * quiere buscar una complementaria de manera manual o no.
   */
  @Input() isOptional: boolean = false;

  /** Puedes indicar cuales son las keys de las columnas que NO quieres mostrar en la tabla */
  @Input() set hideTableColumns(keys: string[]) {
    this.removeTableColumns(keys);
  }

  @Input() set manualInfoText(text: Nullable<string>) {
    this._manualInfoText = text;
  }

  /**
   * Parámetros necesarios para la búsqueda de autoliquidaciones previamente
   * presentadas y que no dependen directamente del componente, sino que deben
   * establecerse desde fuera (desde un componente padre).
   */
  @Input() set searchRequestParameters(
    parameters: Nullable<GetAutoliquidacioCompRequest>,
  ) {
    if (
      // Parametros obligatorios
      !parameters?.idTramit ||
      !parameters?.impost ||
      !parameters?.exercici ||
      !parameters?.periodi ||
      !parameters?.model ||
      (this.isOptional && !parameters?.clau)
    ) {
      return;
    }

    this._searchRequestParameters = parameters;
    this.searchPresentedSelfassessments(parameters);
  }

  get searchRequestParameters(): Nullable<GetAutoliquidacioCompRequest> {
    return this._searchRequestParameters;
  }

  /**
   * Emite cuando el usuario pulsa el botón para realizar una autoliquidación
   * complementaria.
   */
  @Output() complementaryButtonClick =
    new EventEmitter<AutoliquidacioComplementaria>();

  /**
   * Emite cuando la consulta automática de autoliquidaciones previas finaliza.
   */
  @Output() automaticSearchEnd = new EventEmitter<{
    selfassessments: AutoliquidacioComplementaria[];
    hiddenSelfassessmentsExist: boolean;
  }>();

  /**
   * Emite cuando la búsqueda mediante el formulario finaliza.
   */
  @Output() formSearchEnd = new EventEmitter<
    Nullable<AutoliquidacioComplementaria>
  >();

  /**
   * Emite cuando el usuario pulsa el botón para pagar una autoliquidación
   * previa.
   */
  @Output() payButtonClick = new EventEmitter<AutoliquidacioComplementaria>();
  @Output() handleChangeVisibility = new EventEmitter<boolean>();

  /**
   * En caso de isOpcional = true, el usuario puede decicidir no informar una complementaria
   * en ese caso se emitirá un valor true para dejarle continuar.
   */
  @Output() continueWithoutSearch = new EventEmitter<boolean>();

  protected title: Nullable<string>;

  protected lastTaxReturnDocumentsIds: string[] = [];

  protected filename: string = '';

  protected columns: Column[] = automaticComplementaryTableColumns;

  protected data: Row[] = [];

  protected isSearchFormVisible = false;

  protected isVisible = false;

  private _searchRequestParameters: Nullable<GetAutoliquidacioCompRequest>;

  protected _manualInfoText: Nullable<string> = null;

  private destroyed$ = new Subject<void>();

  constructor(
    private endpoints: SeComplementaryEndpointsService,
    private translate: TranslateService,
  ) {}

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  private searchPresentedSelfassessments(
    params: GetAutoliquidacioCompRequest,
  ): void {
    this.endpoints
      .getAutoliquidacioComplementari({
        idTramit: params.idTramit,
        impost: params.impost,
        exercici: params.exercici,
        periodi: params.periodi,
        model: params.model,
        ...(params.clau ? { clau: params.clau } : null),
      })
      .pipe(takeUntil(this.destroyed$))
      .subscribe({
        next: this.searchPresentedSelfassessmentsSuccessCallback.bind(this),
        error: this.resetData.bind(this),
      });
  }

  private removeTableColumns(keys: string[]): void {
    this.columns = this.columns.filter((column) => !keys.includes(column.key));
  }

  private searchPresentedSelfassessmentsSuccessCallback(
    response: GetAutoliquidacioComplementariaResponse,
  ): void {
    const { existCompl = false, complementariList: selfassessments = [] } =
      response.content ?? {};
    const showManualComplementary = this.isOptional && !existCompl;

    if (this.shouldHandleNoSelfassessmentsCase(existCompl, selfassessments)) {
      return;
    }

    this.handleOptionalCase(selfassessments, existCompl);

    this.isVisible = true;
    this.handleChangeVisibility.emit(this.isVisible);

    this.processSelfassessments(selfassessments, existCompl);
    this.setTitle(existCompl);
    this.setSearchFormVisibility(existCompl, showManualComplementary);
    this.emitAutomaticSearchEnd(selfassessments, existCompl);
  }

  private shouldHandleNoSelfassessmentsCase(
    existCompl: boolean,
    selfassessments: AutoliquidacioComplementaria[],
  ): boolean {
    if (!this.isOptional && selfassessments.length === 0) {
      this.resetData(existCompl);
      this.automaticSearchEnd.emit({
        selfassessments: [],
        hiddenSelfassessmentsExist: existCompl,
      });

      if (existCompl) {
        this.title = this.translate.instant(
          'SE_TRIBUTS_MF.COMPLEMENTARY.PANEL_TITLE_DIFFERENT_PRESENTER',
        );
        this.isSearchFormVisible = true;
      }
      return true;
    }
    return false;
  }

  private handleOptionalCase(
    selfassessments: AutoliquidacioComplementaria[],
    existCompl: boolean,
  ): void {
    if (this.isOptional && selfassessments.length === 0 && existCompl) {
      this.isOptional = false;
    }
  }

  private processSelfassessments(
    selfassessments: AutoliquidacioComplementaria[],
    existCompl: boolean,
  ): void {
    if (selfassessments.length > 0) {
      this.lastTaxReturnDocumentsIds = selfassessments
        .slice(0, 5)
        .flatMap((assessment) => assessment.idDocuments ?? [])
        .slice(0, 5);

      this.filename = this.getFilename(selfassessments);

      this.data = selfassessments.map((selfassessment, index) =>
        this.convertSelfassessementToTableRow({
          selfassessment,
          isComplementaryButtonVisible: index === 0 && !existCompl,
        }),
      );
    }
  }

  private getFilename(selfassessments: AutoliquidacioComplementaria[]): string {
    return selfassessments.length === 1
      ? selfassessments[0].numJustificant
      : this.translate.currentLang === 'ca'
        ? 'Justificants_Presentacio'
        : 'Justificantes_Presentacion';
  }

  private setTitle(existCompl: boolean): void {
    this.title = this.translate.instant(
      existCompl
        ? 'SE_TRIBUTS_MF.COMPLEMENTARY.PANEL_TITLE_DIFFERENT_PRESENTER'
        : 'SE_TRIBUTS_MF.COMPLEMENTARY.PANEL_TITLE_SAME_PRESENTER',
    );
  }

  private setSearchFormVisibility(
    existCompl: boolean,
    showManualComplementary: boolean,
  ): void {
    this.isSearchFormVisible =
      existCompl || (showManualComplementary && this.data.length <= 0);
  }

  private emitAutomaticSearchEnd(
    selfassessments: AutoliquidacioComplementaria[],
    existCompl: boolean,
  ): void {
    this.automaticSearchEnd.emit({
      selfassessments,
      hiddenSelfassessmentsExist: existCompl,
    });
  }

  private resetData(isVisible?: boolean): void {
    this.title = null;
    this.data = [];
    this.lastTaxReturnDocumentsIds = [];
    this.filename = '';
    this.isVisible = isVisible ?? false;
    this.handleChangeVisibility.emit(this.isVisible);
  }

  private convertSelfassessementToTableRow({
    selfassessment,
    isComplementaryButtonVisible,
  }: {
    selfassessment: AutoliquidacioComplementaria;
    isComplementaryButtonVisible: boolean;
  }): Row {
    const {
      numJustificant,
      subjectePassiu: { nif, nom },
      dataPresentacio,
      tipus,
      quotaLiquida,
      estat,
    } = selfassessment;

    const isPayButtonVisible =
      !!estat &&
      [
        SelfAssessmentEstat.PENDENT_PAGAMENT,
        SelfAssessmentEstat.PAGAMENT_ERROR,
      ].includes(estat);

    let actionsCellConfig: SeComplementaryActionsCellConfig = {
      isComplementaryButtonVisible,
      isPayButtonVisible,
    };

    if (isComplementaryButtonVisible) {
      // Añade callback botón "complementaria"
      actionsCellConfig = {
        ...actionsCellConfig,
        complementaryButtonCallbackFn: (): void => {
          this.complementaryButtonClick.emit(selfassessment);
        },
      };
    }

    if (isPayButtonVisible) {
      // Añade callback botón "pagar"
      actionsCellConfig = {
        ...actionsCellConfig,
        payButtonCallbackFn: (): void => {
          this.payButtonClick.emit(selfassessment);
        },
      };
    }

    return {
      data: {
        receiptId: { value: numJustificant },
        taxpayer: { value: `${nif} - ${nom}` },
        submissionDate: { value: dataPresentacio },
        tipus: { value: tipus },
        totalAmount: { value: quotaLiquida },
        state: {
          value: this.translate.instant(
            `COMMONS.SELF_ASSESSMENT_ESTATS.${estat}`,
          ),
        },
        actions: {
          value: null,
          cellComponent: SeComplementaryActionsCellComponent,
          cellConfig: actionsCellConfig,
        },
      },
    };
  }
}
