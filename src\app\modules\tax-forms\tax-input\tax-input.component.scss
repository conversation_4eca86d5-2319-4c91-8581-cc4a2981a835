.tax-input__value-input,
.tax-input__total-price-input {
  min-width: 200px;
}

.tax-input__dates {
  min-width: 147px;
}

.tax-input__label > span,
.tax-input__dates > span,
.tax-input__price-per-unit > span {
  font-family: var(--font-primary);
  font-size: var(--text-xs);
  color: var(--color-gray-700);
  display: grid;
  align-items: center;
  height: 40px;
}

.tax-input__dates > span,
.tax-input__price-per-unit > span {
  font-weight: var(--font-semibold);
  text-wrap: nowrap;
}

.tax-input__price-per-unit > span {
  text-align: center;
}

.tax-input {
  display: flex;
  flex-direction: row;

  &__header-label, &__header-label--total {
    display: none;
  }

  @media (max-width: 768px) {
    flex-direction: column;

    &__label {
      width: 100% !important;
    }

    &__header-label {
      display: block;
      text-align: center;

      &--total {
        display: block;
        text-align: right;
      }
    }
  }
}
