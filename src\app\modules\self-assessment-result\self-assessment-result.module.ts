import { LazyElementsModule } from '@angular-extensions/elements';
import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { NgIconsModule } from '@ng-icons/core';
import * as IconsSharp from '@ng-icons/material-icons/sharp';
import { TranslateModule } from '@ngx-translate/core';
import {
  SeButtonDropdownModule,
  SeButtonModule,
  SeModalModule,
  SePanelModule,
  SeTableModule,
  SpinnerComponent,
} from 'se-ui-components-mf-lib';

import { environment } from 'src/environments/environment';
import { ResultActionsCellComponent } from './actions-cell-template/actions-cell-template.component';
import { SelfAssessmentResultComponent } from './self-assessment-result.component';

@NgModule({
  declarations: [SelfAssessmentResultComponent, ResultActionsCellComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    LazyElementsModule.forFeature({
      elementConfigs: [
        {
          tag: 'mf-documents-download-document-button',
          url: environment.mfDocumentsURL,
          loadingComponent: SpinnerComponent,
        },
        {
          tag: 'mf-documents-send-documents-button',
          url: environment.mfDocumentsURL,
          loadingComponent: SpinnerComponent,
          preload: true,
        },
        {
          tag: 'mf-documents-docs-actions',
          url: environment.mfDocumentsURL,
          loadingComponent: SpinnerComponent,
        },
      ],
    }),
    NgIconsModule.withIcons(IconsSharp),
    FormsModule,
    ReactiveFormsModule,
    BrowserAnimationsModule,
    SePanelModule,
    SeButtonModule,
    SeButtonDropdownModule,
    SeModalModule,
    SeTableModule,
  ],
  exports: [],
  providers: [],
})
export class SelfAssessmentResultModule {}
