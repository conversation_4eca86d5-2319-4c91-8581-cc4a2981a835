import { Component, Input, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import {
  TaxSubGroupControls,
  TaxSubGroupKeys,
} from '../models/tax-declaration.model';

@Component({
  selector: 'app-mass-form-template',
  templateUrl: './mass-form-template.component.html',
  styleUrls: ['./mass-form-template.component.scss'],
})
export class MassFormTemplateComponent implements OnInit {
  @Input({ required: true }) parentForm!: FormGroup;
  @Input({ required: true }) subGroupName!: TaxSubGroupKeys;
  @Input() gassTypeName: string = '';

  get subForm(): FormGroup<TaxSubGroupControls> {
    return this.parentForm?.get(
      this.subGroupName,
    ) as FormGroup<TaxSubGroupControls>;
  }

  ngOnInit(): void {
    this.subForm.get('baseLiquidable')?.valueChanges.subscribe(() => {
      this.calculateQuotaIntegra();
    });
  }

  private calculateQuotaIntegra(): void {
    const base = this.subForm.get('baseLiquidable')?.value || 0;
    const tipus = this.subForm.get('tipusGravament')?.value || 0;
    const quota = base * (tipus / 100);
    this.subForm.get('quotaIntegra')?.setValue(quota, { emitEvent: false });
  }
}
