import { NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { Subject } from 'rxjs';
import { SelfAssessmentEstat } from './self-assesment-estat.enum';

export class CheckStatusAutoliquidacionsParameters {
  modalRef: NgbModalRef;
  idsAutoliquidacio?: string[];
  statValidat: SelfAssessmentEstat;
  progressValue$?: Subject<number>;
  closeModal?: boolean;
  progressValuesHistory?: Array<number>;

  constructor(data: CheckStatusAutoliquidacionsParameters) {
    this.modalRef = data.modalRef;
    this.idsAutoliquidacio = data.idsAutoliquidacio;
    this.statValidat = data.statValidat;
    this.progressValue$ = data.progressValue$;
    this.closeModal = data.closeModal ?? true;
    this.progressValuesHistory = [];
  }
}
