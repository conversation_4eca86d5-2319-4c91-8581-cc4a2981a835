import { Nullable } from 'se-ui-components-mf-lib';

export interface AutoliquidacionsForaTerminiData {
  complementaria: Complementaria;
  indRequerimentPrevi: boolean;

  idCalcul: string;
  dataFinalitzacioTerme: string;

  interessosForaTermini: Interessos;
  recarrec: Recarrec;
}

export interface Complementaria {
  indComplementaria: boolean;
  indPresentaPerduaBeneficioFiscal: boolean;
  dataTerminiPresentacio: string;
}
export interface Interessos {
  dataIniciComput?: string;
  dataFiComput?: string;
}

export interface Recarrec {
  indRecarrec: boolean;
  percentaje: number;
  checkReduccio: boolean;
}
export interface RequestUpdateRecarrec {
  idCalcul: string;
  indRecarrec: boolean;
  checkReduccio: boolean;
}

export interface RequestCalculInteressos {
  idCalcul: Nullable<string>;
  dataIniciComput: Nullable<string>;
  dataFiComput: Nullable<string>;
  baseCalcul: Nullable<number>;
  interessoType: InteressoType;
}

export enum InteressoType {
  DEMORA = 'DEMORA',
  FORA_TERMINI = 'FORA_TERMINI',
}
