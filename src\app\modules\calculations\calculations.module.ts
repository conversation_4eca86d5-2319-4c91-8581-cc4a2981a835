import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { SePanelModule, SeButtonModule } from 'se-ui-components-mf-lib';
import { CalculationsComponent } from './calculations.component';
import { SurchargesComponent } from './surcharges/surcharges.component';
import { SurchargesModule } from './surcharges/surcharges.module';
import { SummaryModule } from './summary/summary.module';

@NgModule({
    declarations: [CalculationsComponent],
    imports: [ 
        CommonModule,
		TranslateModule.forChild(),
        FormsModule,
        ReactiveFormsModule,
        BrowserAnimationsModule,
        SurchargesModule,
        SummaryModule,
        SePanelModule,
        SeButtonModule,
     ],
    exports: [],
    providers: [],
})
export class CalculationsModule {}