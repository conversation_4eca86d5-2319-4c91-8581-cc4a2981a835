import { CellConfig, SeHttpResponse } from 'se-ui-components-mf-lib';

interface SubjectePassiu {
  nif: string;
  nom: string;
  idPers: string;
  indEstranger: boolean;
  uuid: string | null;
  declaracioResponsable: boolean;
  dataNaixement: string | null;
  tipusDocument: string;
}

export interface HttpResponseSelfAssessment extends SeHttpResponse {
  content: ResponseSelfAssessment;
}

export interface HttpResponseSelfAssessments extends SeHttpResponse {
  content: ResponseSelfAssessment[];
}

export interface ResponseSelfAssessment {
  numJustificant: string;
  idAutoliquidacio: string;
  idDocuments: string[];
  docsNegoci: string[];
  errors: ErrorSelfAssessment[];
  quotaLiquida: number;
  subjectePassiu: SubjectePassiu;
  tipus: string;
  estat: string;
  idMfpt: string;
  dataPresentacio: string | null;
  dataIncompliment: string | null;
  dataFinTermini: string | null;
  dataTerminiPresentacio: string | null;
}

export interface ErrorSelfAssessment {
  date: string;
  code: string;
  description: string;
  technicalCode: string;
  technicalDescription: string;
}

export class TableSelfassessmentData {
  justificant: { value: string } = { value: '' };
  taxpayer: { value: string } = { value: '' };
  date: { value: string } = { value: '' };
  type: { value: string } = { value: '' };
  total: { value: string; cellConfig?: CellConfig } = { value: '' };
  state: { value: string } = { value: '' };
  nif: { value: string } = { value: '' };
  idAutoliquidacio: { value: string } = { value: '' };
  idMfpt: { value: string } = { value: '' };
  errors: { value: ErrorSelfAssessment[] } = { value: [] };

  constructor(response: ResponseSelfAssessment) {
    this.justificant.value = response.numJustificant;
    this.taxpayer.value = response.subjectePassiu.nom;
    this.date.value = response.dataPresentacio!;
    this.type.value = response.tipus;
    this.total.value = response.quotaLiquida.toString();
    this.state.value = response.estat;
    this.nif.value = response.subjectePassiu.nif;
    this.idAutoliquidacio.value = response.idAutoliquidacio;
    this.idMfpt.value = response.idMfpt;
    this.errors.value = response.errors;
  }
}
