import { Cell } from 'se-ui-components-mf-lib';

interface SelfAssessmentError {
  code: string;
  date: string;
  description: string;
  technicalCode: string;
  technicalDescription: string;
  trackingId: string;
  stackTrace: string;
}

interface SubjectePassiu {
  nif: string;
  nom: string;
}

interface SelfAssessmentEstat {
  codi: string;
  descripcio: string;
}

export interface SelfAssessment {
  idMfpt: string;
  errors: SelfAssessmentError[];
  numJustificant: string;
  idDocuments: string[];
  idAutoliquidacio: string;
  quotaLiquida: number;
  quotaTributaria: number;
  subjectePassiu: SubjectePassiu;
  tipus: string;
  estat: SelfAssessmentEstat;
  dataPresentacio: string;
}

export interface SelfAssessmentCell {
  numJustificant: Cell;
  idDocuments: Cell;
  idAutoliquidacio: Cell;
  quotaLiquida: Cell;
  quotaTributaria: Cell;
  subjectePassiu: Cell;
  tipus: Cell;
  estat: Cell;
  dataPresentacio: Cell;
  idMfpt?: Cell;
  errors?: Cell;
  actions?: Cell;
}
