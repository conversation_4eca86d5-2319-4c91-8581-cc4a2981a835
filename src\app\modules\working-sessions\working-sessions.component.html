<ng-template #customActionsTemplate>
  <mf-documents-docs-actions
    *axLazyElement
    [documentsIds]="idDocuments"
    [customFileName]="fileName"
    [isDownloadDocumentButtonDisabled]="!selfAssessmentsSelected.length"
    [isSendDocumentButtonDisabled]="!selfAssessmentsSelected.length"
    (downloadDocumentEvent)="onDownloadDocumentsZip(selfAssessmentsSelected)"
    (sendDocumentEvent)="sendVoucherEmail($event)"
  />
</ng-template>

<!-- VISIBLE CONTENT -->
<se-panel
  [id]="'workingSessionsPanel'"
  [title]="'SE_TRIBUTS_MF.WORKING_SESSIONS.SELF_ASSESSMENTS.TITLE' | translate"
  [colapsible]="false"
  *ngIf="selfAssessmentTableData.length"
  panelTheme="primary"
  [customActions]="customActionsTemplate"
>
  <p>
    {{
      'SE_TRIBUTS_MF.WORKING_SESSIONS.SELF_ASSESSMENTS.DESCRIPTION_PANEL1'
        | translate: { model }
    }}
  </p>

  <se-table
    [selectable]="selfAssessmentTableData.length > 1"
    [cellTemplatePriorityOrder]="'row-column-cell'"
    [resizable]="true"
    (onSelectionChange)="onSelfAssessmentSelectionChange($event)"
    [currentPage]="0"
    [itemsPerPage]="5"
    [showEmptyState]="true"
    [showPagination]="true"
    [columns]="selfAssessmentHeaders"
    [data]="selfAssessmentTableData"
    *ngIf="selfAssessmentTableData.length"
  >
  </se-table>
</se-panel>
<br *ngIf="selfAssessmentTableData.length" />

<se-panel
  *ngIf="workingSessionsTableData.length"
  [title]="'SE_TRIBUTS_MF.WORKING_SESSIONS.W_S.TITLE' | translate"
  [colapsible]="false"
  panelTheme="primary"
>
  <div [innerHTML]="workingSessionsPanelDescription | translate"></div>

  <se-table
    [itemsPerPage]="workingSessionDefaultNumberOfItems"
    [cellTemplatePriorityOrder]="'row-column-cell'"
    [showPagination]="true"
    [columns]="workingSessionColumns"
    [data]="workingSessionsTableData"
    [rowsPerPageOptions]="workingSessionRowsPerPageOptions"
    [showRowsPerPage]="true"
  >
  </se-table>
</se-panel>
<!-- VISIBLE CONTENT -->
