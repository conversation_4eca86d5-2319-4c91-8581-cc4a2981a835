import {
  Component,
  EventEmitter,
  On<PERSON><PERSON>roy,
  OnInit,
  Output,
} from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ValidationErrors,
  Validators,
} from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import { Subject } from 'rxjs';
import {
  Nullable,
  SeMessageService,
  SeModal,
  SeModalOutputEvents,
  SeValidations,
} from 'se-ui-components-mf-lib';
import {
  ModalTrasspassar,
  Taxpayer,
} from '../autoliquidacions-pendents-tramitar.model';
import { ScoringStatus } from './modal-scoring.modal';

@Component({
  selector: 'app-modal-scoring',
  templateUrl: './modal-scoring.component.html',
  styleUrls: ['./modal-scoring.component.scss'],
})
export class ModalScoringComponent implements OnInit, On<PERSON><PERSON>roy {
  BASE_TRANSLATE = 'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PENDENTS_TRAMITAR';
  data: Nullable<SeModal>;
  scoringTaxpayer: Nullable<Taxpayer>;
  validScoring = false;
  modalScoringForm: Nullable<FormGroup>;

  scoringStatus: Nullable<ScoringStatus>;

  @Output() trasspasarFormulariEvent: EventEmitter<ModalTrasspassar> =
    new EventEmitter<ModalTrasspassar>();
  @Output() setShowExceptionViewerEvent: EventEmitter<boolean> =
    new EventEmitter<boolean>();

  private readonly unsubscribe: Subject<void> = new Subject();

  validatorsNIF: ((control: AbstractControl) => ValidationErrors | null)[] = [
    Validators.required,
    SeValidations.dniNie(
      this.translateService.instant(
        `${this.BASE_TRANSLATE}.MODAL_SCORING.ERROR_NIF`,
      ),
    ),
  ];

  constructor(
    private readonly activeModal: NgbActiveModal,
    private readonly seMessageService: SeMessageService,
    private readonly translateService: TranslateService,
    private readonly fb: FormBuilder,
  ) {}

  ngOnInit(): void {
    this.modalScoringForm = this.fb.group({
      mantenirOriginal: [false],
    });
    this.setShowExceptionViewerEvent.emit(false);
    this.data = {
      title: `${this.BASE_TRANSLATE}.MODAL_SCORING.TITLE`,
      closable: true,
      closableDisabled: true,
      closableLabel: `${this.BASE_TRANSLATE}.BUTTONS.TRANSFERIR_SESSIO`,
      secondaryButton: true,
      secondaryButtonLabel: `${this.BASE_TRANSLATE}.BUTTONS.CANCEL`,
    };
    this.modalScoringForm.valueChanges.subscribe((value) => {
      if (this.data) {
        this.data.closableLabel = value.mantenirOriginal
          ? `${this.BASE_TRANSLATE}.BUTTONS.TRANSFERIR_COPIAR_SESSIO`
          : `${this.BASE_TRANSLATE}.BUTTONS.TRANSFERIR_SESSIO`;
      }
    });
  }

  ngOnDestroy(): void {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }

  closeModal(): void {
    this.activeModal.close();
    this.setShowExceptionViewerEvent.emit(true);
  }

  protected onScoringValidationChange(event: Event): void {
    const customEvent = event as CustomEvent<{
      valid: boolean;
    }>;
    this.validScoring = customEvent.detail.valid;
    // reseteo los mensajes tanto si esta validado o no
    this.seMessageService.resetMessages();
    if (this.validScoring && this.data) {
      this.data.closableDisabled = false;
    } else if (this.scoringStatus !== ScoringStatus.Default) {
      // Controlo que el estado sea distinto de Default para no mostrar el mensaje cuando se modifique el valor del pagador
      // Pongo el mensaje especifico para solo personas fisicas
      this.seMessageService.addMessages([
        {
          severity: 'error',
          subtitle: `${this.BASE_TRANSLATE}.MODAL_SCORING.SCORING_NOT_MATCH`,
        },
      ]);
    }
  }

  protected onScoringChange(event: Event): void {
    this.scoringStatus = (event as CustomEvent<ScoringStatus>).detail;
  }

  protected trasspasarFormulari(action: string): void {
    if (action === SeModalOutputEvents.CLOSE) {
      this.closeModal();
      return;
    }

    if (this.validScoring && this.scoringTaxpayer) {
      this.trasspasarFormulariEvent.emit({
        taxPayer: this.scoringTaxpayer,
        mantenirOriginal: this.modalScoringForm?.get('mantenirOriginal')?.value,
      });
    }
  }

  protected onScoringTaxpayerChange(event: Event): void {
    const scoringTaxpayerEvent = { ...(event as CustomEvent<Taxpayer>).detail };
    if (!this.validScoring && this.data) {
      this.validScoring = false;
      this.data.closableDisabled = true;
    }
    this.scoringTaxpayer = scoringTaxpayerEvent;
  }
}
