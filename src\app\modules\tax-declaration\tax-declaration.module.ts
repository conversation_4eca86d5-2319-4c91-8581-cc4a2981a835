import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule, Routes } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import {
  SeAlertModule,
  SeButtonModule,
  SeInputModule,
  SePanelModule,
  SeSwitchModule,
  SeUploadFilesModule,
  SpinnerComponent,
} from 'se-ui-components-mf-lib';

import { TaxDeclarationComponent } from './tax-declaration.component';
import { MassFormTemplateComponent } from './mass-form-template/mass-form-template.component';
import { NgIconsModule } from '@ng-icons/core';
import { matSimCardDownloadOutline } from '@ng-icons/material-icons/outline';
import { LazyElementsModule } from '@angular-extensions/elements';
import { environment } from '@environments/environment';

const routes: Routes = [
  {
    path: '',
    component: TaxDeclarationComponent,
    data: {
      title: 'SE_GASOS_MF.APP_TITLE',
      stepId: 'REPOSITION_RESOURCE_DESKTOP_STEP3',
      stepperId: 'REPOSITION_RESOURCE_ENABLE',
    },
  },
];

@NgModule({
  declarations: [TaxDeclarationComponent, MassFormTemplateComponent],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule.forChild(),
    RouterModule.forChild(routes),
    SeAlertModule,
    SePanelModule,
    SeButtonModule,
    SeInputModule,
    SeSwitchModule,
    SeUploadFilesModule,
    LazyElementsModule.forFeature({
      elementConfigs: [
        {
          tag: 'mf-documents-upload-files',
          url: environment.mfDocumentsURL,
          loadingComponent: SpinnerComponent,
          preload: true,
        },
      ],
    }),
    NgIconsModule.withIcons({
      matSimCardDownloadOutline,
    }),
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class TaxDeclarationModule {}
