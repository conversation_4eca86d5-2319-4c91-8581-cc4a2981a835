import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Column, SeDocumentsService } from 'se-ui-components-mf-lib';
import { MenuItem } from 'primeng/api';

import { SelfAssessmentCell } from './model/self-assessment.model';
import { WORKING_SESSION_HEADERS } from './model/working-session.model';
import { LoginInfoService } from '@core/services/login-info.service';
import { Constant } from '@core/models/constants.enum';

@Injectable({
  providedIn: 'root',
})
export class WorkingSessionService {
  private seuUrl = '';
  private readonly defaultFileName = `Docs_${Constant.MODEL}.zip`;

  constructor(
    private translateService: TranslateService,
    private loginInfo: LoginInfoService,
    private seDocumentsService: SeDocumentsService,
  ) {}

  getWorkingSessionHeaders(): Column[] {
    return WORKING_SESSION_HEADERS.map((elem) => {
      return { ...elem, header: this.translateService.instant(elem.header) };
    });
  }

  async getSelfAssessmentActions(isDisabled = false): Promise<MenuItem[]> {
    this.seuUrl = await this.loginInfo.getSeuUrl();

    return [
      {
        disabled: isDisabled,
        label: this.translateService.instant(
          'SE_GASOS_MF.MODULE_WORKING_SESSION.SELF_ASSESSMENTS.ACTIONS.OBTAIN_PAYMENT_LETTER',
        ),
        command: (event): void => {
          const data: SelfAssessmentCell = (event.item as MenuItem)['data'];
          const ids = data?.idDocuments?.value;
          if (ids.length) {
            this.downloadDocuments(ids);
          }
        },
      },
      {
        disabled: isDisabled,
        label: this.translateService.instant(
          'SE_GASOS_MF.MODULE_WORKING_SESSION.SELF_ASSESSMENTS.ACTIONS.APPLY_FOR_FRACTIONATION',
        ),
        command: (event): void => {
          if (event.item && this.seuUrl) {
            window.open(
              `${this.seuUrl}/${this.translateService.currentLang}/OficinaVirtual/Pagines/TramitsGenerics.aspx`,
            );
          }
        },
      },
    ];
  }

  private downloadDocuments(selfAssessmentIds: string[]): void {
    this.seDocumentsService.downloadDocument(
      selfAssessmentIds,
      selfAssessmentIds.length === 1
        ? selfAssessmentIds[0]
        : this.defaultFileName,
    );
  }
}
