import { DecimalPipe } from '@angular/common';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { SelfAssessmentEstat } from '@core/models';
import {
  Autoliquidacio,
  CalculErrors,
} from '@core/models/autoliquidacio.model';
import { CheckStatusAutoliquidacionsParameters } from '@core/models/check-status-autoliquidacions-parameters.model';
import { firstValueFrom } from 'rxjs';
import { AutoliquidacionsEndpointsService } from './autoliquidacions-endpoints.service';

@Injectable({
  providedIn: 'root',
})
export class AutoliquidacionsService {
  constructor(
    private readonly autoliquidacionsEndpointsService: AutoliquidacionsEndpointsService,
    private readonly router: Router,
    private readonly decimalPipe: DecimalPipe,
  ) {}

  async checkStatusAutoliquidaciones(
    data: CheckStatusAutoliquidacionsParameters,
  ): Promise<[CalculErrors[], Autoliquidacio[]]> {
    const autoliquidaciones = await this.getAutoliquidaciones(
      data.idsAutoliquidacio,
    );
    const lenghtArray = autoliquidaciones.length;

    const [calculsErrors, idCalculsValidat] = this.checkCalculErrors(
      autoliquidaciones,
      data.statValidat,
      data,
    );
    if (
      calculsErrors.length > 0 &&
      calculsErrors.length + idCalculsValidat === lenghtArray
    ) {
      data.modalRef?.close();
    } else if (idCalculsValidat === lenghtArray) {
      data.closeModal && data.modalRef?.close(true);
    }
    return [calculsErrors, autoliquidaciones];
  }

  async getAutoliquidaciones(
    idsAutoliquidacio?: string[],
  ): Promise<Autoliquidacio[]> {
    let autoResult;
    if (idsAutoliquidacio) {
      autoResult = await firstValueFrom(
        this.autoliquidacionsEndpointsService.getAutoliquidacions(
          idsAutoliquidacio,
        ),
      );
    }
    return autoResult?.content ?? [];
  }

  checkCalculErrors(
    autoliquidacions: Autoliquidacio[],
    statValidat: SelfAssessmentEstat,
    data?: CheckStatusAutoliquidacionsParameters,
  ): [CalculErrors[], number] {
    const calculsErrors: CalculErrors[] = [];
    let idCalculsValidat = 0;
    autoliquidacions.forEach((autoliquidacio) => {
      if (this.hasError(autoliquidacio.estat)) {
        calculsErrors.push({
          idCalcul: autoliquidacio.idAutoliquidacio,
          errors: autoliquidacio.errors,
        });
      } else if (this.checkStatValidat(statValidat, autoliquidacio)) {
        idCalculsValidat++;
      }
    });
    if (data?.progressValue$ && data.progressValuesHistory) {
      // Aumento 15 % cada vez que se llama hasta un maximo de 90%
      const percent = 15;
      const history = data.progressValuesHistory ?? [];
      const lastPercent = history[history.length - 1] ?? 0;
      const progress = lastPercent < 90 ? lastPercent + percent : lastPercent;

      data.progressValuesHistory.push(progress);
      data.progressValue$.next(progress);

      // Corto la ejecucion si en los ultimos 90sec no se ha cambiado de estado para eliminar que se quede en bucle
      if (
        this.checkLastProgressSameByNumber(
          [...(data.progressValuesHistory ?? [])],
          18,
        )
      ) {
        data.modalRef?.close(calculsErrors);
      }
    }
    return [calculsErrors, idCalculsValidat];
  }

  checkStatValidat(
    statValidat: SelfAssessmentEstat,
    autoliquidacio: Autoliquidacio,
  ): boolean {
    return autoliquidacio.estat === statValidat;
  }

  /**
   * Incremento el progreso en un 10% si las ultimas 3 llamadas ha devuelto el mismo progreso
   * @param percent porgcentaje generado de los estados
   * @param history historial de progressos para contrastar los ultimos
   * @returns
   */
  getArtificialProgressBar(percent: number, history: Array<number>): number {
    let progress = Number(
      this.decimalPipe.transform(percent, '1.2-2')?.replace(',', '.'),
    );
    const lastPercent = history[history.length - 1] ?? 0;
    progress = progress > lastPercent ? progress : lastPercent;
    if (progress <= 90 && this.checkLastProgressSameByNumber(history, 3)) {
      progress += 15;
    }
    return progress;
  }

  checkLastProgressSameByNumber(
    history: Array<number>,
    numberOfIterations: number,
  ): boolean {
    let isSameHistory = true;
    if (history.length >= numberOfIterations) {
      history = history.reverse();
      const referenceValue = history[0];
      for (let index = 0; index < numberOfIterations; index++) {
        if (referenceValue !== history[index]) {
          isSameHistory = false;
        }
      }
      return isSameHistory;
    }
    return false;
  }

  /**
   * Comprueba si el estado de la autoliquidacion es final:
   * Puede ser final si esta en estado 'PAGAT' o si esta en estado 'PRESENTAT' y el total a ingressar es 0
   * @param auto Atuoliquidacio
   * @returns
   */
  checkStatDraftFinalize(auto: Autoliquidacio): boolean {
    return (
      auto.estat === SelfAssessmentEstat.PAGAT ||
      (auto.estat === SelfAssessmentEstat.PRESENTAT && auto.quotaLiquida === 0)
    );
  }

  hasError(estat: SelfAssessmentEstat): boolean {
    return (
      estat === SelfAssessmentEstat.ERROR ||
      estat === SelfAssessmentEstat.PRESENTACIO_ERROR ||
      estat === SelfAssessmentEstat.PAGAMENT_ERROR ||
      estat === SelfAssessmentEstat.ESBORRANY_ERROR ||
      estat === SelfAssessmentEstat.ESBORRANY_AGRUPANT_ERROR ||
      estat === SelfAssessmentEstat.NOTIFICACIO_ERROR
    );
  }
}
