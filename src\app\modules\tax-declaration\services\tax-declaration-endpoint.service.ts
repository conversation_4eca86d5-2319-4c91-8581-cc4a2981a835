import { Injectable } from '@angular/core';
import { environment } from '@environments/environment';
import { Observable } from 'rxjs';
import { SeHttpResponse, SeHttpService } from 'se-ui-components-mf-lib';
import {
  GravamentTaxSubGroup,
  TaxDeclararion,
} from '../models/tax-declaration.model';

@Injectable({
  providedIn: 'root',
})
export class TaxDeclarationEndpointService {
  constructor(private httpService: SeHttpService) {
    // Empty constructor
  }

  getDeclaration(idTramit: string): Observable<SeHttpResponse> {
    return this.httpService.get({
      method: 'get',
      baseUrl: environment.baseUrlGasos,
      url: `/${idTramit}/declaracio`,
      clearExceptions: true,
    });
  }

  postDeclaration(body: TaxDeclararion): Observable<SeHttpResponse> {
    return this.httpService.post({
      method: 'post',
      baseUrl: environment.baseUrlGasos,
      url: `/declaracio`,
      clearExceptions: true,
      body: body,
    });
  }

  getTipusGravament(
    impost: string,
    exercici: string,
    periode: string,
  ): Observable<SeHttpResponse<GravamentTaxSubGroup[]>> {
    return this.httpService.get({
      method: 'get',
      baseUrl: environment.baseUrlTributs,
      url: `/${impost}/exercici/${exercici}/periode/${periode}/tarifa`,
      clearExceptions: true,
    });
  }

  // get bonificaciones y reducciones /api/gasos/:idTramit/dades-mestres
  getDadesMestres(idTramit: string): Observable<SeHttpResponse> {
    return this.httpService.get({
      method: 'get',
      baseUrl: environment.baseUrlGasos,
      url: `/${idTramit}/dades-mestres`,
      clearExceptions: true,
    });
  }
}
