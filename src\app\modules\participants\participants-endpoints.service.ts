import { Injectable } from '@angular/core';
import { HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { SeHttpService } from 'se-ui-components-mf-lib';

import { environment } from 'src/environments/environment';
import { DraftRequest, GetDraftHttpResponse } from './models/draft.model';
import {
  CreateProcedureRequest,
  PostCreateProcedureHttpResponse,
} from './models';

@Injectable({
  providedIn: 'root',
})
export class ParticipantsService {
  constructor(
    private httpService: SeHttpService,
    private translateService: TranslateService,
  ) {
    /* intentionally empty constructor */
  }

  verifyIfTaxPayerHasDraft({
    taxName,
    taxPayerNif,
    shouldUseDraft,
  }: DraftRequest): Observable<GetDraftHttpResponse> {
    const params = new HttpParams()
      .set('impost', taxName)
      .set('nifSubjectePasiu', taxPayerNif)
      .set('esborrany', shouldUseDraft);

    return this.httpService.get({
      baseUrl: environment.baseUrlTributs,
      url: '',
      method: 'get',
      params,
    });
  }

  createProcedure(
    body: CreateProcedureRequest,
  ): Observable<PostCreateProcedureHttpResponse> {
    return this.httpService.post({
      baseUrl: environment.baseUrlTributs,
      url: '/create',
      method: 'post',
      body,
    });
  }
}
