import { Component, EventEmitter, Input, Output } from '@angular/core';
import {
  Form<PERSON>uilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import {
  DateUtilsService,
  Nullable,
  SeValidations,
} from 'se-ui-components-mf-lib';
import {
  AutoliquidacionsForaTerminiData,
  InteressoType,
  RequestCalculInteressos,
  RequestUpdateRecarrec,
} from './autoliquidacions-fora-termini.model';

@Component({
  selector: 'app-autoliquidacions-fora-termini',
  templateUrl: './autoliquidacions-fora-termini.component.html',
})
export class AutoliquidacionsForaTerminiComponent {
  _data: Nullable<AutoliquidacionsForaTerminiData>;

  @Input() set data(data: Nullable<AutoliquidacionsForaTerminiData>) {
    this._data = data;
    if (this.data) {
      this.setForm(this.data);
      const dataInici = this.calculateDataInici(
        this.data.dataFinalitzacioTerme,
      );
      if (dataInici) {
        this.setValidatorsDataIniciComput(dataInici);
        this.setValidatorsDataFiComput(
          new Date(this.getFormControl('dataIniciComput').value),
        );
      }
    }
  }
  get data(): Nullable<AutoliquidacionsForaTerminiData> {
    return this._data;
  }

  @Output() requerimentPreviChangeRequest = new EventEmitter<boolean>();
  @Output() updateRecarrecRequest = new EventEmitter<RequestUpdateRecarrec>();
  @Output() calculateInteressosRequest =
    new EventEmitter<RequestCalculInteressos>();

  today = new Date();
  componentForm!: FormGroup;
  readonlyDataInici = true;
  readonlyDataFi = true;

  constructor(
    private readonly fb: FormBuilder,
    private readonly dateUtilsService: DateUtilsService,
  ) {}

  setForm(data: AutoliquidacionsForaTerminiData): void {
    this.componentForm = this.fb.group({
      indRequerimentPrevi: [data.indRequerimentPrevi],
      indAplicarRecarrer: [data.recarrec.indRecarrec],
      tipusPercent: [data.recarrec.percentaje],
      indAplicarReduccio: [data.recarrec.checkReduccio],
      dataIniciComput: [
        this.dateUtilsService.parseDate(
          data.interessosForaTermini.dataIniciComput,
        ),
      ],
      dataFiComput: [
        this.dateUtilsService.parseDate(
          data.interessosForaTermini.dataFiComput,
        ),
      ],
    });
  }

  dataIniciChange(dataInici: Date | Date[]): void {
    if (typeof dataInici === 'object') {
      this.readonlyDataFi = false;
      this.setValidatorsDataFiComput(dataInici as Date);
    }
  }

  setValidatorsDataIniciComput(dataInici: Date): void {
    this.getFormControl('dataIniciComput').setValidators([
      Validators.required,
      SeValidations.dateRange(
        dataInici,
        this.today,
        'SE_TRIBUTS_MF.CALCUL.AUTOLIQUIDACIONS-FORA-TERMINI.INTERESSOS.DATA_INICI_MIN_FORA',
        'SE_TRIBUTS_MF.CALCUL.AUTOLIQUIDACIONS-FORA-TERMINI.INTERESSOS.DATA_INICI_MAX',
      ),
    ]);
  }

  setValidatorsDataFiComput(dataFi: Date): void {
    this.getFormControl('dataFiComput').setValidators([
      Validators.required,
      SeValidations.dateRange(
        dataFi,
        this.today,
        'SE_TRIBUTS_MF.CALCUL.AUTOLIQUIDACIONS-FORA-TERMINI.INTERESSOS.DATA_FIN_MIN',
        'SE_TRIBUTS_MF.CALCUL.AUTOLIQUIDACIONS-FORA-TERMINI.INTERESSOS.DATA_FIN_MAX',
      ),
    ]);
    this.getFormControl('dataFiComput').updateValueAndValidity();
    this.getFormControl('dataFiComput').markAsTouched();
  }

  getFormControl = (name: string): FormControl =>
    this.componentForm?.get(name) as FormControl;

  requerimentPreviChange(indRequerimentPrevi: boolean): void {
    this.requerimentPreviChangeRequest.emit(indRequerimentPrevi);
  }

  showAdministracioRequeriment(): boolean {
    // Secció en la que el ciudadano puede indicar si la presentación es consecuencia de un requerimiento previo de la administración.
    // No será visible en los casos en los que se trate de una complementaria que NO sea por pérdida de beneficio fiscal.
    return (
      !this.data?.complementaria.indComplementaria ||
      this.data?.complementaria.indPresentaPerduaBeneficioFiscal
    );
  }

  showRecarrec(): boolean {
    //Sección visible en los casos en los que el ciudadano NO indique que la presentación es consecuencia de un requerimiento previo de la administración.
    return this.getFormControl('indRequerimentPrevi').value === false;
  }

  showInteressosDemoraForaTermini(): boolean {
    // Visible si: Campo Recàrrec está informado Y han transcurrido más de 12 meses desde la fecha de fin del plazo de presentación
    //  Y ((no existe requerimiento previo de la Administración) O (NO ES una autoliquidación complementaria por pérdida de beneficio fiscal)).
    return !!this.data?.interessosForaTermini.dataIniciComput;
  }

  checkOneYearTerme(): boolean {
    const dataFinalitzacioTerme = this.dateUtilsService.parseDate(
      this.data?.dataFinalitzacioTerme,
    );
    if (dataFinalitzacioTerme) {
      dataFinalitzacioTerme.setMonth(dataFinalitzacioTerme.getMonth() + 12);
      return dataFinalitzacioTerme < new Date();
    }
    return false;
  }

  saveData(): void {
    this.calculateInteressos();
  }

  indAplicarRecarrerChange(indRecarrec: boolean): void {
    this.getFormControl('indAplicarReduccio')?.setValue(indRecarrec);
    this.updateRecarrec(indRecarrec, indRecarrec);
  }

  indAplicarReduccioChange(checkReduccio: boolean): void {
    this.updateRecarrec(true, checkReduccio);
  }

  updateRecarrec(indRecarrec: boolean, checkReduccio: boolean): void {
    if (this.data) {
      const request: RequestUpdateRecarrec = {
        idCalcul: this.data.idCalcul,
        indRecarrec,
        checkReduccio,
      };
      this.calculateInteressos();
      this.updateRecarrecRequest.emit(request);
    }
  }

  calculateInteressos(): void {
    if (this.showInteressosDemoraForaTermini()) {
      this.readonlyDataInici = true;
      this.readonlyDataFi = true;
      const request: RequestCalculInteressos = {
        idCalcul: null,
        dataIniciComput: this.dateUtilsService.formatDate(
          this.getFormControl('dataIniciComput').value,
          'yyyy-MM-dd',
        ),
        dataFiComput: this.dateUtilsService.formatDate(
          this.getFormControl('dataFiComput').value,
          'yyyy-MM-dd',
        ),
        baseCalcul: null,
        interessoType: InteressoType.FORA_TERMINI,
      };

      this.calculateInteressosRequest.emit(request);
    }
  }

  calculateDataInici(dataFinalitzacioTerme: string): Nullable<Date> {
    let dataInici = this.dateUtilsService.parseDate(dataFinalitzacioTerme);
    if (
      this.data?.complementaria.indComplementaria &&
      this.data?.complementaria.indPresentaPerduaBeneficioFiscal
    ) {
      dataInici = this.dateUtilsService.parseDate(
        this.data?.complementaria.dataTerminiPresentacio,
      );
    }
    if (dataInici) {
      dataInici.setMonth(dataInici.getMonth() + 12);
      dataInici.setDate(dataInici.getDate() + 1);
    }
    return dataInici;
  }
}
