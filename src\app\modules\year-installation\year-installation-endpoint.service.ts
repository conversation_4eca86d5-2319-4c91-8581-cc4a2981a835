import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import {
  Nullable,
  SeHttpResponse,
  SeHttpService,
} from 'se-ui-components-mf-lib';

import { environment } from 'src/environments/environment';
import { InitialData } from './model/year-installation.model';

@Injectable({
  providedIn: 'root',
})
export class YearInstallationEndpointService {
  constructor(private httpService: SeHttpService) {}

  getWorkingSession(
    idProcedure: Nullable<string>,
    year: Nullable<number>,
    clau: Nullable<string>,
  ): Observable<SeHttpResponse> {
    return this.httpService.get({
      method: 'get',
      baseUrl: environment.baseUrlTributs,
      url: `/sessions-treball/${idProcedure}/${year}/${clau}`,
      clearExceptions: true,
    });
  }

  getInitialData(idTramit: string): Observable<SeHttpResponse<InitialData>> {
    return this.httpService.get({
      method: 'get',
      baseUrl: environment.baseUrlGasos,
      url: `/${idTramit}/dades-inicials`,
      clearExceptions: true,
    });
  }

  saveInitialData(body: InitialData): Observable<SeHttpResponse> {
    return this.httpService.post({
      method: 'post',
      baseUrl: environment.baseUrlGasos,
      url: `/dades-inicials`,
      clearExceptions: true,
      body,
    });
  }
}
