<div class="app-self-assessment-result">
  <se-panel
    [id]="'summaryPanel'"
    [title]="titleLabelPanel | translate"
    [colapsible]="false"
    class="panel-self-assessment-result"
    [labelButton]="
      'SE_TRIBUTS_MF.SELF_ASSESSMENT_RESULT.BUTTONS.DOWNLOAD' | translate
    "
    [customActions]="customButton"
    panelTheme="primary"
  >
    <p
      *ngIf="subtitleLabelPanel"
      [innerHTML]="subtitleLabelPanel | translate"
    ></p>
    <se-table
      *ngIf="tableData"
      [columns]="tableHeader"
      [data]="tableData"
      [resizable]="true"
      [cellTemplatePriorityOrder]="'row-column-cell'"
      [currentPage]="0"
      [itemsPerPage]="5"
      [showEmptyState]="true"
      [emptyButton]="emptyButtonTable"
    ></se-table>

    <ng-template #customButton>
      <section
        class="app-self-assessment-result__custom-buttons"
        *ngIf="
          showDocsActions && idDocuments.length && idAutoliquidacions.length
        "
      >
        <mf-documents-docs-actions
          *axLazyElement
          [showDownloadMenu]="isPagat && showDownloadMenu"
          [documentsIds]="idDocuments"
          [customFileName]="numJustificant"
          [itemsDropDownload]="itemsDropDownload"
          [emitDownloadDocument]="emitDownloadDocument"
          (downloadDocumentEvent)="downloadAllZip()"
          [emitSendDocument]="emitSendDocument"
          (sendDocumentEvent)="sendJustificant($event)"
        />
      </section>
    </ng-template>
  </se-panel>
</div>
