import { TAX_MODEL } from '@core/models';

export interface RequestComplementaryFormulari {
  numJustificant: string;
  quotaTotal: number;
  dataPresentacio: string;
  impost: TAX_MODEL;
  dataIncompliment?: string; // perdida fiscal
  dataFinTermini?: string; // perdida fiscal
  dataTerminiPresentacio?: string; // perdida fiscal
}

export interface ResponseCheckComplementari {
  existeixComplementaria: boolean;
  consultatPreviament: boolean;
}

export interface ResponseCrearSesionComplementaria {
  idFormulari: string;
}
