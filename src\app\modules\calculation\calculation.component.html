<div
  *ngIf="requestPeriodInfo && calculationInfo"
  class="d-flex flex-column gap-4"
>
  <mf-tributs-calculations
    *axLazyElement
    [requestPeriodInfo]="requestPeriodInfo"
    [calculationInfo]="calculationInfo"
    [summaryTemplate]="template"
    [useCommonSummaryTemplate]="false"
    (formData)="getDataFromTributs($event)"
  ></mf-tributs-calculations>

  <!--  ALERTS -->
  <section *ngIf="alertTitle">
    <se-alert
      [title]="alertTitle"
      type="warning"
      [closeButton]="true"
      (close)="closeAlert()"
    >
    </se-alert>
  </section>

  <!--  BUTTONS -->
  <section class="d-flex justify-content-between flex-row">
    <se-button (onClick)="goBack()" [theme] [btnTheme]="'secondary'">
      {{ "UI_COMPONENTS.BUTTONS.PREVIOUS" | translate }}
    </se-button>
    <se-button (onClick)="submit()" [disabled]="disabledButtonNext">
      {{ "UI_COMPONENTS.BUTTONS.TRAMIT" | translate }}
    </se-button>
  </section>
</div>
