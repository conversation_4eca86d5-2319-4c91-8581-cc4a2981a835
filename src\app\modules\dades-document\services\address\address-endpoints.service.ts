import { Injectable } from '@angular/core';
import { Observable, lastValueFrom, map, shareReplay } from 'rxjs';
//LIBRARY
import { SeHttpRequest, SeHttpService } from 'se-ui-components-mf-lib';
//APP
import { environment } from 'src/environments/environment';
import {
  RequestGetMunicipalities,
  RequestGetProvinces,
} from './address-endpoints.model';
import { BasicList, BasicListResponse, SPAIN_CODE } from '../../models/shared.model';

@Injectable({
  providedIn: 'root',
})
export class AddressEndpointsService {

  public getCountries$!: Observable<BasicListResponse>;
  public getSpainProvinces$!: Observable<BasicListResponse>;
  public getRoadType$!: Observable<BasicListResponse>;
  public getNumberingType$!: Observable<BasicListResponse>;

  private municipies: BasicList[][] = [];

  constructor(
    private httpService: SeHttpService,
  ) { 
    this.getCountries$ = this.getCountries().pipe(shareReplay(1));
    this.getSpainProvinces$ = this.getProvinces({ RES_ESP: SPAIN_CODE }).pipe(shareReplay(1));
    this.getRoadType$ = this.getRoadType().pipe(shareReplay(1));
    this.getNumberingType$ = this.getNumberingType().pipe(shareReplay(1));
  }

  getCountryList = async (): Promise<BasicList[]> => await lastValueFrom(this.getCountries$.pipe(map(result => result?.content || [])));

  /**
   * Get list of countries
   * @description Get paisos from Datalake DDBB
   */
  getCountries = (): Observable<BasicListResponse> => {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlDadesReferencia,
      url: `/paisos`,
      method: 'get',
    };
    return this.httpService.get(httpRequest);
  }

  getSpainProvinces = async (): Promise<BasicList[]> => await lastValueFrom(this.getSpainProvinces$.pipe(map(result => result?.content || [])));

  /**
   * Get list of provinces
   * @description Get provincies from Datalake DDBB
   */
  getProvinces = (request: RequestGetProvinces): Observable<BasicListResponse> => {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlDadesReferencia,
      url: `/paisos/${request.RES_ESP}/provinci`,
      method: 'get',
    };
    return this.httpService.get(httpRequest);
  }

  getMunicipiList = async (COD_PRV: string): Promise<BasicList[]> => {
    const index = Number(COD_PRV);

    if(!this.municipies[index]) {
      const request: RequestGetMunicipalities = { COD_PRV };
      this.municipies[index] = await lastValueFrom(this.getMunicipalities(request).pipe(map(result => result?.content || [])));
    }

    return this.municipies[index];
  }

  /**
   * Get COD_MUN (Municipis)
   * @description Get municipalities from Datalake DDBB
   */
  getMunicipalities = (request: RequestGetMunicipalities): Observable<BasicListResponse> => {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlDadesReferencia,
      url: `/provinci/${request.COD_PRV}/municipi`,
      method: 'get',
    };
    return this.httpService.get(httpRequest);
  }

  getRoadTypeList = async (): Promise<BasicList[]> => await lastValueFrom(this.getRoadType$.pipe(map(result => result?.content || [])));

  getRoadType = (): Observable<BasicListResponse> => {
		const httpRequest: SeHttpRequest = {
			baseUrl: environment.baseUrlDadesReferencia,
			url: `/tipus-via`,
			method: 'get',
			clearExceptions: true
		};
		return this.httpService.get(httpRequest);
	}

  getNumberingTypeList = async (): Promise<BasicList[]> => await lastValueFrom(this.getNumberingType$.pipe(map(result => result?.content || [])));

  getNumberingType = (): Observable<BasicListResponse> => {
		const httpRequest: SeHttpRequest = {
			baseUrl: environment.baseUrlDadesReferencia,
			url: `/tipus-numeracio`,
			method: 'get',
			clearExceptions: true
		};
		return this.httpService.get(httpRequest);
	}

  getCodiPostalList(municipiCode: string): Observable<BasicListResponse> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlDadesReferencia,
      url: `/municipi/${municipiCode}/codi-postal`,
      method: 'get',
    };
    return this.httpService.get(httpRequest);
  }
}
