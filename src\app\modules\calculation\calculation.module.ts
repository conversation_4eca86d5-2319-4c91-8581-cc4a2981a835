import {
  CommonModule,
  C<PERSON>rencyPipe,
  DatePipe,
  DecimalPipe,
} from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';

import { CalculationComponent } from './calculation.component';
import { LazyElementsModule } from '@angular-extensions/elements';
import { environment } from 'src/environments/environment';
import {
  SeAlertModule,
  SeButtonModule,
  SpinnerComponent,
} from 'se-ui-components-mf-lib';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

const routes: Routes = [
  {
    path: '',
    component: CalculationComponent,
    data: {
      title: 'SE_GASOS_MF.APP_TITLE',
      stepId: 'REPOSITION_RESOURCE_DESKTOP_STEP4',
      stepperId: 'REPOSITION_RESOURCE_ENABLE',
    },
  },
];

@NgModule({
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  providers: [DatePipe, CurrencyPipe, DecimalPipe],
  declarations: [CalculationComponent],
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    RouterModule.forChild(routes),
    LazyElementsModule.forFeature({
      elementConfigs: [
        {
          tag: 'mf-tributs-calculations',
          url: environment.mfTributsURL,
          loadingComponent: SpinnerComponent,
          preload: true,
        },
      ],
    }),
    FormsModule,
    ReactiveFormsModule,
    SeButtonModule,
    SeAlertModule,
  ],
})
export class CalculationModule {}
