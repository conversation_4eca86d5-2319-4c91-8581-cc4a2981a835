import {
  Component,
  EventEmitter,
  Input,
  Output,
  TemplateRef,
} from '@angular/core';

import type { Nullable } from 'se-ui-components-mf-lib';
import { CalculTreeItem } from '../../models/calcul.model';

@Component({
  selector: 'app-calcul-resum',
  template: `
    <se-panel
      class="panel-0-padding"
      [id]="panelId || 'panel-resum-calculs'"
      [title]="panelTitle || 'SE_TRIBUTS_MF.CALCUL.PANEL_TITLE' | translate"
      [collapsible]="!!panelCollapsible"
    >
      <ng-content></ng-content>
      <div class="p-4">
        <app-calcul-tree-view
          *ngIf="items && items.length"
          [items]="items"
          (itemClick)="itemClick.emit($event)"
        />
        <div class="calcul-resum__footer">
          <span class="d-flex justify-content-end align-items-start">
            {{ 'SE_TRIBUTS_MF.CALCUL.TOTAL_A_INGRESSAR' | translate }}&nbsp;
            <span [attr.translate]="'no'">
              {{ total }}
            </span>
            <ng-icon
              *ngIf="totalTooltip"
              class="tooltip-icon"
              name="matInfo"
              size="14"
              [pTooltipAccessible]="totalTooltip"
            ></ng-icon>
          </span>
        </div>
      </div>
    </se-panel>
  `,
  styleUrls: ['./calcul-resum.component.scss'],
})
export class CalculResumComponent {
  @Input() panelId: Nullable<string>;
  @Input() panelTitle: Nullable<string>;
  @Input() panelCollapsible: Nullable<boolean>;
  @Input() items: CalculTreeItem[] = [];
  @Input() total: Nullable<string>;
  @Input() totalTooltip:
    | Nullable<string>
    | TemplateRef<HTMLElement>
    | undefined;

  @Output() itemClick = new EventEmitter<CalculTreeItem>();
}
