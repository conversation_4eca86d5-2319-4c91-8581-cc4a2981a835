import { Component, EventEmitter, Input, Output } from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import {
  DateUtilsService,
  Nullable,
  SeValidations,
} from 'se-ui-components-mf-lib';

@Component({
  selector: 'app-data-fi-termini',
  templateUrl: './data-fi-termini.component.html',
})
export class DataFiTerminiComponent {
  BASE_TRANSLATE = 'SE_TRIBUTS_MF.CALCUL.DATA_FI_TERMINI';

  @Input() isComplementaria = false;
  @Input() set dataFinalitzacioTerme(dataFinalitzacioTerme: Nullable<string>) {
    const dataFinalizacio = this.dateUtilsService.parseDate(
      dataFinalitzacioTerme,
    );
    if (dataFinalizacio) {
      this.setForm(dataFinalizacio);

      this.setValidators(dataFinalizacio);
    }
  }

  @Input() set dataFinalitzacioTermeSistema(
    dataFinalitzacioTermeSistema: Nullable<string>,
  ) {
    const dataFinalizacio = this.dateUtilsService.parseDate(
      dataFinalitzacioTermeSistema,
    );
    dataFinalizacio && this.setValidators(dataFinalizacio);
  }

  @Output() saveDataRequest = new EventEmitter<RequestUpdateData>();

  readonly = true;
  componentForm!: FormGroup;

  constructor(
    private readonly fb: FormBuilder,
    private readonly dateUtilsService: DateUtilsService,
  ) {}

  setForm(dataFinalizacio: Date): void {
    this.componentForm = this.fb.group({
      dataFinalitzacioTerme: [dataFinalizacio],
    });
  }

  setValidators(dataFinalizacio: Date): void {
    this.getFormControl('dataFinalitzacioTerme')?.setValidators([
      Validators.required,
      SeValidations.dateRange(
        dataFinalizacio,
        null,
        `${this.BASE_TRANSLATE}.DATA_FI_TERMINI_ERROR`,
      ),
    ]);
  }

  saveData(): void {
    const request: RequestUpdateData = {
      dataFinalitzacioTerme: this.dateUtilsService.formatDate(
        this.getFormControl('dataFinalitzacioTerme').value,
        'yyyy-MM-dd',
      ),
    };

    this.saveDataRequest.emit(request);
  }

  getFormControl = (name: string): FormControl =>
    this.componentForm?.get(name) as FormControl;
}

interface RequestUpdateData {
  dataFinalitzacioTerme: Nullable<string>;
}
