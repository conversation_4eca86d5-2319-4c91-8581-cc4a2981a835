import { Component, HostBinding, Input } from '@angular/core';

import type {
  CellComponent,
  Column,
  FlattenedCell,
  FlattenedRow,
} from 'se-ui-components-mf-lib';
import type { SeComplementaryActionsCellConfig } from './models';

@Component({
  selector: 'se-complementary-actions-cell',
  template: `
    <se-button
      *ngIf="cellConfig.isComplementaryButtonVisible"
      [size]="'small'"
      [btnTheme]="'primary'"
      (onClick)="cellConfig.complementaryButtonCallbackFn?.()"
    >
      {{
        'SE_TRIBUTS_MF.COMPLEMENTARY.ACTIONS_CELL.COMPLEMENTARY_BUTTON_LABEL'
          | translate
      }}
    </se-button>

    <se-button
      *ngIf="cellConfig.isPayButtonVisible"
      [size]="'small'"
      [btnTheme]="'primary'"
      (onClick)="cellConfig.payButtonCallbackFn?.()"
    >
      {{
        'SE_TRIBUTS_MF.COMPLEMENTARY.ACTIONS_CELL.PAY_BUTTON_LABEL' | translate
      }}
    </se-button>
  `
})
export class SeComplementaryActionsCellComponent implements CellComponent {
  @HostBinding('class') hostClass = ['d-flex', 'gap-2'];

  @Input() cellConfig!: SeComplementaryActionsCellConfig;

  @Input() value: any;

  @Input() cell!: FlattenedCell;

  @Input() column!: Column;

  @Input() row!: FlattenedRow;
}
