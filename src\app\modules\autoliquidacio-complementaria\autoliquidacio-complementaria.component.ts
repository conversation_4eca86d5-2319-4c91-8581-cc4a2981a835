import { CommonModule } from '@angular/common';
import {
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  EventEmitter,
  Input,
  OnDestroy,
  Output,
} from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { TAX_MODEL } from '@core/models';
import { TranslateModule } from '@ngx-translate/core';
import { Subject, takeUntil } from 'rxjs';
import {
  Nullable,
  SeAlertModule,
  SeAlertType,
  SeButtonModule,
  SeDatepickerModule,
  SeFormControlErrorModule,
  SeInputModule,
  SeLinkModule,
  SePanelModule,
  SeRadioModule,
  SeSwitchModule,
  SeValidations,
} from 'se-ui-components-mf-lib';
import { UtilsService } from '../dades-document/services/utils.service';
import { AutoliquidacioComplementariaService } from './autoliquidacio-complementaria.service';

@Component({
  selector: 'app-autoliquidacio-complementaria',
  templateUrl: './autoliquidacio-complementaria.component.html',
  styles: [],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule,
    SeRadioModule,
    SeLinkModule,
    TranslateModule,
    SeInputModule,
    SeDatepickerModule,
    SePanelModule,
    SeButtonModule,
    SeAlertModule,
    SeSwitchModule,
    SeFormControlErrorModule,
  ],

  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class AutoliquidacioComplementariaComponent implements OnDestroy {
  private _numJustificantPattern: string | RegExp = '';

  @Input()
  get numJustificantPattern(): string | RegExp {
    return this._numJustificantPattern;
  }
  set numJustificantPattern(value: string | RegExp) {
    this._numJustificantPattern = value;
    this.setComplementaryValidators(value);
  }

  private _IMPOST: Nullable<TAX_MODEL>;

  @Input()
  get IMPOST(): Nullable<TAX_MODEL> {
    return this._IMPOST;
  }
  set IMPOST(value: Nullable<TAX_MODEL>) {
    this._IMPOST = value;
  }
  private _perdidaFiscal: boolean = true;

  @Input()
  get perdidaFiscal(): boolean {
    return this._perdidaFiscal;
  }
  set perdidaFiscal(value: boolean) {
    this._perdidaFiscal = value;
    if (!value) {
      this.clearAndResetFormControl('lostBenef');
    }
  }

  @Input() urlHelpComplementaries: Nullable<string>;

  @Output() backEvent: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() redirectSession = new EventEmitter<string>();

  SeAlertType = SeAlertType;
  autoCompForm: FormGroup = this.fb.group({
    lostBenef: [null, Validators.required],
    // Basic
    numJustificant: [],
    quotaLiquida: [],
    dataPresentacio: [],
    // Perdua
    dataTerminiPresentacio: [],
    dataIncompliment: [],
    dataFinTermini: [],
  });

  today: Date = new Date();

  private readonly unsubscribe: Subject<void> = new Subject();

  constructor(
    private readonly fb: FormBuilder,
    private readonly utilsService: UtilsService,
    private readonly autoliquidacioComplementariaService: AutoliquidacioComplementariaService,
  ) {
    this.autoliquidacioComplementariaService.redirect$
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((redirect) => {
        this.redirectSession.emit(redirect);
      });
  }

  ngOnDestroy(): void {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }

  showBasicComplementary(): boolean {
    return (
      (this.perdidaFiscal &&
        this.getFormControl('lostBenef')?.value !== null) ||
      !this.perdidaFiscal
    );
  }

  setComplementaryValidators(numJustificantPattern: string | RegExp): void {
    const validators = [
      Validators.required,
      Validators.pattern(numJustificantPattern),
    ];
    this.getFormControl('numJustificant')?.setValidators(validators);
    this.autoCompForm.updateValueAndValidity();
    this.addRequiredValidator('quotaLiquida', [
      Validators.required,
      Validators.min(0),
      Validators.max(100_000_000),
    ]);
    this.addRequiredValidator('dataPresentacio', [
      Validators.required,
      SeValidations.dateRange(
        null,
        this.today,
        '',
        'SE_TRIBUTS_MF.AUTOLIQUIDACIO_COMPLEMENTARIA.FORM_FIELDS.DATA_DARRERA_MAX_ERROR',
      ),
    ]);
  }

  setPerduaFiscalValidators(isPerduaBeneficiFiscal: boolean): void {
    this.autoCompForm.markAsUntouched();
    if (isPerduaBeneficiFiscal && this.perdidaFiscal) {
      this.addRequiredValidator('dataTerminiPresentacio');

      this.addRequiredValidator('dataIncompliment', [
        Validators.required,
        SeValidations.dateRange(
          null,
          this.today,
          '',
          'SE_TRIBUTS_MF.AUTOLIQUIDACIO_COMPLEMENTARIA.FORM_FIELDS.DATA_INCOMPLIMENT_MAX_ERROR',
        ),
      ]);
      this.addRequiredValidator('dataFinTermini');
    } else {
      this.clearAndResetFormControl('dataTerminiPresentacio');
      this.clearAndResetFormControl('dataIncompliment');
      this.clearAndResetFormControl('dataFinTermini');
    }
  }

  dataIncomplimentChange(): void {
    if (this.autoCompForm.value['dataIncompliment']) {
      const dataIncompliment = new Date(
        this.autoCompForm.value['dataIncompliment'],
      );
      const dataFinalizacio = new Date(
        dataIncompliment.setMonth(dataIncompliment.getMonth() + 1),
      );
      this.autoCompForm.get('dataFinTermini')?.setValue(dataFinalizacio);
    }
  }

  clearAndResetFormControl(name: string, reset = true): void {
    this.utilsService.clearAndResetFormControl(this.autoCompForm, name, reset);
  }

  addRequiredValidator(name: string, validators = [Validators.required]): void {
    this.utilsService.addRequiredValidator(this.autoCompForm, name, validators);
  }

  getFormControl(name: string): FormControl {
    return this.autoCompForm.get(name) as FormControl;
  }

  navigateBackwards(): void {
    this.backEvent.emit(true);
  }

  checkAutoComp(): void {
    this.autoCompForm.markAsDirty();
    this.autoCompForm.markAllAsTouched();

    if (this.autoCompForm.valid) {
      const numJustificant = this.autoCompForm.value['numJustificant'];
      const quotaTotal = this.autoCompForm.value['quotaLiquida'];
      const dataPresentacio = this.autoCompForm.value['dataPresentacio'];

      if (
        numJustificant &&
        typeof quotaTotal === 'number' &&
        dataPresentacio &&
        this.IMPOST
      ) {
        this.autoliquidacioComplementariaService.checkAutoComp({
          numJustificant,
          quotaTotal,
          dataPresentacio,
          impost: this.IMPOST,
        });
      }
    }
  }
}
