import { Component, Input } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { SeModal, SeModalService } from 'se-ui-components-mf-lib';

import {
  CalculInfo,
  ModalTaxRateDetail,
  MODEL,
  SummaryTemplate,
} from '../calculations.model';
import { CalculationsEndpointService } from '../calculations-endpoint.service';
import { ModalIntegratedQuotaCalculationDetailComponent } from './modal-integrated-quota-calculation-detail/modal-integrated-quota-calculation-detail.component';

@Component({
  selector: 'app-summary',
  templateUrl: './summary.component.html',
  styleUrls: ['./summary.component.scss'],
})
export class SummaryComponent {
  @Input() model: string = '';
  @Input() calculationInfo!: CalculInfo;
  @Input() modalTaxRateDetail: ModalTaxRateDetail | undefined;
  @Input() surcharge: number = 0.0;
  @Input() interest: number = 0.0;
  @Input() aplicarRecarrec: boolean = true;
  @Input() summaryTemplate: SummaryTemplate[] | undefined;
  @Input() useCommonSummaryTemplate: boolean = true;

  model_550 = MODEL.CINCUENTA;
  model_560 = MODEL.SESENTA;
  readonly model_540 = MODEL.CUARENTA;
  resultingTax: number = 0.0;

  constructor(
    private calculationService: CalculationsEndpointService,
    private modalService: SeModalService,
    private translateService: TranslateService,
  ) {}

  protected getTotalValue(): number {
    this.resultingTax =
      this.calculationInfo.quotaTotal - this.calculationInfo.quotaLiquidades > 0
        ? this.calculationInfo.quotaTotal - this.calculationInfo.quotaLiquidades
        : 0.0;
    const surcharge = this.aplicarRecarrec ? this.surcharge : 0.0;
    const liquidades = this.calculationService.getLiquidateAmount(
      this.model,
      this.calculationInfo,
    );
    const total =
      surcharge + this.interest + this.calculationInfo.quotaTotal! - liquidades;
    return total < 0 ? 0 : total;
  }

  protected handleOpenModalIntegratedQuotaCalculationDetail(): void {
    const title = this.translateService.instant(
      'SE_TRIBUTS_MF.CALCULATIONS.MODAL_INTEGRATED_QUOTA.TITLE',
    );
    const data: SeModal = {
      title,
      closable: true,
      component: ModalIntegratedQuotaCalculationDetailComponent,
    };
    const component: ModalIntegratedQuotaCalculationDetailComponent =
      this.modalService.openModal(data).componentInstance;
    component.modalTaxRateDetail = this.modalTaxRateDetail;
  }
}
