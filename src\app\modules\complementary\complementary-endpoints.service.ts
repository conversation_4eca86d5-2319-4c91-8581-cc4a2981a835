import { Injectable } from '@angular/core';
import { map, type Observable } from 'rxjs';

import { SeHttpService } from 'se-ui-components-mf-lib';
import { environment } from 'src/environments/environment';
import type {
  AutoliquidacioComplementaria,
  GetAutoliquidacioByNumJustificantResponse,
  GetAutoliquidacioComplementariaResponse,
  GetAutoliquidacioCompRequest,
  GetAutoliquidacioRequest,
} from './models';

@Injectable({
  providedIn: 'root',
})
export class SeComplementaryEndpointsService {
  constructor(private httpService: SeHttpService) {
    // Intencionadamente vacío
  }

  public getAutoliquidacioComplementari(
    getAutoliquidacioCompRequest: GetAutoliquidacioCompRequest
  ): Observable<GetAutoliquidacioComplementariaResponse> {
    return this.httpService
      .post({
        method: 'post',
        baseUrl: environment.baseUrlTributs,
        url: '/complementari',
        body: getAutoliquidacioCompRequest,
      })
      .pipe(
        map((response: GetAutoliquidacioComplementariaResponse) => {
          response.content?.complementariList?.sort((a, b) =>
            this.sortComplementariListByDataPresentacioDESC(a, b)
          );
          return response;
        })
      );
  }

  private sortComplementariListByDataPresentacioDESC(
    complementaria_A: Partial<AutoliquidacioComplementaria>,
    complementaria_B: Partial<AutoliquidacioComplementaria>
  ): number {
    const dataPresentacio_A = new Date(complementaria_A?.dataPresentacio ?? '');
    const dataPresentacio_B = new Date(complementaria_B?.dataPresentacio ?? '');
    return dataPresentacio_B.getTime() - dataPresentacio_A.getTime();
  }

  public getAutoliquidacioByNumJustificant(
    getAutoliquidacioRequest: GetAutoliquidacioRequest
  ): Observable<GetAutoliquidacioByNumJustificantResponse> {
    return this.httpService.post({
      method: 'post',
      baseUrl: environment.baseUrlTributs,
      url: '/autoliquidacio',
      body: getAutoliquidacioRequest,
    });
  }
}
