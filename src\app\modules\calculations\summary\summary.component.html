<se-panel
  [id]="'summaryPanel'"
  [title]="'SE_TRIBUTS_MF.CALCULATIONS.TITLE' | translate"
  [colapsible]="true"
  panelTheme="primary"
>
  <div *ngIf="summaryTemplate">
    <ng-container *ngFor="let info of summaryTemplate">
      <p *ngIf="!info.value">{{ info.translate }}</p>
      <div *ngIf="info.value" class="mb-2">
        <div class="d-flex justify-content-between" [ngClass]="info.class">
          <span>{{ info.translate }}</span>
          <span>{{ info.value }}</span>
        </div>
        <div *ngIf="info.lineBreak">
          <hr class="calculations_hr" />
        </div>
      </div>
    </ng-container>
  </div>
  <!-- COMMON -->
  <ng-container
    *ngIf="useCommonSummaryTemplate"
    class="d-flex flex-column gap-2"
  >
    <div
      *ngIf="model === model_540"
      class="d-flex justify-content-between mb-2"
    >
      <div>
        <span>{{
          'SE_TRIBUTS_MF.CALCULATIONS.ROWS.TAX_RATE' | translate
        }}</span>
        <se-button
          class="ms-3"
          (onClick)="handleOpenModalIntegratedQuotaCalculationDetail()"
          [btnTheme]="'link'"
          >{{
            'SE_TRIBUTS_MF.CALCULATIONS.SEE_CALCULATION_DETAIL' | translate
          }}</se-button
        >
      </div>
      <span>{{
        modalTaxRateDetail?.quotaTributaria || 0
          | currency: 'EUR' : 'symbol' : '1.2-2'
      }}</span>
    </div>

    <div
      *ngIf="model === model_550"
      class="d-flex justify-content-between mb-2"
    >
      <span>{{
        'SE_TRIBUTS_MF.CALCULATIONS.ROWS.QUARTERLY_PAYMENTS' | translate
      }}</span>
      <span>{{
        calculationInfo.quotaLiquidadesTrimestri || 0
          | currency: 'EUR' : 'symbol' : '1.2-2'
      }}</span>
    </div>

    <div class="d-flex justify-content-between mb-2">
      <span>{{
        'SE_TRIBUTS_MF.CALCULATIONS.ROWS.PREVIUSLY_PAID_CONTRIBUTIONS'
          | translate
      }}</span>
      <span>{{
        calculationInfo.quotaLiquidades || 0
          | currency: 'EUR' : 'symbol' : '1.2-2'
      }}</span>
    </div>
    <div class="d-flex justify-content-between mb-2">
      <span>{{
        'SE_TRIBUTS_MF.CALCULATIONS.ROWS.RESULTING_TAX' | translate
      }}</span>
      <span>{{ resultingTax | currency: 'EUR' : 'symbol' : '1.2-2' }}</span>
    </div>
    <div class="d-flex justify-content-between mb-2">
      <span>{{
        'SE_TRIBUTS_MF.CALCULATIONS.ROWS.EXTEMPORANEITY_SURCHARGE' | translate
      }}</span>
      <span>{{ surcharge || 0 | currency: 'EUR' : 'symbol' : '1.2-2' }}</span>
    </div>
    <div class="d-flex justify-content-between mb-2">
      <span>{{
        'SE_TRIBUTS_MF.CALCULATIONS.ROWS.INTEREST_LATE_PAYMENT' | translate
      }}</span>
      <span>{{ interest || 0 | currency: 'EUR' : 'symbol' : '1.2-2' }}</span>
    </div>
  </ng-container>
</se-panel>
<div class="total-card mt-4">
  <ng-icon
    class="tooltip-icon"
    name="matInfo"
    [pTooltipAccessible]="'SE_TRIBUTS_MF.TOTAL.TOOLTIP' | translate"
  ></ng-icon>
  <span>{{ 'SE_TRIBUTS_MF.TOTAL.TITLE' | translate }}</span>
  <span>{{ getTotalValue() || 0 | currency: 'EUR' : 'symbol' : '1.2-2' }}</span>
</div>
