import { Injectable } from '@angular/core';
import { FormGroup, Validators } from '@angular/forms';
import {
  DateUtilsService,
  Nullable,
  SeValidations,
} from 'se-ui-components-mf-lib';
import { AutoliquidacionsComplementariaPerduaFiscalData } from './autoliquidacions-complementaria-perdua-fiscal.model';

@Injectable({
  providedIn: 'root',
})
export class AutoliquidacionsComplementariaPerduaFiscalService {
  today = new Date();

  constructor(private readonly dateUtilsService: DateUtilsService) {}

  setValidators(
    autoPerduaFiscalForm: FormGroup,
    data: AutoliquidacionsComplementariaPerduaFiscalData,
  ): void {
    autoPerduaFiscalForm
      .get('dataTerminiPresentacio')
      ?.setValidators([
        Validators.required,
        SeValidations.dateRange(
          this.dateUtilsService.parseDate(data.dataMeritacio)!,
          this.today,
          'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_COMPLEMENTARIA_PERDUA_FISCAL.TERMINI_PRESENTACIO_DATE_MIN_ERROR',
          'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_COMPLEMENTARIA_PERDUA_FISCAL.TERMINI_PRESENTACIO_DATE_MAX_ERROR',
        ),
      ]);
    autoPerduaFiscalForm
      .get('dataIncompliment')
      ?.setValidators([
        Validators.required,
        SeValidations.dateRange(
          this.dateUtilsService.parseDate(data.dataMeritacio)!,
          this.today,
          'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_COMPLEMENTARIA_PERDUA_FISCAL.DATA_INCOMPLIMENT_MIN_ERROR',
          'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_COMPLEMENTARIA_PERDUA_FISCAL.DATA_INCOMPLIMENT_MAX_ERROR',
        ),
        SeValidations.dateRange(
          this.dateUtilsService.parseDate(
            data.complementaria.dataTerminiPresentacio,
          )!,
          this.today,
          'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_COMPLEMENTARIA_PERDUA_FISCAL.DATA_INCOMPLIMENT_MIN_ERROR_2',
          'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_COMPLEMENTARIA_PERDUA_FISCAL.DATA_INCOMPLIMENT_MAX_ERROR',
        ),
      ]);
    autoPerduaFiscalForm
      .get('dataFinTermini')
      ?.setValidators([
        Validators.required,
        SeValidations.dateRange(
          this.dateUtilsService.parseDate(data.dataMeritacio)!,
          null,
          'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_COMPLEMENTARIA_PERDUA_FISCAL.DATA_FINALIZACIO_MIN_ERROR',
        ),
        SeValidations.dateRange(
          this.dateUtilsService.parseDate(data.dataFinalitzacioTerme)!,
          null,
          'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_COMPLEMENTARIA_PERDUA_FISCAL.DATA_FINALIZACIO_MIN_ERROR_2',
        ),
        SeValidations.dateRange(
          this.dateUtilsService.parseDate(
            data.complementaria?.dataIncompliment,
          )!,
          null,
          'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_COMPLEMENTARIA_PERDUA_FISCAL.DATA_FINALIZACIO_MIN_ERROR_3',
        ),
      ]);
    autoPerduaFiscalForm
      .get('dataIniciComput')
      ?.setValidators([
        Validators.required,
        SeValidations.dateRange(
          this.calculateDataInici(data)!,
          this.today,
          'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_COMPLEMENTARIA_PERDUA_FISCAL.INTERESSOS.DATA_INICI_MIN_FORA',
          'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_COMPLEMENTARIA_PERDUA_FISCAL.INTERESSOS.DATA_INICI_MAX',
        ),
      ]);
    autoPerduaFiscalForm
      .get('dataFiComput')
      ?.setValidators([
        Validators.required,
        SeValidations.dateRange(
          autoPerduaFiscalForm.get('dataIniciComput')?.value,
          this.today,
          'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_COMPLEMENTARIA_PERDUA_FISCAL.INTERESSOS.DATA_FIN_MIN',
          'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_COMPLEMENTARIA_PERDUA_FISCAL.INTERESSOS.DATA_FIN_MAX',
        ),
      ]);
  }

  calculateDataInici(
    data: Nullable<AutoliquidacionsComplementariaPerduaFiscalData>,
  ): Nullable<Date> {
    const dataInici = this.dateUtilsService.parseDate(
      data?.complementaria?.dataTerminiPresentacio,
    );
    if (dataInici) {
      // Caundo sea fora termini se le añaden 12 meses
      dataInici.setMonth(dataInici.getMonth() + 12);
      dataInici.setDate(dataInici.getDate() + 1);
    }
    return dataInici;
  }
}
