import { Injectable } from '@angular/core';
import { Autoliquidacio } from '@core/models/autoliquidacio.model';
import { Observable } from 'rxjs';
import {
  SeHttpRequest,
  SeHttpResponse,
  SeHttpService,
} from 'se-ui-components-mf-lib';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class AutoliquidacionsEndpointsService {
  constructor(private readonly httpService: SeHttpService) {}

  getAutoliquidacions(
    autoliquidacions: string[],
  ): Observable<SeHttpResponse<Autoliquidacio[]>> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlTributs,
      url: `/autoliquidacions`,
      method: 'post',
      body: { autoliquidacions },
      spinner: false,
      clearExceptions: true,
    };
    return this.httpService.post(httpRequest);
  }

  deleteAutoliquidacio(
    idAutoliquidacio: string,
  ): Observable<SeHttpResponse<string>> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlTributs,
      url: `/autoliquidacion/${idAutoliquidacio}`,
      method: 'delete',
    };
    return this.httpService.delete(httpRequest);
  }

  downloadPdf(idAutoliquidacio: string): Observable<SeHttpResponse<string>> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlTributs,
      url: `/autoliquidacion/${idAutoliquidacio}/document/pdf`,
      method: 'get',
    };
    return this.httpService.get(httpRequest);
  }
}
