/* eslint-disable no-extra-boolean-cast */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { Injectable } from '@angular/core';
import { DatePipe } from '@angular/common';

@Injectable({
  providedIn: 'root'
})
export class UtilsService {

  getDateOrNull(date: Date | string | null | undefined, isUTC = false): Date | null {
    if (typeof date === "object") return date
    if (typeof date !== "string") return null
    if (isUTC) date = date.endsWith("Z") ? date : date + "Z"
    return new Date(date)
  }

  formatDateUTC(fecha: string | Date, onlyDate = true, format = 'yyyy-MM-dd'): string {
    if (fecha) {
      try {
        if (typeof fecha === "string") {
          const datepipe: DatePipe = new DatePipe('en-ES');
          return datepipe.transform(fecha, format) + ""
        } else if (typeof fecha === "object") {
          const datepipe: DatePipe = new DatePipe('en-ES');
          return datepipe.transform(fecha, format) + (!onlyDate ? '' : "T00:00:00.000")
        }
      } catch (error) {
        console.error(error)
       }
    }
    return "";
  }
}