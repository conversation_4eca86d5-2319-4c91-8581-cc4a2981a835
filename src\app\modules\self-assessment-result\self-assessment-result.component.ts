import {
  Component,
  EventEmitter,
  Input,
  Output,
  OnDestroy,
} from '@angular/core';
import {
  ButtonCallbackReturn,
  Cell,
  CellConfig,
  Column,
  FlattenedCell,
  FlattenedRow,
  Row,
  SeButton,
  SeDocumentsService,
  SeMessageService,
  WcComponentInput,
} from 'se-ui-components-mf-lib';
import { SelfAssessmentResultEndpointService } from './self-assessment-result-endpoint.service';
import { Subject, catchError, of, takeUntil } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { CellComponentKeys } from 'se-ui-components-mf-lib/lib/components/table/cells/cell-component-registry';
import {
  HttpResponseSelfAssessments,
  ResponseSelfAssessment,
  TableSelfassessmentData,
} from './self-assessment-result.model';
import { SelfAssessmentEstat } from '@core/models';
import { MenuItem } from 'primeng/api';
import { ResultActionsCellComponent } from './actions-cell-template/actions-cell-template.component';

@Component({
  selector: 'app-self-assessment-result',
  templateUrl: './self-assessment-result.component.html',
  styleUrls: ['self-assessment-result.component.scss'],
})
export class SelfAssessmentResultComponent implements OnDestroy {
  @Input() titleLabelPanel = 'SE_TRIBUTS_MF.SELF_ASSESSMENT_RESULT.TITLE';
  @Input() subtitleLabelPanel = '';
  @Input() emitDownloadDocument = false;
  @Input() emitSendDocument = false;
  @Input() showDownloadMenu = true;

  idDocuments: string[] = [];
  numJustificant: string = '';
  tableHeader: Column[] = [];
  tableData: Row[] = [];
  btnTheme: 'primary' | 'secondary' | 'onlyText' | 'danger' = 'primary';
  btnSize: 'large' | 'default' | 'small' = 'small';
  documentsWcInput: WcComponentInput | undefined;
  isPagat = false;
  showDocsActions: boolean = true;
  idAutoliquidacions: string[] = [];
  autoliquidacions: ResponseSelfAssessment[] = [];
  itemsDropDownload: MenuItem[] = [
    {
      label: this.translate.instant(
        'SE_TRIBUTS_MF.SELF_ASSESSMENT_RESULT.DOWNLOAD_MENU.DILIGENCIA_PRESENTACIO_PAGAMENT',
      ),
      command: (): void => {
        this.downloadDiligenciaPdf();
      },
    },
    {
      label: this.translate.instant(
        'SE_TRIBUTS_MF.SELF_ASSESSMENT_RESULT.DOWNLOAD_MENU.TOTA_DOCUMENTACIO',
      ),
      command: (): void => {
        this.downloadAllZip();
      },
    },
  ];

  emptyButtonTable: SeButton = {
    label: 'SE_TRIBUTS_MF.SELF_ASSESSMENT_RESULT.BUTTONS.ADD',
  };

  @Input() set selfassessments(value: string[]) {
    this.idAutoliquidacions = value;
    if (this.idDocuments.length > 0) return;
    this.getSummarySelfAssessments(value);
  }

  @Input() columns: string[] = [
    'justificant',
    'taxpayer',
    'date',
    'type',
    'total',
    'state',
  ];

  @Input() actions: MenuItem[] | undefined;

  @Output() payButtonEvent: EventEmitter<FlattenedCell> =
    new EventEmitter<FlattenedCell>();

  private unsubscribe: Subject<void> = new Subject();
  private destroyed$ = new Subject<void>();

  constructor(
    private seDocumentsService: SeDocumentsService,
    private selfAssessmentService: SelfAssessmentResultEndpointService,
    private translate: TranslateService,
    private seMessageService: SeMessageService,
  ) {
    console.log(
      'Webcomponent: SE Tributs > SelfAssessmentResultComponent > constructor',
    );
  }

  private getSummarySelfAssessments(idsSelfassessment: string[]): void {
    this.selfAssessmentService
      .getSummarySelfAssessments(idsSelfassessment)
      .pipe(takeUntil(this.unsubscribe))
      .subscribe({
        next: (result: HttpResponseSelfAssessments) => {
          if (result?.content && result?.content.length > 0) {
            this.isPagat = result.content.every(
              (auto) =>
                auto.estat === SelfAssessmentEstat.PAGAT ||
                (auto.estat === SelfAssessmentEstat.PRESENTAT &&
                  auto.quotaLiquida === 0),
            );
            this.showDocsActions = result.content.some(
              (auto) =>
                auto.estat !== SelfAssessmentEstat.PRESENTACIO_ERROR &&
                auto.estat !== SelfAssessmentEstat.PENDENT_PRESENTACIO,
            );
            this.initialiceTable();
            this.autoliquidacions = result.content;
            this.setSelfAssessmentsRows(result.content);
          } else {
            this.tableData = [];
          }
        },
        error: () => {
          this.tableData = [];
        },
      });
  }

  private setSelfAssessmentsRows = (list: ResponseSelfAssessment[]): void => {
    list.forEach((value) => this.addSummarySelfAssessment(value));
  };

  private addSummarySelfAssessment = (value: ResponseSelfAssessment): void => {
    const selfAssessment = value;
    this.idDocuments = [
      ...this.idDocuments,
      ...(selfAssessment.idDocuments ?? []),
      selfAssessment.idMfpt ?? [],
    ].flat();
    this.numJustificant = selfAssessment.numJustificant;
    this.tableData = [...this.tableData, this.generateRow(selfAssessment)];
  };

  private generateRow = (selfAssessment: ResponseSelfAssessment): Row => {
    let data: TableSelfassessmentData = new TableSelfassessmentData(
      selfAssessment,
    );
    data = {
      ...data,
      state: {
        value: this.getStat(selfAssessment.estat),
      },
    };

    return {
      data: {
        ...Object.fromEntries(
          Object.entries(data).map(([key, { value }]) => {
            if (key === 'state') {
              const tooltip = this.getErrorPament(data, value);
              return [
                key,
                {
                  value,
                  cellConfig: {
                    tooltip: tooltip.length,
                    tooltipText: tooltip,
                    ngStyle: {
                      color: this.getColorForState(data, value),
                    },
                  },
                },
              ];
            }
            return [key, { value }];
          }),
        ),
        ...this.getPayButton(selfAssessment.estat, data),
      },
    };
  };

  getErrorPament(
    data: TableSelfassessmentData,
    value: SelfAssessmentEstat,
  ): string {
    let tooltip = '';
    if (value.includes(SelfAssessmentEstat.PAGAMENT_ERROR)) {
      if (data.errors.value.length) {
        const error = data.errors.value[0];
        if (
          !this.translate
            .instant(`UI_COMPONENTS.MODAL_ERRORS.MAPPED_ERRORS.${error.code}`)
            .includes('UI_COMPONENTS.MODAL_ERRORS.MAPPED_ERRORS')
        ) {
          tooltip += `${this.translate.instant(
            `UI_COMPONENTS.MODAL_ERRORS.MAPPED_ERRORS.${error.code}`,
          )}`;
        }
      }
    }
    return tooltip;
  }

  getColorForState(
    data: TableSelfassessmentData,
    value: SelfAssessmentEstat,
  ): string {
    let color = '';
    if (value.includes(SelfAssessmentEstat.PRESENTAT)) {
      color =
        Number(data['total'].value) > 0
          ? 'var(--color-orange-300)'
          : 'var(--color-green-300)';
    } else if (
      value.includes(SelfAssessmentEstat.PAGAMENT_ERROR) ||
      value.includes(SelfAssessmentEstat.NOTIFICACIO_ERROR)
    ) {
      color = 'var(--color-red-400)';
    } else if (value.includes(SelfAssessmentEstat.PAGAT)) {
      color = 'var(--color-green-300)';
    }
    return color;
  }

  getStat(estatAuto: string): string {
    return estatAuto ? `COMMONS.SELF_ASSESSMENT_ESTATS.${estatAuto}` : '';
  }

  private initialiceTable(): void {
    this.tableHeader = this.columns.map((column) => {
      return {
        header: this.translate.instant(
          `SE_TRIBUTS_MF.SELF_ASSESSMENT_RESULT.HEADER.${column.toUpperCase()}`,
        ),
        key: column,
        resizable: false,
        cellComponentName: this.handleCellComponentName(column),
      };
    });
  }

  private handleCellComponentName(key: string): CellComponentKeys {
    const cellComponents: { [key: string]: CellComponentKeys } = {
      date: 'dateCellComponent',
      total: 'currencyCellComponent',
    };

    return cellComponents[key] ?? 'defaultCellComponent';
  }

  private handleCellConfig(
    key: string,
    data: TableSelfassessmentData,
  ): CellConfig {
    if (key.includes('actions')) {
      return {
        align: 'right',
        buttonCell: {
          buttonConfig: {
            label: this.translate.instant(
              'SE_TRIBUTS_MF.SELF_ASSESSMENT_RESULT.BUTTONS.PAY',
            ),
            disabled: Number(data.total.value) <= 0,
            btnTheme: 'primary',
          },
          buttonCallback: (
            row: FlattenedRow,
            cell: FlattenedCell,
            // column: Column
          ): Promise<ButtonCallbackReturn> => {
            return new Promise(() => {
              this.payButtonEvent.emit(cell);
            });
          },
        },
        actions: this.actions?.map((action) => ({
          ...action,
          data,
          disabled: action['checkDisabled']
            ? Number(data.total.value) <= 0
            : false,
        })),
      };
    }
    return {};
  }

  private getPayButton = (
    state: string,
    data: TableSelfassessmentData,
  ): { [key: string]: Cell } => {
    if (
      state === SelfAssessmentEstat.PRESENTAT ||
      state === SelfAssessmentEstat.PENDENT_PAGAMENT ||
      state === SelfAssessmentEstat.PAGAMENT_ERROR
    ) {
      if (this.tableHeader.length === this.columns.length) {
        this.tableHeader.push({
          header: `SE_TRIBUTS_MF.SELF_ASSESSMENT_RESULT.HEADER.ACTIONS`,
          size: 22,
          key: 'actions',
        });
      }

      return {
        actions: {
          value: '',
          cellComponent: ResultActionsCellComponent,
          cellConfig: this.handleCellConfig('actions', data),
        },
      };
    }

    return { actions: { value: '' } };
  };

  downloadDiligenciaPdf(): void {
    if (this.idDocuments.length > 0) {
      this.selfAssessmentService
        .downloadDiligenciaPdf(this.idDocuments[0])
        .pipe(
          takeUntil(this.unsubscribe),
          catchError(() => {
            this.captureErrorDiligencia();
            return of(undefined);
          }),
        )
        .subscribe((result) => {
          if (result?.content && result.content.base64File) {
            this.seDocumentsService.openFile(
              result.content.base64File,
              result.content.format,
              result.content.nom,
            );
          } else {
            this.captureErrorDiligencia();
          }
        });
    }
  }

  captureErrorDiligencia(): void {
    this.seMessageService.resetMessages();
    this.seMessageService.addMessages([
      {
        severity: 'error',
        title: 'UI_COMPONENTS.EXCEPTIONS.CODES.EXECUTION_NOK',
        subtitle:
          'UI_COMPONENTS.EXCEPTIONS.MSG.COMMONS.ERROR_WHILE_CALLING_DOCUMENTS_MS',
      },
    ]);
  }

  downloadAllZip(): void {
    if (this.autoliquidacions.length && this.idAutoliquidacions.length) {
      const docsNegoci = this.mapDocsNegoci(this.autoliquidacions);
      if (docsNegoci.length) {
        this.selfAssessmentService
          .downloadAllZipPadoct(docsNegoci)
          .pipe(takeUntil(this.unsubscribe))
          .subscribe((result) => {
            if (result?.content) {
              this.seDocumentsService.downloadBinariFile(
                result.content,
                'documentacio.zip',
              );
            }
          });
      } else {
        this.selfAssessmentService
          .downloadAllZip(this.idAutoliquidacions)
          .pipe(takeUntil(this.unsubscribe))
          .subscribe((result) => {
            if (result?.content && result.content.base64File) {
              this.seDocumentsService.openFile(
                result.content.base64File,
                result.content.format,
                'documentacio.zip',
              );
            }
          });
      }
    }
  }

  sendJustificant(event: Event): void {
    const email = (event as CustomEvent<string>).detail;
    if (this.autoliquidacions.length && this.idAutoliquidacions.length) {
      const docsNegoci = this.mapDocsNegoci(this.autoliquidacions);
      if (docsNegoci.length) {
        this.seDocumentsService
          .sendEmailDocumentIdsZip(email, docsNegoci)
          .pipe(takeUntil(this.unsubscribe))
          .subscribe();
      } else {
        this.selfAssessmentService
          .sendDocumentacio(email, this.idAutoliquidacions)
          .pipe(takeUntil(this.unsubscribe))
          .subscribe();
      }
    }
  }

  /**
   * Mapeo, filtro repetidos y posibles nullos de los id padocts
   * @param autoliquidacions
   * @returns
   */
  mapDocsNegoci(autoliquidacions: ResponseSelfAssessment[]): string[] {
    return [
      ...new Set(
        autoliquidacions
          .map((auto) => auto.docsNegoci)
          .filter((doc) => doc)
          .flat(),
      ),
    ];
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }
}
