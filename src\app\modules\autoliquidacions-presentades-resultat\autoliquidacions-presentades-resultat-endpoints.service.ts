import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import {
  SeHttpRequest,
  SeHttpResponse,
  SeHttpService,
} from 'se-ui-components-mf-lib';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class AutoPresResultatEndpointService {
  constructor(private readonly httpService: SeHttpService) {}

  downloadAllZipPadoct(request: string[]): Observable<SeHttpResponse> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlDocuments,
      url: `/padoct-zip`,
      method: 'post',
      body: request,
    };
    return this.httpService.post(httpRequest);
  }

  downloadJustificantPdf(
    idMFPT: string,
  ): Observable<SeHttpResponse<{ base64File: string }>> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlPresentacions,
      url: `/autoliquidacio/${idMFPT}/justificant`,
      method: 'get',
    };
    return this.httpService.get(httpRequest);
  }

  downloadDiligenciaPdf(idMFPT: string): Observable<SeHttpResponse> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlPresentacions,
      url: `/autoliquidacio/${idMFPT}/diligencia`,
      method: 'get',
    };
    return this.httpService.get(httpRequest);
  }

  copiarFormulari(idTramit: string): Observable<SeHttpResponse<string>> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlTributs,
      url: `/sessions-treball/${idTramit}/copiar`,
      spinner: false,
      method: 'post',
    };

    return this.httpService.post(httpRequest);
  }
}
