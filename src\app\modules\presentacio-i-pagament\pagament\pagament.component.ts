import { Location } from '@angular/common';
import { Component, OnInit, type OnD<PERSON>roy } from '@angular/core';
import { Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { SeProgressModal } from 'se-ui-components-mf-lib';
import { NgbModalRef } from '@ng-bootstrap/ng-bootstrap';

import { PresentacioIPagamentsEndpointsService } from '../presentacio-i-pagament-endpoints.service';
import { PaymentData, PaymentType } from './pagament.model';
import { PresentacioIPagamentsService } from '../presentacio-i-pagament.service';
import { StoreService } from '@core/services';
import { AppRoutes } from '@core/models/app-routes.enum';

@Component({
  selector: 'app-pagament',
  templateUrl: './pagament.component.html',
})
export class PagamentComponent implements OnInit, OnDestroy {
  paymentData: PaymentData | undefined;
  totalAmount: number = 0;
  presentacioMode: boolean = false;
  progressModal!: SeProgressModal;

  private destroyed$ = new Subject<void>();

  constructor(
    private storeService: StoreService,
    private router: Router,
    private location: Location,
    private translateService: TranslateService,
    private pagamentsEndpointsService: PresentacioIPagamentsEndpointsService,
    private presentacioIPagamentService: PresentacioIPagamentsService,
  ) {
    this.presentacioMode = Boolean(
      this.router.getCurrentNavigation()?.extras.state?.['presentacioMode'],
    );
  }

  async ngOnInit(): Promise<void> {
    this.pagamentsEndpointsService
      .createPaymentNoteFor([this.storeService.selfAssessmentId!])
      .pipe(takeUntil(this.destroyed$))
      .subscribe((idPagament) => {
        this.paymentData = { idPagament };
        this.storeService.paymentId = idPagament;
      });

    this.totalAmount = this.storeService.amountToPay!;

    this.progressModal = {
      interval: 10,
      message: await this.translateService.instant(
        'SE_GASOS_MF.MODULE_PRESENTACION_I_PAGAMENT.PRESENTACIO.MODAL_MESSAGE',
      ),
    };
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  handlePayButton(event: Event): void {
    const ev = event as CustomEvent<PaymentType>;

    if (ev?.detail == PaymentType.CUENTA) {
      this.presentacioIPagamentService.runPagament();
    }
  }

  goBack(): void {
    if (this.storeService.presentedAl) {
      this.router.navigate([AppRoutes.RESULT]);
    } else {
      this.location.back();
    }
  }

  onOpenPresentationModal = (event: Event): void => {
    const customEvent = event as CustomEvent<NgbModalRef>;
    const modalRef = customEvent.detail;

    this.presentacioIPagamentService.runPresentacio(modalRef, false);
  };
}
