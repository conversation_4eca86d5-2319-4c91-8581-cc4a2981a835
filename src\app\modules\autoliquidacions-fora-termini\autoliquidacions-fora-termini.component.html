<se-panel
  [colapsible]="true"
  [title]="
    'SE_TRIBUTS_MF.CALCUL.AUTOLIQUIDACIONS-FORA-TERMINI.TITLE' | translate
  "
  id="autoliquidacions-fora-termini-panel"
>
  <p>
    {{
      'SE_TRIBUTS_MF.CALCUL.AUTOLIQUIDACIONS-FORA-TERMINI.SUBTITLE_1'
        | translate
    }}
  </p>
  <p>
    {{
      'SE_TRIBUTS_MF.CALCUL.AUTOLIQUIDACIONS-FORA-TERMINI.SUBTITLE_2'
        | translate
    }}
  </p>
  <form *ngIf="componentForm" [formGroup]="componentForm">
    <!-- Requerimiento previo de Hacienda -->
    <div class="mb-2" *ngIf="showAdministracioRequeriment()">
      <hr />
      <div>
        <se-switch
          id="indRequerimentPrevi"
          [label]="
            'SE_TRIBUTS_MF.CALCUL.AUTOLIQUIDACIONS-FORA-TERMINI.AUTOLIQUIDACIO_REQUIREMENT_PREVI'
              | translate
          "
          formControlName="indRequerimentPrevi"
          (onToggle)="requerimentPreviChange($event)"
        >
        </se-switch>
      </div>
    </div>
    <!-- Recargo -->
    <div class="mb-2" *ngIf="showRecarrec()">
      <hr />
      <h5 class="text-md mb-2">
        {{
          'SE_TRIBUTS_MF.CALCUL.AUTOLIQUIDACIONS-FORA-TERMINI.RECARREC.TITLE'
            | translate
        }}
      </h5>
      <p
        [innerHTML]="
          'SE_TRIBUTS_MF.CALCUL.AUTOLIQUIDACIONS-FORA-TERMINI.RECARREC.SUBTITLE'
            | translate
        "
      ></p>
      <div class="my-3">
        <se-switch
          id="indAplicarRecarrer"
          [label]="
            'SE_TRIBUTS_MF.CALCUL.AUTOLIQUIDACIONS-FORA-TERMINI.RECARREC.SWITCH_RECARREC'
              | translate
          "
          formControlName="indAplicarRecarrer"
          (onToggle)="indAplicarRecarrerChange($event)"
        >
        </se-switch>
      </div>
      <div
        class="row"
        *ngIf="getFormControl('indAplicarRecarrer').value === true"
      >
        <div class="col-12 col-sm-6 col-md-3 col-lg-2 text-sm">
          <se-input
            id="tipusPercent"
            [disabled]="true"
            [label]="
              'SE_TRIBUTS_MF.CALCUL.AUTOLIQUIDACIONS-FORA-TERMINI.RECARREC.TIPUS'
                | translate
            "
            formControlName="tipusPercent"
          >
          </se-input>
        </div>
        <div
          class="col-12 col-sm-6 col-md-4 col-lg-4 pb-3 d-flex align-items-end text-sm"
        >
          <se-switch
            id="indAplicarReduccio"
            [label]="
              'SE_TRIBUTS_MF.CALCUL.AUTOLIQUIDACIONS-FORA-TERMINI.RECARREC.SWITCH_REDUCCIO'
                | translate
            "
            formControlName="indAplicarReduccio"
            (onToggle)="indAplicarReduccioChange($event)"
          >
          </se-switch>
        </div>
      </div>
    </div>
    <!-- Interessos -->
    <div class="mb-2" *ngIf="showInteressosDemoraForaTermini()">
      <hr />
      <h5 class="text-md mb-2">
        {{
          'SE_TRIBUTS_MF.CALCUL.AUTOLIQUIDACIONS-FORA-TERMINI.INTERESSOS.TITLE' +
            (showInteressosDemoraForaTermini() ? '_FORA_TERMINI' : '')
            | translate
        }}
      </h5>
      <p>
        {{
          'SE_TRIBUTS_MF.CALCUL.AUTOLIQUIDACIONS-FORA-TERMINI.INTERESSOS.SUBTITLE' +
            (getFormControl('indRequerimentPrevi').value === true
              ? ''
              : '_FORA_TERMINI') | translate
        }}
      </p>
      <div class="row">
        <div class="col-12 col-md-6 col-lg-4 d-flex column-gap-1">
          <se-datepicker
            id="dataFiTermini"
            [placeholder]="'dd/mm/aaaa'"
            formControlName="dataIniciComput"
            [label]="
              'SE_TRIBUTS_MF.CALCUL.AUTOLIQUIDACIONS-FORA-TERMINI.INTERESSOS.DATA_INICI_COMPUT'
                | translate
            "
            [showIcon]="true"
            (dateSelectedEvent)="dataIniciChange($event)"
            [disabled]="readonlyDataInici"
          >
          </se-datepicker>
          <div class="pt-4">
            <app-modify-save-buttons
              [(readonly)]="readonlyDataInici"
              (saveEvent)="saveData()"
              [isDisabled]="componentForm.invalid"
            ></app-modify-save-buttons>
          </div>
        </div>
        <div class="col-12 col-md-6 col-lg-4 d-flex column-gap-1">
          <se-datepicker
            id="dataFiTermini"
            [placeholder]="'dd/mm/aaaa'"
            formControlName="dataFiComput"
            [label]="
              'SE_TRIBUTS_MF.CALCUL.AUTOLIQUIDACIONS-FORA-TERMINI.INTERESSOS.DATA_FI_COMPUT'
                | translate
            "
            [showIcon]="true"
            [disabled]="readonlyDataFi"
          >
          </se-datepicker>
          <div class="pt-4">
            <app-modify-save-buttons
              [(readonly)]="readonlyDataFi"
              (saveEvent)="saveData()"
              [isDisabled]="componentForm.invalid"
            ></app-modify-save-buttons>
          </div>
        </div>
      </div>
    </div>
  </form>
</se-panel>
