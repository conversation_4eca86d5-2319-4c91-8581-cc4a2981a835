import { Component, EventEmitter, Input, Output } from '@angular/core';
import type { Nullable } from 'se-ui-components-mf-lib';
import { CalculTreeItem } from '../../models/calcul.model';
@Component({
  selector: 'app-calcul-tree-item',
  template: `
    <div
      class="calcul-tree-item"
      seDisclosure
      [seDisclosureTargetId]="childrenContainerId"
    >
      <span
        class="calcul-tree-item__key"
        [class.secondary]="item.visualHierarchyLevel === 'secondary'"
        [class.bold]="!!item.bold"
        [innerHTML]="item.key | translate"
      ></span>
      <div
        *ngIf="!item.value.hideDotLine"
        class="calcul-tree-item__separator"
      ></div>
      <div class="calcul-tree-item__value">
        <span *ngIf="item.value?.squareNumber" class="calcul-tree-item__square">
          {{ item.value.squareNumber }}
        </span>

        <button
          class="calcul-tree-item__clickable"
          *ngIf="item.value.clickable; else plainValue"
          [title]="item.value.titleClickable ?? '' | translate"
          [attr.translate]="item.value.translateNoAttr ? 'no' : undefined"
          (click)="onItemClick()"
        >
          {{ item.value.content | translate }}
        </button>
        <ng-template #plainValue>
          <span
            class="calcul-tree-item__key"
            [class.secondary]="item.visualHierarchyLevel === 'secondary'"
            [attr.translate]="item.value.translateNoAttr ? 'no' : undefined"
            [class.bold]="!!item.bold"
          >
            {{ item.value.content | translate }}
          </span>
        </ng-template>
      </div>
    </div>

    <div
      *ngIf="item.children"
      [attr.id]="childrenContainerId"
      class="calcul-tree-item__children"
    >
      <app-calcul-tree-view
        [items]="item.children"
        (itemClick)="itemClick.emit($event)"
      />
    </div>
  `,
  styleUrls: ['./calcul-tree-item.component.scss'],
})
export class CalculTreeItemComponent {
  @Input() item!: CalculTreeItem;

  @Output() itemClick = new EventEmitter<CalculTreeItem>();

  protected onItemClick(): void {
    if (!this.item) return;
    this.itemClick.emit(this.item);
  }

  protected get childrenContainerId(): Nullable<string> {
    const hasId = !!this.item?.id;
    const isFoldable = this.item?.foldable;
    const hasChildren = (this.item?.children ?? []).length > 0;

    // `null` significa que no se puede expandir/contraer
    if (!hasId || !isFoldable || !hasChildren) return null;

    return `${this.item?.id}-children`;
  }
}
