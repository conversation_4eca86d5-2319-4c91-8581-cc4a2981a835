import { SelfAssessmentEstat } from '@core/models';
import type { SubjectePassiu } from './subjecte-passiu.model';

export interface AutoliquidacioComplementaria {
  numJustificant: string;
  idDocuments: string[];
  idAutoliquidacio: string;
  quotaLiquida: number;
  quotaTributaria: number;
  subjectePassiu: SubjectePassiu;
  tipus: string;
  estat: SelfAssessmentEstat;
  dataPresentacio: string;
}
