import { ChangeDetector<PERSON><PERSON>, Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { Subject, lastValueFrom, map, take, takeUntil, tap } from 'rxjs';

import {
  SeAlertMessage,
  SeAlertType,
  SeAuthService,
  SeModalService,
  SeProgressModal,
} from 'se-ui-components-mf-lib';
import { MenuItem } from 'primeng/api';

import { ResultSelfAssessmentService } from './result-selfassessment.service';
import { HeaderInfoService } from 'src/app/core/services/header-info.service';
import { AlertSelfAssessment } from './result-self-assessment.model';
import { StoreService } from '@core/services';
import { PresentacioIPagamentsEndpointsService } from '../presentacio-i-pagament/presentacio-i-pagament-endpoints.service';
import { AutoliquidacioError } from '../presentacio-i-pagament/models/autoliquidacio.model';
import {
  SelfAssessmentState,
  SelfAssessmentStatus,
} from '@core/models/self-assessment-status.model';
import { AppRoutes } from '@core/models/app-routes.enum';

@Component({
  selector: 'app-result-selfassessment',
  templateUrl: './result-selfassessment.component.html',
  styleUrls: [],
})
export class ResultSelfAssessmentComponent implements OnInit, OnDestroy {
  selfAssessmentId: string | null = null;
  idDocuments: string[] | null = null;
  status: SelfAssessmentState | null = null;
  alertMessage: SeAlertMessage | null = null;
  alertDescription = '';

  private readonly familyProcedure = 'FAM3';
  private readonly procedureId = 'F3CAS19';
  private destroyed$: Subject<void> = new Subject();
  actions: MenuItem[] | undefined;
  protected dataLoaded = false;
  protected readonly headers = [
    'justificant',
    'taxpayer',
    'date',
    'total',
    'state',
  ];

  constructor(
    private storeService: StoreService,
    private router: Router,
    private modalService: SeModalService,
    private translateService: TranslateService,
    private pipEndpointsService: PresentacioIPagamentsEndpointsService,
    private resultSelfassessmentService: ResultSelfAssessmentService,
    private cdRef: ChangeDetectorRef,
    private authService: SeAuthService,
    private header: HeaderInfoService,
  ) {}

  ngOnInit(): void {
    this.selfAssessmentId = this.storeService.selfAssessmentId!;
    this.checkStatus();
    this.setupSurvey();
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  goToPagament = (): void => {
    this.router.navigate([AppRoutes.PAGAMENT]);
  };

  private checkStatus = async (): Promise<void> => {
    this.status = await lastValueFrom(
      this.pipEndpointsService
        .getStatusAutoliquidacio(this.selfAssessmentId!)
        .pipe(
          take(1),
          tap((response) => {
            if (response?.content) {
              const { message, description } = this.getAlert(
                response.content.estat,
                response.content.errors || [],
              );
              this.alertMessage = message;
              this.alertDescription = description;
              const state = response.content.estat as SelfAssessmentStatus;
              this.header.status = state;
              this.header.presentationDate =
                response.content.dataPresentacio || null;
              this.idDocuments = response.content.idDocuments || null;
            }
          }),
          map((response) => response?.content?.estat || null),
        ),
    );

    const amount = this.storeService.amountToPay;

    if (
      (this.status === SelfAssessmentState.PRESENTAT ||
        this.status === SelfAssessmentState.PENDENT_PAGAMENT ||
        this.status === SelfAssessmentState.PAGAMENT_ERROR) &&
      amount &&
      amount > 0
    ) {
      this.actions = await this.resultSelfassessmentService.getActions(
        this.idDocuments!,
      );
    }

    this.dataLoaded = true;
    if (this.status === SelfAssessmentState.PAGANT) {
      const progressModal: SeProgressModal = {
        interval: 15,
        message: this.translateService.instant(
          'SE_GASOS_MF.MODULE_PRESENTACION_I_PAGAMENT.PAGAMENT.MODAL_MESSAGE',
        ),
      };

      const modalRef = this.modalService.openProgressModal(
        progressModal.interval!,
        progressModal.message!,
      );

      modalRef.componentInstance.intervalOutput
        .pipe(takeUntil(this.destroyed$))
        .subscribe(() => {
          this.pipEndpointsService
            .getStatusAutoliquidacio(this.selfAssessmentId!)
            .pipe(take(1))
            .subscribe({
              next: (response) => {
                if (response?.content) {
                  this.header.status = response.content
                    .estat as SelfAssessmentStatus;
                  this.header.presentationDate =
                    response.content.dataPresentacio || null;

                  if (
                    response.content.estat === SelfAssessmentState.PAGAT ||
                    response.content.estat ===
                      SelfAssessmentState.PAGAMENT_ERROR ||
                    response.content.estat === SelfAssessmentState.ERROR
                  ) {
                    const { message, description } = this.getAlert(
                      response.content.estat,
                      response.content.errors || [],
                    );
                    this.alertMessage = message;
                    this.alertDescription = description;

                    modalRef.close();
                    this.reloadView();
                  }
                }
              },
              error: () => {
                modalRef.close();
              },
            });
        });
    }
  };

  private getAlert(
    status: SelfAssessmentState | undefined,
    errors: AutoliquidacioError[] = [],
  ): AlertSelfAssessment {
    if (!status) {
      return { message: null, description: '' };
    }

    const translation = `SE_GASOS_MF.MODULE_PRESENTACION_I_PAGAMENT.DOCUMENTATION`;
    const alert: SeAlertMessage = {
      title: '',
      type: SeAlertType.SUCCESS,
      list: [],
      id: status,
    };

    const alertSuccess = {
      ...alert,
      title: `${translation}.ALERT_TITLE_SUCCESS`,
    };

    const alertError = {
      ...alert,
      type: SeAlertType.ERROR,
    };

    switch (status) {
      case SelfAssessmentState.PAGAT:
        return {
          message: alertSuccess,
          description: `${translation}.ALERT_DESCRIPTION_PAGAT_SUCCESS`,
        };
      case SelfAssessmentState.PENDENT_PAGAMENT:
      case SelfAssessmentState.PRESENTAT:
        return {
          message: alertSuccess,
          description: `${translation}.ALERT_DESCRIPTION_PRESENTAT_PENDENT_SUCCESS`,
        };

      case SelfAssessmentState.PAGAMENT_ERROR:
      case SelfAssessmentState.ERROR:
        return {
          message: {
            ...alertError,
            title: `${translation}.ALERT_TITLE_PAGAMENT_ERROR`,
            list: this.getOneSiteErrors(errors),
          },
          description: `${translation}.ALERT_DESCRIPTION_PAGAMENT_ERROR`,
        };
      case SelfAssessmentState.PRESENTACIO_ERROR:
        return {
          message: {
            ...alertError,
            title: `${translation}.ALERT_TITLE_PRESENTACIO_ERROR`,
          },
          description: `${translation}.ALERT_DESCRIPTION_PRESENTACIO_ERROR`,
        };

      default:
        return { message: null, description: '' };
    }
  }

  private setupSurvey(): void {
    const eventDetail = {
      familia: this.familyProcedure,
      tramite: this.procedureId,
      querySelector: 'app-result-selfassessment',
      produccion:
        this.authService.getSessionStorageUser().environment === 'pro',
    };

    document.dispatchEvent(
      new CustomEvent('showSurveyEvent', { detail: eventDetail }),
    );
  }

  private getOneSiteErrors(errors: AutoliquidacioError[]): string[] {
    return errors.map(
      (error) => `UI_COMPONENTS.MODAL_ERRORS.MAPPED_ERRORS.${error.code}`,
    );
  }

  reloadView = (): void => {
    const aux = this.selfAssessmentId;
    this.selfAssessmentId = null;
    this.cdRef.detectChanges();
    this.selfAssessmentId = aux;
  };
}
