.spinner-container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  color: var(--color-primary-action);

  .lds-ring {
    display: inline-block;
    position: relative;
    width: 24px;
    height: 24px;

    .ring-segment {
      box-sizing: border-box;
      display: block;
      position: absolute;
      width: 24px;
      height: 24px;
      border: 2px solid var(--color-primary-action);
      border-radius: 50%;
      animation: lds-ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
      border-color: var(--color-primary-action) transparent transparent
        transparent;
    }

    .ring-segment:nth-child(1) {
      animation-delay: -0.45s;
    }

    .ring-segment:nth-child(2) {
      animation-delay: -0.3s;
    }

    .ring-segment:nth-child(3) {
      animation-delay: -0.15s;
    }
  }
}

@keyframes lds-ring {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
