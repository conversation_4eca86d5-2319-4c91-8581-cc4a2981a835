<div *ngIf="showComponent">
  <p>{{ 'SE_TRIBUTS_MF.SURCHARGES.DESCRIPTION_1' | translate }}</p>
  <p>{{ 'SE_TRIBUTS_MF.SURCHARGES.DESCRIPTION_2' | translate }}</p>
  <form [formGroup]="componentForm" class="d-flex flex-column gap-2">
    <div class="row align-items-center gap-2">
      <div *ngIf="showDeadLineDate" class="col-sm-8 col-md-5 col-lg-4">
        <se-datepicker
          id="deadLineDate"
          [label]="'SE_TRIBUTS_MF.SURCHARGES.DEADLINE_DATE' | translate"
          formControlName="deadLineDate"
          [minDate]="minCalendarDate"
          [showIcon]="true"
          placeholder="dd/mm/yyyy"
          [inline]="false"
          [disabled]="!handleDeadLineDate"
          selectionMode="single"
          (dateSelectedEvent)="deadLineDateChange($event)"
        >
        </se-datepicker>
      </div>
      <div class="col-sm-3 col-md-5 col-lg-7 mb-1">
        <se-button
          btnTheme="secondary"
          class="button-position"
          (onClick)="handleChangeDeadLineDate()"
        >
          {{ 'SE_TRIBUTS_MF.SURCHARGES.BUTTON_CHANGE' | translate }}
        </se-button>
      </div>
    </div>
    <div *ngIf="showSurchargeQuestion">
      <hr />
      <div class="row">
        <se-switch
          id="liquidateSurcharge"
          [label]="
            'SE_TRIBUTS_MF.SURCHARGES.PRIOR_REQUIREMENT_QUESTION' | translate
          "
          formControlName="isRequirement"
          (onToggle)="requirementChange($event)"
        >
        </se-switch>
      </div>
      <!-- SURCHARGE -->
      <ng-container *ngIf="showSurchargeBlock()">
        <p class="fs-5 mt-3">
          {{ 'SE_TRIBUTS_MF.SURCHARGES.SURCHARGE.TITLE' | translate }}
        </p>
        <p>
          {{
            'SE_TRIBUTS_MF.SURCHARGES.SURCHARGE.RECARREC_DESCRIPTION'
              | translate
          }}
          <a [href]="moreInformationUrl" target="_blank">{{
            'SE_TRIBUTS_MF.SURCHARGES.SURCHARGE.MORE_INFORMATION_LINK'
              | translate
          }}</a>
        </p>
        <se-switch
          id="liquidateSurcharge"
          [label]="
            'SE_TRIBUTS_MF.SURCHARGES.SURCHARGE.SWITCH_LABEL' | translate
          "
          formControlName="liquidateSurcharge"
          (onToggle)="liquidateSurchargeChange($event)"
        >
        </se-switch>
        <ng-container *ngIf="getLiquidateSurchargeValue()">
          <section class="row mt-3">
            <div class="col-12 col-lg-3">
              <se-input
                formControlName="baseCalculation"
                [label]="
                  'SE_TRIBUTS_MF.SURCHARGES.SURCHARGE.BASE_CALCULATION'
                    | translate
                "
                placeholder="0,00€"
                [readonly]="false"
                [inline]="false"
                [disabled]="true"
                type="text"
                id="baseCalculation"
                [currencyMode]="true"
                currencySymbol="€"
              ></se-input>
            </div>
            <div class="col-12 col-lg-2">
              <se-input
                formControlName="typeSurcharge"
                [label]="'SE_TRIBUTS_MF.SURCHARGES.SURCHARGE.TYPE' | translate"
                placeholder="0,00%"
                [readonly]="false"
                [inline]="false"
                [disabled]="true"
                type="text"
                id="typeSurcharge"
                [currencyMode]="true"
                currencySymbol="%"
              >
              </se-input>
            </div>
            <div class="col-12 col-lg-3 text-end">
              <se-input
                formControlName="amount"
                [label]="
                  'SE_TRIBUTS_MF.SURCHARGES.SURCHARGE.AMOUNT' | translate
                "
                placeholder="0,00€"
                [readonly]="false"
                [inline]="false"
                [disabled]="true"
                type="text"
                id="amount"
                [currencyMode]="true"
                currencySymbol="€"
              >
              </se-input>
            </div>
          </section>
          <se-switch
            id="applyDiscount"
            [label]="
              'SE_TRIBUTS_MF.SURCHARGES.SURCHARGE.SWITCH_REDUCTION_LABEL'
                | translate
            "
            formControlName="applyDiscount"
            (onToggle)="callSurchargeEndpoint()"
          >
          </se-switch>
        </ng-container>
      </ng-container>
      <!-- INTERES ON LATE PAYMENT -->
      <ng-container *ngIf="showLatePayment">
        <hr *ngIf="showSurchargeBlock()" />
        <p class="fs-5 mt-3">
          {{
            'SE_TRIBUTS_MF.SURCHARGES.INTEREST_LATE_PAYMENT.TITLE' | translate
          }}
        </p>
        <p>
          {{
            (componentForm.get('isRequirement')!.value
              ? 'SE_TRIBUTS_MF.SURCHARGES.SURCHARGE.INTEREST_DESCRIPTION_YES'
              : 'SE_TRIBUTS_MF.SURCHARGES.SURCHARGE.INTEREST_DESCRIPTION_NO'
            ) | translate
          }}
        </p>
        <section class="row">
          <div
            class="col-sm-12 col-md-12 col-lg-4 d-flex align-items-center flex-row gap-1"
          >
            <se-datepicker
              id="countingStartDate"
              class="w-100"
              [label]="
                'SE_TRIBUTS_MF.SURCHARGES.INTEREST_LATE_PAYMENT.COUNTING_START_DATE'
                  | translate
              "
              formControlName="countingStartDate"
              [showIcon]="true"
              placeholder="dd/mm/yyyy"
              [inline]="false"
              [disabled]="!handleCountingStartDate"
              selectionMode="single"
              (dateSelectedEvent)="countingDatesChange()"
            >
            </se-datepicker>
            <se-button
              btnTheme="secondary"
              (onClick)="handleChangeCountingStartDate()"
              class="mt-1"
            >
              {{ 'SE_TRIBUTS_MF.SURCHARGES.BUTTON_CHANGE' | translate }}
            </se-button>
          </div>
          <div
            class="col-sm-12 col-md-12 col-lg-4 d-flex flex-row align-items-center gap-1"
          >
            <se-datepicker
              id="countingEndDate"
              class="w-100"
              [label]="
                'SE_TRIBUTS_MF.SURCHARGES.INTEREST_LATE_PAYMENT.COUNTING_END_DATE'
                  | translate
              "
              formControlName="countingEndDate"
              [showIcon]="true"
              placeholder="dd/mm/yyyy"
              [inline]="false"
              [disabled]="!handleCountingEndDate"
              selectionMode="single"
              (dateSelectedEvent)="countingDatesChange()"
            >
            </se-datepicker>
            <se-button
              btnTheme="secondary"
              (onClick)="handleChangeCountingEndDate()"
              class="mt-1"
            >
              {{ 'SE_TRIBUTS_MF.SURCHARGES.BUTTON_CHANGE' | translate }}
            </se-button>
          </div>
          <div class="col-sm-12 col-md-12 col-lg-4 text-end">
            <se-input
              formControlName="import"
              [label]="
                'SE_TRIBUTS_MF.SURCHARGES.INTEREST_LATE_PAYMENT.IMPORT'
                  | translate
              "
              placeholder="0,00€"
              [readonly]="false"
              [inline]="false"
              type="text"
              id="import"
              [disabled]="true"
              [currencyMode]="true"
              currencySymbol="€"
              (dateSelectedEvent)="recalculateSurcharges()"
            >
            </se-input>
          </div>
        </section>
      </ng-container>
    </div>
  </form>
</div>
