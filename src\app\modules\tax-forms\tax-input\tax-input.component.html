<form [formGroup]="form" class="tax-input">
  <div *ngIf="label" class="col-3 tax-input__label">
    <span [class.invisible]="labelInvisible">{{ label }}</span>
  </div>

  <se-input
    class="col tax-input__value-input"
    [id]="id + 'value-input'"
    [formControlName]="'value'"
    [currencyMode]="true"
    [currencySymbol]="units"
    [decimals]="0"
    [attr.aria-label]="label"
  >
  </se-input>

  <div class="tax-input__header-label">
    <span>{{ headerLabels.pricePerUnitHeaderLabel }}</span>
  </div>

  <div *ngIf="!hideDates" class="col tax-input__dates">
    <span>
      {{ startDate | date : "dd/MM/yyyy" }} -
      {{ endDate | date : "dd/MM/yyyy" }}
    </span>
  </div>

  <div class="col tax-input__price-per-unit">
    <span>x {{ pricePerUnit | number : "1.2-99" }} €/{{ units }}</span>
  </div>

  <div class="tax-input__header-label--total">
    <span>{{ headerLabels.totalPriceHeaderLabel }}</span>
  </div>

  <se-input
    class="col tax-input__total-price-input"
    [id]="id + '-total-price-input'"
    [formControlName]="'totalPrice'"
    [currencyMode]="true"
    [labelAlign]="'right'"
    [attr.aria-label]="'total'"
  >
  </se-input>
</form>
