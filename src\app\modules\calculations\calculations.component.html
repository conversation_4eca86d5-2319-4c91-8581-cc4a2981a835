<div class="d-flex flex-column gap-4">
  <ng-container *ngIf="isResumePanelVisible && !!resumeList.length">
    <se-panel
      [id]="'SE_TRIBUTS_MF.CALCULATIONS.RESUME.TITLE' | translate"
      [title]="'SE_TRIBUTS_MF.CALCULATIONS.RESUME.TITLE' | translate"
      [colapsible]="true"
      panelTheme="primary"
      class="calculations__resum"
    >
      <div
        class="d-flex flex-direction-row justify-content-between align-items-center"
        *ngFor="let resume of resumeList"
      >
        <p>{{ resume.label }}</p>
        <p>{{ resume.amount }}</p>
      </div>
    </se-panel>
  </ng-container>

  <ng-container
    *ngIf="
      showSurchargeComponent && calculationInfoValue && requestPeriodInfoValue
    "
  >
    <se-panel
      [id]="'surchargePanelId'"
      [title]="'SE_TRIBUTS_MF.SURCHARGES.TITLE' | translate"
      [colapsible]="true"
      panelTheme="primary"
    >
      <app-surcharges
        [model]="requestPeriodInfoValue.model"
        [impost]="requestPeriodInfoValue.impost"
        [calculationInfo]="calculationInfoValue"
        (callInterestLatePayment)="callInterestLatePayment($event)"
        (sendData)="getDataForm($event)"
      ></app-surcharges>
    </se-panel>
  </ng-container>
  <ng-container *ngIf="requestPeriodInfoValue && calculationInfoValue">
    <app-summary
      [model]="requestPeriodInfoValue.model"
      [calculationInfo]="calculationInfoValue"
      [modalTaxRateDetail]="modalTaxRateDetail"
      [surcharge]="surcharge"
      [interest]="interest"
      [aplicarRecarrec]="aplicarRecarrec"
      [summaryTemplate]="summaryTemplate"
      [useCommonSummaryTemplate]="useCommonSummaryTemplate"
    ></app-summary>
  </ng-container>
</div>
