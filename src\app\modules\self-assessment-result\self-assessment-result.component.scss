:host ::ng-deep .app-self-assessment-result {

  .panel-self-assessment-result .p-panel .p-panel-content {
    padding: 0;

    p {
      padding: 1.5rem;
      margin-bottom: 0;
    }

    se-table .se-table-container {
      
      th.se-table__header-cell {
        border-top: 1px solid var(--color-gray-300);
      }

      tr:last-child td.se-table__cell {
        border-bottom: none;
      }
    }
  }

  &__custom-buttons {
    display: flex;

    & > div {
      padding-right: 0.5rem;
    }
  }
}
