import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LazyElementsModule } from '@angular-extensions/elements';
import { Routes, RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { SpinnerComponent } from 'se-ui-components-mf-lib';

import { environment } from 'src/environments/environment';
import { PresentacioComponent } from './presentacio/presentacio.component';
import { PagamentComponent } from './pagament/pagament.component';

const routes: Routes = [
  {
    path: '',
    component: PresentacioComponent,
    data: {
      title: 'SE_GASOS_MF.APP_TITLE',
      isElementVisible: false,
    },
  },
  {
    path: 'pagament',
    component: PagamentComponent,
    data: {
      title: 'SE_GASOS_MF.APP_TITLE',
      isElementVisible: false,
    },
  },
];

@NgModule({
  declarations: [PresentacioComponent, PagamentComponent],
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    RouterModule.forChild(routes),
    LazyElementsModule.forFeature({
      elementConfigs: [
        {
          tag: 'mf-presentacions-i-pagament',
          url: environment.mfPresentacionsURL,
          loadingComponent: SpinnerComponent,
          preload: false,
        },
        {
          tag: 'mf-pagaments-proces-pagament',
          url: environment.mfPagamentsURL,
          loadingComponent: SpinnerComponent,
          preload: false,
        },
      ],
    }),
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class PresentacioIPagamentModule {}
