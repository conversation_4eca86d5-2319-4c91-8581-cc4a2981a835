<section class="confirm-recover-draft">
  <se-alert
    *ngIf="alertMessage"
    [title]="alertMessage"
    type="info"
    [closeButton]="true"
  >
  </se-alert>
  <p>{{ "SE_TRIBUTS_MF.CONFIRM_RECOVER_DRAFT.TITLE" | translate }}</p>
  <form [formGroup]="componentForm" class="d-flex gap-3 flex-column">
    <se-radio
      [id]="'draft-option-1-radio-input'"
      [label]="'SE_TRIBUTS_MF.CONFIRM_RECOVER_DRAFT.OPTION_1' | translate"
      name="option-1"
      [value]="true"
      formControlName="useDraft"
      (valueChanged)="onValueChanged($event)"
    ></se-radio>
    <se-radio
      [id]="'draft-option-2-radio-input'"
      class="ml-0"
      [label]="'SE_TRIBUTS_MF.CONFIRM_RECOVER_DRAFT.OPTION_2' | translate"
      name="option-2"
      [value]="false"
      formControlName="useDraft"
      (valueChanged)="onValueChanged($event)"
    ></se-radio>
  </form>
</section>
