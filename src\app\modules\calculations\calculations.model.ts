export enum IMPOST {
  IIIMA = 'IIIMA',
}

export enum MODEL {
  CUARENTA = '540',
  CINCUENTA = '550',
  SESENTA = '560',
}

export interface RequestSurcharge {
  amount: number;
  startDate: string;
  finalDate: string;
  indReducedSurcharge: boolean;
}

export interface ResponseSurcharge {
  surcharge: number;
  surchargePercent: number;
}

export interface RequestPeriodInfo {
  impost: string;
  year: string;
  period: string;
  model: string;
}

export interface ResponsePeriodInfo {
  codiPeriode: string;
  dataIniciPeriode: string;
  dataFiPeriode: string;
  dataIniciPresentacio: string;
  dataFiPresentacio: string;
  dataFiVoluntaria: string;
  descripcioES: string;
  descripcioCA: string;
  codiImpost: string;
  model: string;
  exercici: number;
}

export interface RequestInterestCalculator {
  request: Interest;
  execute: boolean;
}

export type Interest = {
  amount: number[];
  startDate: string;
  finalDate: string;
  type: string;
};

interface interestInfo {
  amount: number;
  startPeriod: string;
  endPeriod: string;
  daysBetween: number;
  percentage: number;
  total: number;
}

export interface ResponseInterestCalculator {
  totalAmount: number;
  interestInfo?: interestInfo[];
}

export interface ModalTaxRateDetail {
  quotaTributaria: number;
  baseImposable: number;
  baseImposableFinsA: number;
  baseImposableFinsACalcul: number;
  restaBaseImposableFinsA: number;
  restaBaseImposableFinsACalcul: number;
  tipusAplicable: number;
  quotaResultante: number;
}

export interface CalculInfo {
  quotaTotal: number;
  quotaLiquidades: number;
  quotaLiquidadesTrimestri?: number;
}

export interface SummaryTemplate {
  class?: string;
  translate: string;
  value?: string | null;
  lineBreak?: boolean;
}

export interface FormData {
  amount: number;
  applyDiscount: boolean;
  baseCalculation: number;
  countingEndDate: Date;
  countingStartDate: Date;
  deadLineDate: Date;
  import: number;
  isRequirement: boolean;
  liquidateSurcharge: boolean;
  typeSurcharge: string;
  total: number;
}

export class SendFormData {
  model: string;
  dataFiTermini?: Date;
  dataIniciComput?: Date;
  dataFiComput?: Date;
  requerimentAdministracio?: boolean;
  quotasLiquidades: number; // calculation
  aplicarRecarrec?: boolean;
  percentajeRecarrec?: number;
  recarrec?: number;
  reduccioRecarrec?: boolean;
  interessos?: number;
  total: number; // calculation

  constructor(quotaLiquidades: number, formData?: FormData) {
    if (formData) {
      this.dataFiTermini = formData.deadLineDate;
      this.dataIniciComput = formData.countingStartDate;
      this.dataFiComput = formData.countingEndDate;
      this.requerimentAdministracio = formData.isRequirement ?? false;
      this.aplicarRecarrec = formData.liquidateSurcharge ?? null;
      this.reduccioRecarrec = formData.applyDiscount ?? null;
      this.percentajeRecarrec =
        Number(formData.typeSurcharge?.replace(',', '.').replace('%', '')) ??
        null;
      this.recarrec = formData.amount ?? 0;
      this.interessos = formData.import ?? 0;
    }
    this.model = '';
    this.total = 0;
    this.quotasLiquidades = quotaLiquidades;
  }
}

export interface ResumeData {
  quantity: number;
  amount: string;
  label: string;
}
