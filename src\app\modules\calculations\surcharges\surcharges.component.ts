import {
  Component,
  OnInit,
  Output,
  EventEmitter,
  Input,
  OnDestroy,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { CalculationsEndpointService } from '../calculations-endpoint.service';
import {
  RequestInterestCalculator,
  RequestPeriodInfo,
  RequestSurcharge,
  ResponseInterestCalculator,
  ResponsePeriodInfo,
  ResponseSurcharge,
  FormData,
  CalculInfo,
} from '../calculations.model';
import { Subject, takeUntil } from 'rxjs';
import { DatePipe } from '@angular/common';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-surcharges',
  templateUrl: './surcharges.component.html',
  styleUrls: ['./surcharges.component.scss'],
})
export class SurchargesComponent implements OnInit, OnDestroy {
  @Input() model: string = '';
  @Input() impost: string = '';
  @Input() callEndpoints: boolean = false;
  @Input() periodInfo?: RequestPeriodInfo;
  @Input() calculationInfo!: CalculInfo;

  @Output() callInterestLatePayment: EventEmitter<RequestInterestCalculator> =
    new EventEmitter<RequestInterestCalculator>();
  @Output() sendData: EventEmitter<FormData> = new EventEmitter<FormData>();
  protected componentForm: FormGroup;
  private unsubscribe: Subject<void> = new Subject();

  // Calendars
  protected handleDeadLineDate: boolean = false;
  protected handleCountingStartDate: boolean = false;
  protected handleCountingEndDate: boolean = false;
  protected minCalendarDate!: Date;

  // Parameters
  protected showDeadLineDate: boolean = true;
  protected showLatePayment: boolean = false;
  protected showComponent: boolean = true;
  protected showSurchargeQuestion: boolean = true;
  protected moreInformationUrl: string = `https://atc.gencat.cat/${this.translateService.currentLang}/dir/recarrecs`;
  private interestTotal: number = 0;
  private surchargeTotal: number = 0;

  constructor(
    private fb: FormBuilder,
    private calculationsService: CalculationsEndpointService,
    private datePipe: DatePipe,
    private translateService: TranslateService,
  ) {
    this.componentForm = this.fb.group({
      deadLineDate: [null, Validators.required],
      isRequirement: [false, Validators.required],
      // surcharge
      liquidateSurcharge: [false, Validators.required],
      baseCalculation: [null, Validators.required],
      typeSurcharge: [null, Validators.required],
      amount: [0, Validators.required],
      applyDiscount: [false, Validators.required],
      // interest on late payment
      countingStartDate: [null, Validators.required],
      countingEndDate: [new Date(), Validators.required],
      import: [0, Validators.required],
      // total
      total: [null],
    });
  }

  ngOnInit(): void {
    if (!this.callEndpoints) {
      this.calculationsService.periodObservable$
        .pipe(takeUntil(this.unsubscribe))
        .subscribe((response) => {
          this.handleResponsePeriodInfo(response);
        });
      this.calculationsService.surchargeObservable$
        .pipe(takeUntil(this.unsubscribe))
        .subscribe((response) => {
          this.handleSurchargeResponse(response);
        });
      this.calculationsService.interestObservable$
        .pipe(takeUntil(this.unsubscribe))
        .subscribe((response) => {
          this.handleResponseInterestLatePayment(response);
        });
    } else {
      this.callPeriodEndpoint();
    }
  }

  ngOnDestroy() {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }

  private callPeriodEndpoint() {
    if (this.periodInfo) {
      this.calculationsService
        .getPeriodData(this.periodInfo)
        .pipe(takeUntil(this.unsubscribe))
        .subscribe((data) => {
          this.handleResponsePeriodInfo(data.content);
          this.showComponent = this.calculationsService.voluntaryPeriodEnd(
            data.content.dataFiVoluntaria,
          );
          if (!this.showComponent) {
            this.emitData();
          }
        });
    }
  }

  private handleParameters(isRequirement: boolean) {
    this.componentForm.get('liquidateSurcharge')?.setValue(!isRequirement);
    this.componentForm.get('applyDiscount')?.setValue(!isRequirement);
    this.emitData();
  }

  protected liquidateSurchargeChange(value: boolean) {
    if (value) {
      this.componentForm.get('applyDiscount')?.setValue(true);
      this.recalculateSurcharges();
    } else {
      this.componentForm.get('amount')?.setValue(0);
      this.componentForm.get('import')?.setValue(0);
      this.showLatePayment = false;
      this.emitData();
    }
  }

  protected requirementChange(value: boolean) {
    this.getIniciComputDate();
    this.handleParameters(value);
    const liquidateSurcharge =
      this.componentForm.get('liquidateSurcharge')?.value;
    if (value) {
      this.componentForm.get('amount')?.setValue(0);
      this.loadInterestLatePayment();
    } else if (liquidateSurcharge) {
      this.componentForm.get('import')?.setValue(0);
      this.callSurchargeEndpoint();
      this.loadInterestLatePayment();
    } else {
      this.componentForm.get('amount')?.setValue(0);
      this.componentForm.get('import')?.setValue(0);
      this.showLatePayment = false;
      this.emitData();
    }
  }

  protected deadLineDateChange(event: Date | Date[]) {
    this.showSurchargeQuestion =
      (event as Date).setHours(0, 0, 0, 0) < new Date().setHours(0, 0, 0, 0);
    this.getIniciComputDate();
    if (this.showSurchargeQuestion) {
      if (this.componentForm.get('isRequirement')?.value) {
        this.loadInterestLatePayment();
      } else {
        this.recalculateSurcharges();
      }
    } else {
      this.getInterestCalculationInfo();
      this.callSurchargeEndpoint(false);
    }
  }

  protected countingDatesChange(): void {
    if (this.componentForm.get('isRequirement')?.value) {
      this.loadInterestLatePayment();
    } else {
      this.recalculateSurcharges();
    }
  }

  protected recalculateSurcharges() {
    this.callSurchargeEndpoint();
    this.loadInterestLatePayment();
  }

  protected callSurchargeEndpoint(execute?: boolean) {
    if (
      this.calculationInfo?.quotaTotal !== undefined &&
      this.calculationInfo?.quotaTotal !== null &&
      (execute === null || execute !== false)
    ) {
      const amount = this.getBaseCalculation();
      const request: RequestSurcharge = {
        amount: amount,
        startDate: this.datePipe.transform(
          this.componentForm.get('deadLineDate')!.value,
          'yyyy-MM-dd',
        )!,
        finalDate: this.datePipe.transform(
          this.componentForm.get('countingEndDate')!.value,
          'yyyy-MM-dd',
        )!,
        indReducedSurcharge: this.componentForm.get('applyDiscount')!.value,
      };
      this.calculationsService
        .getSurcharge(request)
        .pipe(takeUntil(this.unsubscribe))
        .subscribe((data) => {
          const response: ResponseSurcharge = data.content;
          this.handleSurchargeResponse(response);
        });
    } else if (!execute) {
      const response: ResponseSurcharge = {
        surcharge: 0,
        surchargePercent: 0,
      };
      this.handleSurchargeResponse(response);
    }
  }

  protected emitData() {
    this.sendData.emit(this.componentForm.getRawValue());
  }

  private handleSurchargeResponse(response: ResponseSurcharge) {
    this.componentForm
      .get('typeSurcharge')
      ?.setValue(response.surchargePercent.toFixed(2).replace('.', ','));
    this.componentForm.get('amount')?.setValue(response.surcharge);
    this.surchargeTotal = response.surcharge;
    this.componentForm
      .get('total')
      ?.setValue(
        this.calculationInfo.quotaTotal +
          this.surchargeTotal +
          this.interestTotal,
      );
    this.emitData();
  }

  private getIniciComputDate() {
    const date = new Date(this.componentForm.get('deadLineDate')?.value);
    if (!this.componentForm.get('isRequirement')?.value) {
      date.setFullYear(date.getFullYear() + 1);
    }
    date.setDate(date.getDate() + 1);
    date.setHours(0, 0, 0, 0);
    this.componentForm.get('countingStartDate')?.setValue(date);
  }

  private createInterestRequest(): RequestInterestCalculator {
    const initComput: Date = this.componentForm.get('countingStartDate')?.value;
    this.showLatePayment =
      new Date().setHours(0, 0, 0, 0) >= initComput.setHours(0, 0, 0, 0);
    const initDate = this.datePipe.transform(initComput, 'yyyy-MM-dd');
    const finalDate = this.datePipe.transform(
      this.componentForm.get('countingEndDate')?.value,
      'yyyy-MM-dd',
    );
    const amount = this.getBaseCalculation();
    return {
      request: {
        amount: [amount],
        startDate: initDate!,
        finalDate: finalDate!,
        type: 'D',
      },
      execute: this.showLatePayment,
    };
  }

  private getInterestCalculationInfo() {
    const { request, execute }: RequestInterestCalculator =
      this.createInterestRequest();

    if (!request || !execute) {
      this.calculationsService.emitInterestData({ totalAmount: 0.0 });
      return;
    }
    this.calculationsService
      .getInterestCalculator(request)
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((response) => {
        if (response && response.content.length > 0) {
          this.handleResponseInterestLatePayment(response.content[0]);
        }
      });
  }

  private handleResponseInterestLatePayment(
    response: ResponseInterestCalculator,
  ) {
    const data: ResponseInterestCalculator = response;
    this.componentForm.get('import')?.setValue(data.totalAmount);
    this.interestTotal = data.totalAmount;
    this.componentForm
      .get('total')
      ?.setValue(
        this.calculationInfo.quotaTotal +
          this.surchargeTotal +
          this.interestTotal,
      );
    this.emitData();
  }

  private handleResponsePeriodInfo(response: ResponsePeriodInfo) {
    this.componentForm.patchValue({
      deadLineDate: new Date(response.dataFiVoluntaria),
      baseCalculation: this.getBaseCalculation(),
    });
    this.minCalendarDate = new Date(response.dataFiVoluntaria);
    this.getIniciComputDate();
    this.requirementChange(false);
  }

  private getBaseCalculation(): number {
    const quotaTotal = Number(this.calculationInfo.quotaTotal.toFixed(2));
    const liquidades = Number(
      this.calculationsService
        .getLiquidateAmount(this.model, this.calculationInfo)
        .toFixed(2),
    );
    const difference = quotaTotal - liquidades;
    return difference < 0 ? 0.0 : Number(difference.toFixed(2));
  }

  protected handleChangeDeadLineDate() {
    this.handleDeadLineDate = !this.handleDeadLineDate;
  }
  protected handleChangeCountingStartDate() {
    this.handleCountingStartDate = !this.handleCountingStartDate;
  }
  protected handleChangeCountingEndDate() {
    this.handleCountingEndDate = !this.handleCountingEndDate;
  }
  protected showSurchargeBlock(): boolean {
    return this.componentForm.get('isRequirement')?.value === false;
  }
  protected getLiquidateSurchargeValue(): boolean {
    return this.componentForm.get('liquidateSurcharge')?.value;
  }

  private loadInterestLatePayment() {
    if (this.callEndpoints) {
      this.getInterestCalculationInfo();
    } else {
      this.callInterestLatePayment.emit(this.createInterestRequest());
    }
  }
}
