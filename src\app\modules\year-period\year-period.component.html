<se-panel
  [id]="'SE_TRIBUTS_MF.YEAR_PERIOD_COMPONENT.PANEL.TITLE' | translate"
  [title]="'SE_TRIBUTS_MF.YEAR_PERIOD_COMPONENT.PANEL.TITLE' | translate"
  [colapsible]="true"
  panelTheme="primary"
>
  <p>{{ labelDescription }}</p>
  <form [formGroup]="componentForm">
    <div class="row d-flex justify-content-start gap-4">
      <div class="col-12 col-sm-6 dropdown-exercici">
        <se-dropdown
          id="year"
          [label]="
            'SE_TRIBUTS_MF.YEAR_PERIOD_COMPONENT.PANEL.EXERCISE' | translate
          "
          [editable]="false"
          [options]="yearOptions"
          formControlName="year"
          (dropdownOutput)="onYearOutput($event)"
        ></se-dropdown>
      </div>
      <ng-container *ngIf="usePeriod">
        <div class="col-12 col-sm-6 dropdown-exercici">
          <se-dropdown
            id="period"
            [label]="
              'SE_TRIBUTS_MF.YEAR_PERIOD_COMPONENT.PANEL.PERIOD' | translate
            "
            [editable]="false"
            [options]="periodOptions"
            formControlName="period"
            (dropdownOutput)="onPeriodOutput()"
          ></se-dropdown>
        </div>
      </ng-container>
    </div>
  </form>
</se-panel>
