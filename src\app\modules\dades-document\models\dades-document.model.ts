import { SeHttpResponse } from 'se-ui-components-mf-lib';
import { SPAIN_CODE } from './shared.model';

export interface ListItem {
  id: string;
  label: string;
}

export interface ResponseDocumentType extends SeHttpResponse {
  content: ListItem[];
}

export interface RequestGetNotary {
  province: string;
  municipality: string;
  date: string;
}

export interface ResponseNotaryData extends SeHttpResponse {
  content: ListItem[];
}

export interface FormDocumentValues {
  documentType: string;
  documentType_NAME: string;
  documentDate: Date | string;
  pais: string;
  municipi: string;
  provincia: string;
  pais_NAME: string;
  municipi_NAME?: string;
  provincia_NAME?: string;
  notary?: string;
  notaryOld?: boolean;
  notary_NAME?: string;
  autor?: string;
  trib_jul?: string;
  reference?: string;
  PROT?: string;
  PROT_BIS?: string;
  documentInfo?: string;
}

export class RequestFormDocument {
  indAnterior1970?: boolean;
  tipusDocument?: string;
  dataDocument?: string;
  protocol?: string;
  protocolBis?: string;
  notariCuv?: string;
  notariNom?: string;
  pais?: string;
  provincia?: string;
  municipi?: string;
  autoritat?: string;
  referencia?: string;
  IndEstranger?: boolean;

  constructor(formValues: FormDocumentValues) {
    if (formValues) {
      this.tipusDocument = formValues.documentType;
      this.dataDocument = formValues.documentDate as string;
      this.notariCuv = formValues.notaryOld ? ANTERIOR1970 : formValues.notary;
      this.notariNom = formValues.notary_NAME;
      this.indAnterior1970 = !!formValues.notaryOld;
      this.pais = formValues.pais;
      this.provincia = formValues.provincia;
      this.municipi = formValues.municipi;
      this.protocol = formValues.PROT;
      this.protocolBis = formValues.PROT_BIS;
      this.autoritat = formValues.trib_jul ?? formValues.autor;
      this.referencia = formValues.reference;
      this.IndEstranger =
        formValues.pais !== SPAIN_CODE && formValues.pais !== null;
    }
  }
}

export const ANTERIOR1970 = 'X000002';
export const EXTRANGER_NOTARI = 'X000003';
