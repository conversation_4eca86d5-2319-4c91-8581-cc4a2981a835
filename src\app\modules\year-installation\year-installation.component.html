<div class="mt-4 d-flex flex-column pr-0 pl-0">
  <!--  YEAR - PERIOD -->
  <ng-container *ngIf="showYearPeriodCustomElement">
    <mf-tributs-year-period
      *axLazyElement
      [labelDescription]="
        'SE_GASOS_MF.MODULE_YEAR_INSTALLATION.PANEL_DESCRIPTION' | translate
      "
      [impost]="IMPOST"
      [yearValue]="yearChange"
      [usePeriod]="false"
      (onChange)="onYearChange($event)"
    />
  </ng-container>

  <se-panel
    class="mt-4"
    [id]="
      'SE_GASOS_MF.MODULE_YEAR_INSTALLATION.PANEL_INSTALLATION.TITLE'
        | translate
    "
    [title]="
      'SE_GASOS_MF.MODULE_YEAR_INSTALLATION.PANEL_INSTALLATION.TITLE'
        | translate
    "
    [colapsible]="false"
    [collapsed]="false"
    [panelTheme]="'primary'"
  >
    <p class="fw-bold">
      {{
        "SE_GASOS_MF.MODULE_YEAR_INSTALLATION.PANEL_INSTALLATION.DESCRIPTION_1"
          | translate
      }}
    </p>
    <p class="mb-0">
      {{
        "SE_GASOS_MF.MODULE_YEAR_INSTALLATION.PANEL_INSTALLATION.DESCRIPTION_2"
          | translate
      }}
    </p>
    <p>
      {{
        "SE_GASOS_MF.MODULE_YEAR_INSTALLATION.PANEL_INSTALLATION.DESCRIPTION_3"
          | translate
      }}<span class="email">{{ EMAIL }}</span>
    </p>
    <ng-container *ngIf="componentForm">
      <form [formGroup]="componentForm">
        <div class="row">
          <se-input
            class="col-12 col-md-4"
            formControlName="codEmis"
            [label]="
              'SE_GASOS_MF.MODULE_YEAR_INSTALLATION.PANEL_INSTALLATION.EMIS_CODE'
                | translate
            "
            [id]="'codEmisId'"
            [type]="'text'"
          ></se-input>
        </div>
      </form>
    </ng-container>
  </se-panel>

  <!-- COMPLEMENTARY -->
  <div class="my-3" *ngIf="isComplementarySearchComponentVisible">
    <mf-tributs-automatic-complementary
      *axLazyElement
      [isOptional]="true"
      [hideTableColumns]="removeComplementaryTableColumns"
      [searchRequestParameters]="searchComplementaryRequestParams"
      (complementaryButtonClick)="submitComplementary($event)"
      (automaticSearchEnd)="onAutomaticComplementarySearchEnd($event)"
      (formSearchEnd)="submitComplementary($event)"
      (payButtonClick)="onPayButtonClick($event)"
      (continueWithoutSearch)="onContinueWithoutSearch($event)"
    />
  </div>

  <!--  BUTTONS -->
  <section class="mt-3 d-flex justify-content-between flex-row">
    <se-button (onClick)="goBack()" [btnTheme]="'secondary'">
      {{ "UI_COMPONENTS.BUTTONS.PREVIOUS" | translate }}
    </se-button>
    <se-button [disabled]="!isButtonEnabled" (onClick)="submit()">
      {{ "UI_COMPONENTS.BUTTONS.NEXT" | translate }}
    </se-button>
  </section>
</div>
