import { Nullable } from 'se-ui-components-mf-lib';

export enum SelfAssessmentStatusEnum {
  IN_PROGRESS = 'IN_PROGRESS',
  PENDENT_PAGAMENT = 'PENDENT_PAGAMENT',
  PRESENTAT = 'PRESENTAT',
  PAGAT = 'PAGAT',
}

export interface SelfAssessmentStatusTranslations {
  STATE: {
    IN_PROGRESS: string;
    PRESENTAT: string;
    PAGAT: string;
    PENDENT_PAGAMENT: string;
  };
}

export type SelfAssessmentStatus = `${SelfAssessmentStatusEnum}`;

interface SelfAssessmentError {
  code: string;
  date: string;
  description: string;
  technicalCode: string;
  technicalDescription: string;
  trackingId: string;
  stackTrace: string;
}

interface SubjectePassiu {
  nif: string;
  nom: string;
}

export interface SelfAssessment {
  numJustificant?: string;
  idDocuments?: string[];
  idAutoliquidacio?: string;
  quotaLiquida?: number;
  quotaTributaria?: number;
  subjectePassiu?: SubjectePassiu;
  tipus?: string;
  estat?: SelfAssessmentState;
  dataPresentacio?: string;
  idMfpt?: string;
  errors?: SelfAssessmentError[];
}

export interface HeaderTagData {
  translations: SelfAssessmentStatusTranslations | string[];
  presentationDate: Nullable<string>;
  status: SelfAssessmentStatus;
}

export enum SelfAssessmentState {
  GENERAT = 'GENERAT',
  ESBORRANY = 'ESBORRANY',
  NO_PRESENTAT = 'NO_PRESENTAT',
  PENDENT_PRESENTACIO = 'PENDENT_PRESENTACIO',
  PRESENTANT = 'PRESENTANT',
  PRESENTACIO_ERROR = 'PRESENTACIO_ERROR',
  PRESENTAT = 'PRESENTAT',
  PAGAT = 'PAGAT',
  PAGANT = 'PAGANT',
  PENDENT_PAGAMENT = 'PENDENT_PAGAMENT',
  PAGAMENT_ERROR = 'PAGAMENT_ERROR',
  TRAMITAT = 'TRAMITAT',
  TRAMITANT = 'TRAMITANT',
  TRAMITACIO_ERROR = 'TRAMITACIO_ERROR',
  CONSOLIDANT = 'CONSOLIDANT',
  CONSOLIDAT = 'CONSOLIDAT',
  CONSOLIDACIO_ERROR = 'CONSOLIDACIO_ERROR',
  NOTIFICACIO_ERROR = 'NOTIFICACIO_ERROR',
  ERROR = 'ERROR',
}
