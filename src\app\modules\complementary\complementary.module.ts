import { LazyElementsModule } from '@angular-extensions/elements';
import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { TranslateModule } from '@ngx-translate/core';

import { NgIconsModule } from '@ng-icons/core';
import { matInfo } from '@ng-icons/material-icons/baseline';
import {
  SeAlertModule,
  SeButtonModule,
  SeDatepickerModule,
  SeFormControlErrorModule,
  SeInputModule,
  SeLinkModule,
  SePanelModule,
  SeRadioModule,
  SeSwitchModule,
  SeTableModule,
  SeTooltipAccessibleModule,
  SpinnerComponent,
} from 'se-ui-components-mf-lib';
import { environment } from 'src/environments/environment';
import { SeAutomaticComplementaryComponent } from './automatic-complementary';
import { SeAutomaticComplementarySearchFormComponent } from './automatic-complementary-search-form';
import { SeComplementaryActionsCellComponent } from './complementary-actions-cell';
import { SeManualComplementaryComponent } from './manual-complementary/manual-complementary.component';

@NgModule({
  declarations: [
    SeAutomaticComplementaryComponent,
    SeComplementaryActionsCellComponent,
    SeAutomaticComplementarySearchFormComponent,
    SeManualComplementaryComponent,
  ],
  imports: [
    CommonModule,
    BrowserAnimationsModule,
    ReactiveFormsModule,
    TranslateModule,
    LazyElementsModule.forFeature({
      elementConfigs: [
        {
          tag: 'mf-documents-download-document-button',
          url: environment.mfDocumentsURL,
          loadingComponent: SpinnerComponent,
        },
        {
          tag: 'mf-documents-send-documents-button',
          url: environment.mfDocumentsURL,
          loadingComponent: SpinnerComponent,
        },
        {
          tag: 'mf-documents-docs-actions',
          url: environment.mfDocumentsURL,
          loadingComponent: SpinnerComponent,
        },
      ],
    }),
    SePanelModule,
    SeButtonModule,
    SeAlertModule,
    SeTableModule,
    SeInputModule,
    SeDatepickerModule,
    NgIconsModule.withIcons({
      matInfo,
    }),
    SeSwitchModule,
    SeRadioModule,
    SeLinkModule,
    SeFormControlErrorModule,
    SeTooltipAccessibleModule,
  ],
  exports: [
    SeAutomaticComplementaryComponent,
    SeAutomaticComplementarySearchFormComponent,
    SeManualComplementaryComponent,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class SeComplementaryModule {}
