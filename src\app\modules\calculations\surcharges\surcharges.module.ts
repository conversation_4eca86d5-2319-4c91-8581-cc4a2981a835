import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { SurchargesComponent } from './surcharges.component';
import { SeButtonModule, SeDatepickerModule, SeInputModule, SePanelModule, SeRadioModule, SeSwitchModule} from 'se-ui-components-mf-lib';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';


@NgModule({
	declarations: [SurchargesComponent],
	imports: [
		CommonModule,
		TranslateModule.forChild(),
        FormsModule,
        ReactiveFormsModule,
        BrowserAnimationsModule,
        SePanelModule,
        SeSwitchModule,
		SeDatepickerModule,
		SeInputModule,
		SeButtonModule,
		SeRadioModule
	],
	bootstrap: [
		SurchargesComponent
	],
	exports: [SurchargesComponent]
})
export class SurchargesModule { }
