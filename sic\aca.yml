version: 2.0.0
info:
  version: 0.95.0-snapshot
  description: se-tributs-mf
global-env:
  - CONTAINER_DOCKERFILE_PATH: Dockerfile
  - CONTAINER_IMAGE_NAME: se-tributs-mf-job
  - VOLUME_NAME: shared-pvc
  - CONTENT_SOURCE: elements
  - CONTENT_DESTINATION: /data/html/mf/se-tributs-mf
components:
  - build:
      steps:
        - container:
            image:
              remote:
                name: registreimatges.sic.intranet.gencat.cat/gencat-sic-builders/node-builder:16.20.2
            resources:
              limits:
                cpu: 3000m
                memory: 3072Mi
              requests:
                cpu: 100m
                memory: 128Mi
          execution:
            commands:
              - npm install && npm run build:elements
    deployment:
      scm: https://git.intranet.gencat.cat/0205/orchestrators.git
      environments:
        - name: integration
          actions:
            deploy:
              steps:
                - execution:
                    env:
                      - DESCRIPTORS_PATH: se-tributs-mf/int
                      - DEPLOYMENT_NAME: se-tributs-mf
                      - DEPLOYMENT_WAIT: 1200
        - name: preproduction
          actions:
            deploy:
              steps:
                - execution:
                    env:
                      - DESCRIPTORS_PATH: se-tributs-mf/pre
                      - DEPLOYMENT_NAME: se-tributs-mf
                      - DEPLOYMENT_WAIT: 1200
        - name: production
          actions:
            deploy:
              steps:
                - execution:
                    env:
                      - DESCRIPTORS_PATH: se-tributs-mf/pro
                      - DEPLOYMENT_NAME: se-tributs-mf
                      - DEPLOYMENT_WAIT: 1200
notifications:
  email:
    recipients: [ <EMAIL> ]
