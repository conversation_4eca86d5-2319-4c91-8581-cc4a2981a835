<!-- TEMPLATES -->
<ng-template #customActionsTemplate>
  <ng-container *ngIf="lastTaxReturnDocumentsIds.length > 0">
    <mf-documents-docs-actions
      *axLazyElement
      [documentsIds]="lastTaxReturnDocumentsIds"
      [customFileName]="filename"
    />
  </ng-container>
</ng-template>
<!--/ TEMPLATES -->

<!-- VISIBLE CONTENT -->
<se-panel
  *ngIf="isVisible"
  [title]="title ?? '' | translate"
  [customActions]="customActionsTemplate"
  [id]="'complementaryPanelId'"
>
  <se-alert
    *ngIf="information"
    [type]="'info'"
    [closeButton]="false"
    [title]="information | translate"
  />

  <p *ngIf="description && !isSearchFormVisible">
    {{ description | translate }}
  </p>

  <div *ngIf="isOptional && isSearchFormVisible">
    <p class="bold">
      {{ 'SE_TRIBUTS_MF.COMPLEMENTARY.OPTIONAL.TITLE' | translate }}
    </p>
  </div>

  <div *ngIf="data.length > 0" class="mb-3">
    <se-alert
      [type]="'info'"
      [closeButton]="false"
      [title]="
        _manualInfoText
          ? _manualInfoText
          : ('SE_TRIBUTS_MF.COMPLEMENTARY.OPTIONAL.MANUAL_INFO'
            | translate: { model: searchRequestParameters?.model })
      "
    />
  </div>
  <div
    *ngIf="data.length > 0"
    class="border-top border-start border-end"
    [class.mb-3]="isSearchFormVisible"
  >
    <se-table [columns]="columns" [data]="data" />
  </div>

  <p *ngIf="description && isSearchFormVisible">
    {{ description | translate }}
  </p>

  <se-automatic-complementary-search-form
    *ngIf="isSearchFormVisible"
    [isOptional]="isOptional"
    [searchRequestParameters]="searchRequestParameters"
    (searchEnd)="formSearchEnd.emit($event)"
    (continueWithoutSearch)="continueWithoutSearch.emit($event)"
  />
</se-panel>
<!--/ VISIBLE CONTENT -->
