import { Injectable } from '@angular/core';

import {
  LoginCertificates,
  SeAuthService,
  SeUser,
} from 'se-ui-components-mf-lib';

@Injectable({
  providedIn: 'root',
})
export class UserService {
  private user: SeUser;

  constructor(private authService: SeAuthService) {
    this.user = this.authService.getSessionStorageUser();
  }

  /**
   * Devuelve el NIF del usuario que ha iniciado sesión. Si es persona
   * física devolverá el DNI y si es persona jurídica devolverá el
   * código de identificación fiscal.
   */
  public getNIF(): string {
    if (!this.user) return '';

    const id = this.isLegalEntity ? this.user.companyId : this.user.nif;

    return id ?? '';
  }

  /**
   * Devuelve el nombre del usuario que ha iniciado sesión. Si es
   * persona física devolverá el nombre y apellidos. Si es persona
   * jurídica devolverá la razón social.
   */
  public getName(): string {
    if (!this.user) return '';

    const name = this.isLegalEntity
      ? this.user.companyName
      : this.user.nombreCompleto;

    return name ?? '';
  }

  /**
   * Devuelve `true` si el usuario que ha iniciado sesión es una persona
   * jurídica y `false` en caso de que no (persona física).
   */
  private get isLegalEntity(): boolean {
    if (!this.user) return false;

    return (
      this.user.certificateType === LoginCertificates.JUR_PERSON ||
      this.user.certificateType === LoginCertificates.REPRE_JUR
    );
  }
}
