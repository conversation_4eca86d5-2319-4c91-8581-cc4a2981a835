import type { SearchSelfassessmentsRequestParameters } from './complementary-search-request-parameters.model';

export interface GetAutoliquidacioRequest
  extends SearchSelfassessmentsRequestParameters {
  /**
   * Identificador de justificante de la autoliquidación para buscar.
   */
  numJustificant: string;
  /**
   * Fecha de presentación de la autoliquidación para buscar.
   */
  dataPresentacio: string;
  /**
   * Importe total de la autoliquidación para buscar.
   */
  quotaTotal: number;
}
