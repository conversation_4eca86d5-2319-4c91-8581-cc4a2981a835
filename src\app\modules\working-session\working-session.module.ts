import { RouterModule, Routes } from '@angular/router';
import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { LazyElementsModule } from '@angular-extensions/elements';
import { TranslateModule } from '@ngx-translate/core';
import { SeButtonModule, SpinnerComponent } from 'se-ui-components-mf-lib';

import { environment } from 'src/environments/environment';
import { WorkingSessionComponent } from './working-session.component';

const routes: Routes = [
  {
    path: '',
    component: WorkingSessionComponent,
    data: {
      title: 'SE_GASOS_MF.APP_TITLE',
      isElementVisible: false,
    },
  },
];

@NgModule({
  declarations: [WorkingSessionComponent],
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    RouterModule.forChild(routes),
    LazyElementsModule.forFeature({
      elementConfigs: [
        {
          tag: 'mf-tributs-working-sessions',
          url: environment.mfTributsURL,
          loadingComponent: SpinnerComponent,
        },
      ],
    }),
    SeButtonModule,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class WorkingSessionModule {}
