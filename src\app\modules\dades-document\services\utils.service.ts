/* eslint-disable no-extra-boolean-cast */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { DatePipe } from '@angular/common';
import { Injectable } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { Cell, SeDropdownOption, SeValidations } from 'se-ui-components-mf-lib';

@Injectable({
  providedIn: 'root',
})
export class UtilsService {
  constructor(private translateService: TranslateService) {}

  findListById = (
    id: string,
    list: SeDropdownOption[],
  ): SeDropdownOption | undefined => list.find((e) => e.id === id);

  clearAndResetFormControl = (
    form: FormGroup,
    name: string,
    reset = true,
  ): void => {
    const control = form.get(name);

    if (reset) {
      this.clearFormControl(form, name);
    }

    control?.clearValidators();
    control?.setErrors(null);
    control?.updateValueAndValidity();
  };

  clearFormControl = (form: FormGroup, name: string): void => {
    const control = form.get(name);
    control?.reset(null);
    control?.markAsUntouched();
  };

  addRequiredValidator = (
    form: FormGroup,
    name: string,
    validators = [SeValidations.required()],
  ): void => {
    const control = form.get(name);
    control?.setValidators(validators);
    control?.updateValueAndValidity();
  };

  getStringIfNotNull = (
    value: string | null | undefined,
    prefix = '',
    sufix = '',
  ): string => (value ? prefix + value + sufix : '');

  getCellFormat = (value: any, sufix = ''): Cell => {
    return !!value ? { value: value + sufix } : { value: '--' };
  };

  getDateOrNull(
    date: Date | string | null | undefined,
    isUTC = false,
  ): Date | null {
    if (typeof date === 'object') return date;
    if (typeof date !== 'string') return null;
    if (isUTC) date = date.endsWith('Z') ? date : date + 'Z';
    return new Date(date);
  }

  formatDateUTC(
    fecha: string | Date,
    onlyDate = true,
    format = 'yyyy-MM-dd',
  ): string {
    if (fecha) {
      try {
        if (typeof fecha === 'string') {
          const datepipe: DatePipe = new DatePipe('en-ES');
          return datepipe.transform(fecha, format) + '';
        } else if (typeof fecha === 'object') {
          const datepipe: DatePipe = new DatePipe('en-ES');
          return (
            datepipe.transform(fecha, format) +
            (!onlyDate ? '' : 'T00:00:00.000')
          );
        }
      } catch (error) {
        console.error(error);
      }
    }
    return '';
  }

  isEmptyInputValue(value: any): boolean {
    /**
     * Check if the object is a string or array before evaluating the length attribute.
     * This avoids falsely rejecting objects that contain a custom length attribute.
     * For example, the object {id: 1, length: 0, width: 0} should not be returned as empty.
     */
    return (
      value === null ||
      ((typeof value === 'string' || Array.isArray(value)) &&
        (value as string).trim().length === 0)
    );
  }

  getTranslationOrErrorCode(key: string, errorKey: string): boolean {
    const translation = this.translateService.instant(key);
    return translation !== key && translation !== ''
      ? translation
      : this.translateService.instant(errorKey);
  }

  compareValuesNormalized(valueA: string, valueB: string): boolean {
    return (
      valueA
        .normalize('NFD')
        .replace(/\p{Diacritic}/gu, '')
        .toLowerCase() ===
      valueB
        .normalize('NFD')
        .replace(/\p{Diacritic}/gu, '')
        .toLowerCase()
    );
  }
}
