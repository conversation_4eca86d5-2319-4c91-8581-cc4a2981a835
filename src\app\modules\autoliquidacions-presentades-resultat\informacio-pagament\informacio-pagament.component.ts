import { Component } from '@angular/core';
import { SeButton } from 'se-ui-components-mf-lib';

@Component({
  selector: 'app-informacio-pagament',
  template: `
    <div class="app-informacio-pagament">
      <se-alert
        type="neutral"
        [title]="
          'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PRESENTADES.INFO_PAGAMENT.TITLE1'
            | translate
        "
        [collapsed]="true"
        [closeButton]="false"
        [showAlertIcon]="false"
        [collapseButton]="collapseButton"
        contentClass="flex-column"
        alertClass="padding-normal"
      >
        <div>
          <p>
            {{
              'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PRESENTADES.INFO_PAGAMENT.P1'
                | translate
            }}
          </p>
          <p>
            {{
              'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PRESENTADES.INFO_PAGAMENT.P2'
                | translate
            }}
          </p>
          <p>
            <b>
              {{
                'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PRESENTADES.INFO_PAGAMENT.TITLE2'
                  | translate
              }}
            </b>
          </p>

          <ul>
            <li>
              {{
                'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PRESENTADES.INFO_PAGAMENT.UL1.LI1'
                  | translate
              }}
            </li>
            <li>
              {{
                'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PRESENTADES.INFO_PAGAMENT.UL1.LI2'
                  | translate
              }}
            </li>
          </ul>
          <p>
            <b>
              {{
                'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PRESENTADES.INFO_PAGAMENT.TITLE3'
                  | translate
              }}
            </b>
          </p>
          <ul>
            <li>
              {{
                'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PRESENTADES.INFO_PAGAMENT.UL2.LI1'
                  | translate
              }}
            </li>
          </ul>
        </div>
      </se-alert>
    </div>
  `,
  styleUrls: ['./informacio-pagament.component.scss'],
})
export class InformacioPagamentComponent {
  collapseButton: SeButton = {
    label:
      'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PRESENTADES.INFO_PAGAMENT.MOSTRAR_MENYS',
    alternateLabel:
      'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PRESENTADES.INFO_PAGAMENT.MOSTRAR_MES',
    btnTheme: 'trueOnlyText',
  };
}
