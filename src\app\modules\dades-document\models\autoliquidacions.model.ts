import { EstatEsborrany, Notary, OperacioDraft } from "./shared.model"

export interface Autoliquidacions {
  estat: EstatEsborrany
  errors: ErrorAutoliquidacio[]
  dataAlta: string
  dataModificacio: string
  idAutoliquidacio: string
  idTramit: string
  numJustificant: string
  impost: string
  model: string
  operacions: OperacioDraft[]
  declaracioExempcio: boolean
  baseImposableTotal: number
  totalIngressar: number
  quotaLiquida: number
  presentacio: Presentacio
  tipus: string
  idioma: string
  versio: string
  dataAutoritzacio: string
  indDataForaDeTermini: boolean
  tipusDocument: string
  associatsFormulariClau: string
  protocol: string
  dataProtocol: string
  idDecDB: string
  notari: Notary
  indImportMax: boolean
  dataMeritacio: string
  dataFinalitzacioTerme: string
  indTeAdjunts: boolean
  observacions: string
}

export interface ErrorAutoliquidacio {
  date: string
  code: string
  description: string
  technicalCode: string
  technicalDescription: string
  trackingId: string
  stackTrace: string | null
}

export interface Presentacio {
  estat: "PRESENTAT",
  canviEstat: CaviEstat[],
  errors: [],
  dataAlta: string,
  dataModificacio: string,
  dataPresentacio: string,
  reintentPresentacio: string,
  idioma: string,
  adjunts: string,
  idMFPT: string,
  idPRPT: string,
  versio: string,
  dataValidacio: string,
  dec: string
}

export interface CaviEstat {
  estatAnterior: EstatEsborrany,
  estat: EstatEsborrany,
  dataCanvi: string
}

export interface ComplementariValues {
  numJustificant: number;
  quotaLiquida: number;
  dataPresentacio: Date;
  dataTerminiPresentacio: Date;
  dataIncompliment: Date;
  dataFinTermini: Date;
  indComplementaria: boolean;
  lostBenef: boolean;
}