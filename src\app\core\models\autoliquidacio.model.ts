import { SelfAssessmentEstat } from '@core/models';

export interface SubjectePassiu {
  nif: string;
  nom: string;
  uuid: string | null;
}

export interface Autoliquidacio {
  numJustificant: string;
  idAutoliquidacio: string;
  idDocuments: string[];
  docsNegoci: string[];
  errors: ErrorAutoliquidacio[];
  quotaLiquida: number;
  subjectePassiu: SubjectePassiu;
  tipus: string;
  estat: SelfAssessmentEstat;
  idMfpt: string;
  dataPresentacio: string | null;
  dataIncompliment: string | null;
  dataFinTermini: string | null;
  dataTerminiPresentacio: string | null;
  quotaTributaria: number;
  idPrpt: string;
  idTramit: string;
  calculs: Calculs;
}

export interface ErrorAutoliquidacio {
  date: string;
  code: string;
  description: string;
  technicalCode: string;
  technicalDescription: string;
}

export interface Calculs {
  quotaResultante: number;
  dataFinalitzacioTerme: string;
  indForaTermini: boolean;
  baseImposable: number;
  reduccio: number;
  reduccionPercent: number;
  baseLiquidable: number;
  tipusImpositiuPerc: number;
  quotaIntegra: number;
  bonificacio: number;
  bonificacioPercent: number;
  quotaLiquida: number;
  quotesLiquidadesAnteriorment: number;
  totalIngressar: number;
  indRequerimentPrevi: boolean;
  interessosDemora: InteressosDemora;
  interessosForaTermini: InteressosForaTermini;
  recarrec: Recarrec;
}

export interface InteressosDemora {
  interessos: number;
  percent: number;
  dataIniciComput: string;
  dataFiComput: string;
  baseCalcul: number;
}

export interface InteressosForaTermini {
  interessos: number;
  percent: number;
  dataIniciComput: string;
  dataFiComput: string;
  baseCalcul: number;
}

export interface Recarrec {
  indRecarrec: boolean;
  totalRecarrec: number;
  percentaje: number;
  checkReduccio: boolean;
  dataPresentacioRecarrec: string;
  dataLimit: string;
}

export interface CalculErrors {
  idCalcul: string;
  errors: ErrorAutoliquidacio[];
}
