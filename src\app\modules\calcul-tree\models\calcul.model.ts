/**
 * Modelo que representa el valor de un elemento individual de la lista resumen
 * de cálculos de la pantalla "Càlcul".
 */
export interface CalculTreeItemValue {
  /**
   * Opcional. Número de la casilla del modelo tributario al que se refiere el
   * valor. Puede ser cualquier texto, que se mostrará al principio de
   * `content` y estará rodeado por un borde.
   */
  squareNumber?: string;
  /**
   * Opcional. Permite ocultar la linea de puntos que une el texto con el valor
   */
  hideDotLine?: boolean;
  /**
   * Obligatorio. Texto que representa el valor del elemento de cálculo. Puede ser
   * cualquier texto y se mostrará después de `squareNumber` (si se se ha
   * establecido).
   */
  content: string;
  /**
   * Opcional. Si se establece como `true`, el texto de la propiedad
   * `content` se mostrará como un elemento sobre el que se puede pulsar.
   * Cuando se pulse, se emitirá un evento.
   */
  clickable?: boolean;
  /**
   * Opcional. title para el elemento clicable para dar contexto al usuario
   * sobre la acción que se realizará al pulsar sobre el elemento.
   */
  titleClickable?: string;
  /**
   * Opcional. Añade translate="no" para que el valor no pueda ser traducido
   * por los navegadores.
   */
  translateNoAttr?: boolean;
}

/**
 * Modelo que representa un elemento individual de la lista resumen de cálculos
 * de la pantalla "Càlcul". Cada elemento puede tener a su vez subelementos
 * representados por la propiedad `children`, que consiste en una lista
 * recursiva de este mismo tipo.
 *
 * Es importante tener en cuenta que si se desea que la lista de subelementos
 * se pueda colapsar y expandir, hay que incluir la propiedad `foldable` con
 * valor `true` en la raíz del objeto, en cuyo caso debe incluirse también la
 * propiedad `id` para garantizar que el HTML resultante sea correcto y
 * accesible.
 */
export interface CalculTreeItem {
  /**
   * Obligatorio. Atributo `id` del elemento que servirá para construir
   * correctamente el HTML y que su accesibilidad sea correcta. Además, permite
   * distiguir el elemento sobre el que se ha pulsado en caso de que
   * `value.clickable` sea `true`.
   */
  id: string;
  /**
   * Obligatorio. Clave del elemento: un texto que se mostrará en la parte
   * izquierda.
   */
  key: string;
  /**
   * Obligatorio. Valor del elemento: su contenido se mostrará en la parte
   * derecha.
   */
  value: CalculTreeItemValue;
  /**
   * Opcional. Lista de subelementos que se mostrarán debajo del elemento con
   * una cierta indentación por la parte izquierda. Si se establece la
   * propiedad `foldable` a `true` se podrá expandir y contraer el conjunto de
   * subelementos.
   */
  children?: CalculTreeItem[];
  /**
   * Opcional. Si es `true` entonces el conjunto de subelementos
   * (si se establece) podrá ser contraído y expandido por el usuario.
   */
  foldable?: boolean;
  /**
   * Representa el nivel de jerarquía visual del elemento, puede ser:
   *
   * - `primary`: es un elemento principal. El texto se mostrará con el color
   *    de texto por defecto.
   * - `secondary`: es un elemento secundario. El texto se mostrará con un color
   *    más claro.
   * @default "primary"
   */
  visualHierarchyLevel?: 'primary' | 'secondary';
  /**
   * Opcional. Si es `true` se oscurecerá el texto del elemento.
   */
  bold?: boolean;
  /**
   * Opcional. Si es `true` se mostrará una línea de separación entre este
   * elemento y el siguiente y no se mostrara el contenido
   */
  separator?: boolean;
}
