import { NgModule } from '@angular/core';

//LIBRARY
import {
  SeAlertModule,
  SeButtonModule,
  SeDatepickerModule,
  SeDropdownModule,
  SeInputModule,
  SeModalModule,
  SePanelModule,
  SeSelectiveCardModule,
  SeSwitchModule,
  SeTableModule,
  SeTooltipAccessibleModule,
} from 'se-ui-components-mf-lib';

//COMPONENT
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { DadesDocumentComponent } from './dades-document.component';
import { FormDocumentComponent } from './form-document/form-document.component';
import { ModalModificarDocumentComponent } from './modal-modificar-document/modal-modificar-document.component';

@NgModule({
  declarations: [
    DadesDocumentComponent,
    FormDocumentComponent,
    ModalModificarDocumentComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule.forChild(),
    SeDropdownModule,
    SeDatepickerModule,
    SeAlertModule,
    SeSwitchModule,
    SeSelectiveCardModule,
    SeTableModule,
    SeModalModule,
    SePanelModule,
    SeInputModule,
    SeButtonModule,
    SeTooltipAccessibleModule,
  ],
  exports: [DadesDocumentComponent],
})
export class DadesDocumentModule {}
