import {
  Component,
  Input,
  OnDestroy,
  Output,
  EventEmitter,
} from '@angular/core';
import { SeDropdownOption, SeHttpResponse } from 'se-ui-components-mf-lib';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { YearPeriodEndpointsService } from './year-period-endpoint.service';
import { Subject, takeUntil } from 'rxjs';
import { Period, YearPeriod } from './year-period.model';

@Component({
  selector: 'app-year-period',
  templateUrl: './year-period.component.html',
  styleUrls: ['./year-period.component.scss'],
})
export class YearPeriodComponent implements OnDestroy {
  protected _impost: string = '';
  @Input() labelDescription: string = '';
  @Input() set impost(value: string) {
    this._impost = value;
    this.getExercici(value);
  }
  @Input() usePeriod: boolean = true;
  @Input() filterPeriodFunction:
    | ((periodes: SeDropdownOption[]) => SeDropdownOption[])
    | undefined;
  @Input() set yearValue(value: number) {
    if (value) {
      this._selectedYear = value;
      this.setYear(value);
    }
  }
  @Input() set periodValue(value: string) {
    if (value) {
      this._selectedPeriod = value;
      this.componentForm.get('period')?.setValue(value);
    }
  }
  @Output() onChange: EventEmitter<YearPeriod> = new EventEmitter<YearPeriod>();
  protected componentForm: FormGroup;

  // show info
  protected yearOptions: SeDropdownOption[] = [];
  protected periodOptions: SeDropdownOption[] = [];

  private _selectedPeriod: string | undefined;
  private _selectedYear: number | undefined;
  private destroyed$ = new Subject<void>();

  constructor(
    private fb: FormBuilder,
    private yearPeriodService: YearPeriodEndpointsService,
  ) {
    console.log('Webcomponent: SE Tributs > YearPeriodComponent > constructor');

    this.componentForm = this.fb.group({
      year: [null, Validators.required],
      period: [null, Validators.required],
    });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  private getExercici(impost: string): void {
    this.yearPeriodService
      .getExercici(impost)
      .pipe(takeUntil(this.destroyed$))
      .subscribe((result: SeHttpResponse) => {
        const years: Array<number> = result?.content;
        this.yearOptions = years
          .map((year) => ({ label: year.toString(), id: year }))
          .sort()
          .reverse();
        const currentYear = this._selectedYear ?? this.yearOptions[0]?.id;
        this.componentForm.get('year')?.setValue(currentYear);
        this.onYearOutput(Number(currentYear));
      });
  }

  private setYear(year: number): void {
    this.componentForm.get('year')?.setValue(year);
    this.onYearOutput(year);
  }

  private getPeriodes(impost: string, exercici: number): void {
    this.yearPeriodService
      .getPeriodes(impost, exercici)
      .pipe(takeUntil(this.destroyed$))
      .subscribe((result) => {
        this.periodOptions = [];
        const periodes: SeDropdownOption[] = (
          result?.content?.reverse() as Array<Period>
        ).map((period) => {
          return { label: period.label, id: period.id };
        });
        if (this.filterPeriodFunction) {
          this.periodOptions = this.filterPeriodFunction(periodes);
        } else {
          this.periodOptions = periodes;
        }

        // se setea después de tener la lista de los periodos, primero el valor de selectedPeriod
        this.componentForm.get('period')?.setValue(this.getSelectedPeriod());

        this.onPeriodOutput();
      });
  }

  onYearOutput(event: number): void {
    if (this.usePeriod) {
      this.getPeriodes(this._impost, event);
    } else {
      this.onChange.emit(this.componentForm.getRawValue());
    }
  }

  onPeriodOutput(): void {
    this.onChange.emit(this.componentForm.getRawValue());
  }

  private getSelectedPeriod(): string | number | boolean | undefined {
    const useSelectedPeriod: boolean =
      !!this._selectedPeriod &&
      this.periodOptions.some((period) => period.id === this._selectedPeriod);

    return useSelectedPeriod ? this._selectedPeriod : this.periodOptions[0]?.id;
  }
}
