import { CommonModule, CurrencyPipe } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';

import {
  SeAlertModule,
  SeButtonModule,
  SeExceptionViewerModule,
  SeModalModule,
  SePanelModule,
  SeSwitchModule,
  SeTableModule,
  SeTooltipAccessibleModule,
  SpinnerComponent,
} from 'se-ui-components-mf-lib';

import { LazyElementsModule } from '@angular-extensions/elements';
import { ReactiveFormsModule } from '@angular/forms';
import { environment } from '@environments/environment';
import { TranslateModule } from '@ngx-translate/core';
import { AutoPendentsTramitarComponent } from './autoliquidacions-pendents-tramitar.component';
import { ModalScoringComponent } from './modal-scoring/modal-scoring.component';
import { ModalTramitarComponent } from './modal-tramitar/modal-tramitar.component';
import { PanelBenComponent } from './panel-ben/panel-ben.component';

@NgModule({
  declarations: [
    AutoPendentsTramitarComponent,
    PanelBenComponent,
    ModalTramitarComponent,
    ModalScoringComponent,
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule.forChild(),
    SePanelModule,
    SeModalModule,
    SeTableModule,
    SeAlertModule,
    SeButtonModule,
    SeSwitchModule,
    SeExceptionViewerModule,
    SeTooltipAccessibleModule,
    LazyElementsModule.forFeature({
      elementConfigs: [
        {
          tag: 'mf-seguretat-scoring',
          url: environment.mfSeguretatURL,
          loadingComponent: SpinnerComponent,
        },
      ],
    }),
  ],
  exports: [],
  providers: [CurrencyPipe],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class AutoPendentsTramitarModule {}
