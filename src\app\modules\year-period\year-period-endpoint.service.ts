import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { SeHttpRequest, SeHttpResponse, SeHttpService } from 'se-ui-components-mf-lib';
import { environment } from 'src/environments/environment';

@Injectable({
    providedIn: 'root'
})
export class YearPeriodEndpointsService {

    constructor(
        private httpService: SeHttpService,
    
      ) { }
    
      //Request: GET > Exercici list 
      getExercici = (impost: string): Observable<SeHttpResponse> => {
        const httpRequest: SeHttpRequest = {
          baseUrl: environment.baseUrlTributs,
          url: `/${impost}/exercici`,
          method: 'get',
          clearExceptions: true
        };
        return this.httpService.get(httpRequest);
      }

      //Request: GET > Period list 
      getPeriodes = (impost: string, exercici: number): Observable<SeHttpResponse> => {
        const httpRequest: SeHttpRequest = {
          baseUrl: environment.baseUrlTributs,
          url: `/${impost}/exercici/${exercici}/periode`,
          method: 'get',
          clearExceptions: true
        };
        return this.httpService.get(httpRequest);
      }
}