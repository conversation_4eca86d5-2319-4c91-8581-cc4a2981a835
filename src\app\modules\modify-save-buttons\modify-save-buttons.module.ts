import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { SeButtonModule } from 'se-ui-components-mf-lib';
import { ModifySaveButtonsComponent } from './modify-save-buttons.component';

@NgModule({
  declarations: [ModifySaveButtonsComponent],
  imports: [CommonModule, ReactiveFormsModule, TranslateModule, SeButtonModule],
  exports: [ModifySaveButtonsComponent],
})
export class ModifySaveButtonsModule {}
