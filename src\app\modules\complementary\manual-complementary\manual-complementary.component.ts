import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import {
  <PERSON><PERSON><PERSON>er,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
import { Nullable, SeAlertType, SeValidations } from 'se-ui-components-mf-lib';
import { ComplementariValues } from '../../dades-document/models/autoliquidacions.model';
import { UtilsService } from '../../dades-document/services/utils.service';
import { TaxTypeEnum } from '../enums/tax.enum';

@Component({
  selector: 'app-manual-complementary',
  templateUrl: './manual-complementary.component.html',
  styles: [],
})
export class SeManualComplementaryComponent implements OnInit, OnDestroy {
  @Input() dataFinalizacio: Nullable<Date>;

  @Input() perdidaFiscal: boolean = false;
  @Input() urlHelpComplementaries: Nullable<string>;
  @Input() tax: Nullable<string>;
  @Input() complementariaText: string = '';
  @Input() indComplementariaDisabled: boolean = false;
  @Input() showTooltipAutoCompl: boolean = true;

  _dataModificacio: Nullable<Date>;

  @Input() set dataModificacio(dataModificacio: Date) {
    this._dataModificacio = dataModificacio;
    if (this.getFormControl('indComplementaria').value) {
      this.setComplementaryValidators(
        this.getFormControl('indComplementaria').value,
        false,
      );
      this.lostBenefToggle(this.getFormControl('lostBenef').value);
    }
  }

  get dataModificacio(): Nullable<Date> {
    return this._dataModificacio;
  }

  _complementariValues: Nullable<ComplementariValues>;

  @Input() set complementariValues(complementariValues: ComplementariValues) {
    this._complementariValues = complementariValues;
    this.indComplementariaToggle(complementariValues.indComplementaria, false);
    this.lostBenefToggle(complementariValues.lostBenef);
    this.componentForm.patchValue({
      numJustificant: complementariValues.numJustificant,
      quotaLiquida: complementariValues.quotaLiquida,
      dataPresentacio: complementariValues.dataPresentacio,
      dataTerminiPresentacio: complementariValues.dataTerminiPresentacio,
      dataIncompliment: complementariValues.dataIncompliment,
      dataFinTermini: complementariValues.dataFinTermini,
      indComplementaria: complementariValues.indComplementaria,
      lostBenef: complementariValues.lostBenef,
    });
  }

  get complementariValues(): Nullable<ComplementariValues> {
    return this._complementariValues;
  }

  @Output() componentFormOutput = new EventEmitter<FormGroup>();
  @Output() resetDataFinalizacio = new EventEmitter<boolean>();
  @Output() dataFinalizacioChanges = new EventEmitter<Date>();

  SeAlertType = SeAlertType;
  componentForm: FormGroup = this.fb.group({
    indComplementaria: [],
    numJustificant: [],
    quotaLiquida: [],
    dataPresentacio: [],
    lostBenef: [],
    dataTerminiPresentacio: [],
    dataIncompliment: [],
    dataFinTermini: [],
  });
  today: Date = new Date();

  private readonly unsubscribe: Subject<void> = new Subject();

  constructor(
    private readonly fb: FormBuilder,
    private readonly utilsService: UtilsService,
  ) {}

  ngOnInit(): void {
    this.componentForm.valueChanges
      .pipe(takeUntil(this.unsubscribe))
      .subscribe(() => {
        this.componentFormOutput.emit(this.componentForm);
      });
  }

  ngOnDestroy(): void {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }

  getFormControl = (name: string): FormControl =>
    this.componentForm.get(name) as FormControl;

  showBasicComplementary = (): boolean => {
    if (!this.componentForm.get('indComplementaria')?.value) {
      return false;
    }
    return (
      (this.perdidaFiscal &&
        this.componentForm.get('lostBenef')?.value !== null) ||
      !this.perdidaFiscal
    );
  };

  indComplementariaToggle = (value: boolean, reset = true): void => {
    this.utilsService.clearAndResetFormControl(this.componentForm, 'lostBenef');
    if (value && this.perdidaFiscal) {
      this.utilsService.addRequiredValidator(this.componentForm, 'lostBenef');
    }
    this.setComplementaryValidators(value, reset);
  };

  setComplementaryValidators = (
    complementary: boolean,
    reset: boolean,
  ): void => {
    if (complementary) {
      const validators = [Validators.required];
      if (this.tax === TaxTypeEnum.ASSERGURANCES) {
        validators.push(Validators.pattern(/^887\d{10}$/));
      } else if (this.tax === TaxTypeEnum.IDONS) {
        validators.push(Validators.pattern(/^84[467]\d{10}$/)); // 8470000037213 8460000037213 8440000037213
      } else {
        validators.push(Validators.pattern(/^60[27]\d{10}$/));
      }
      this.componentForm?.get('numJustificant')?.setValidators(validators);
      this.componentForm.updateValueAndValidity();
      this.utilsService.addRequiredValidator(
        this.componentForm,
        'quotaLiquida',
        [Validators.required, Validators.min(0), Validators.max(100_000_000)],
      );
      this.dataModificacio &&
        this.utilsService.addRequiredValidator(
          this.componentForm,
          'dataPresentacio',
          [
            Validators.required,
            SeValidations.dateRange(
              this.dataModificacio,
              this.today,
              'SE_TRIBUTS_MF.COMPLEMENTARY.MANUAL_COMPLEMENTARY.FORM_FIELDS.DATA_DARRERA_MIN_ERROR',
              'SE_TRIBUTS_MF.COMPLEMENTARY.MANUAL_COMPLEMENTARY.FORM_FIELDS.DATA_DARRERA_MAX_ERROR',
            ),
          ],
        );
    } else {
      this.utilsService.clearAndResetFormControl(
        this.componentForm,
        'numJustificant',
      );
      this.utilsService.clearAndResetFormControl(
        this.componentForm,
        'quotaLiquida',
      );
      this.utilsService.clearAndResetFormControl(
        this.componentForm,
        'dataPresentacio',
      );
      reset && this.resetDataFinalizacio.emit();
    }
  };

  lostBenefToggle(isPerduaBeneficiFiscal: boolean): void {
    if (isPerduaBeneficiFiscal && this.perdidaFiscal) {
      this.utilsService.addRequiredValidator(
        this.componentForm,
        'dataTerminiPresentacio',
      );
      this.dataFinalizacio &&
        this.componentForm
          .get('dataTerminiPresentacio')
          ?.patchValue(this.dataFinalizacio);
      this.dataModificacio &&
        this.utilsService.addRequiredValidator(
          this.componentForm,
          'dataIncompliment',
          [
            Validators.required,
            SeValidations.dateRange(
              this.dataModificacio,
              this.today,
              'SE_TRIBUTS_MF.COMPLEMENTARY.MANUAL_COMPLEMENTARY.FORM_FIELDS.DATA_INCOMPLIMENT_MIN_ERROR',
              'SE_TRIBUTS_MF.COMPLEMENTARY.MANUAL_COMPLEMENTARY.FORM_FIELDS.DATA_INCOMPLIMENT_MAX_ERROR',
            ),
          ],
        );
      this.utilsService.addRequiredValidator(
        this.componentForm,
        'dataFinTermini',
      );
    } else {
      this.utilsService.clearAndResetFormControl(
        this.componentForm,
        'dataTerminiPresentacio',
      );
      this.utilsService.clearAndResetFormControl(
        this.componentForm,
        'dataIncompliment',
      );
      this.utilsService.clearAndResetFormControl(
        this.componentForm,
        'dataFinTermini',
      );
    }
  }

  dataIncomplimentChange(): void {
    if (this.componentForm.value['dataIncompliment']) {
      const dataIncompliment = new Date(
        this.componentForm.value['dataIncompliment'],
      );
      const dataFinalizacio = new Date(
        dataIncompliment.setMonth(dataIncompliment.getMonth() + 1),
      );
      this.componentForm.get('dataFinTermini')?.setValue(dataFinalizacio);
      this.dataFinalizacio = dataFinalizacio;
      this.dataFinalizacioChanges.emit(this.dataFinalizacio);
    }
  }

  dataFinTerminiChange(): void {
    if (this.componentForm.value['dataFinTermini']) {
      this.dataFinalizacio = this.componentForm.value['dataFinTermini'];
      this.dataFinalizacioChanges.emit(this.dataFinalizacio!);
    }
  }
}
