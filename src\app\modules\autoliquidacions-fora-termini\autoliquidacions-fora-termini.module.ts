import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import {
  SeButtonModule,
  SeDatepickerModule,
  SeInputModule,
  SePanelModule,
  SeSwitchModule,
} from 'se-ui-components-mf-lib';
import { ModifySaveButtonsModule } from '../modify-save-buttons/modify-save-buttons.module';
import { AutoliquidacionsForaTerminiComponent } from './autoliquidacions-fora-termini.component';

@NgModule({
  declarations: [AutoliquidacionsForaTerminiComponent],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule,
    SePanelModule,
    SeButtonModule,
    SeInputModule,
    SeDatepickerModule,
    SeSwitchModule,
    ModifySaveButtonsModule,
  ],
  exports: [AutoliquidacionsForaTerminiComponent],
})
export class AutoliquidacionsForaTerminiModule {}
