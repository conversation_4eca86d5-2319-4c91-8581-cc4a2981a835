import { Component, EventEmitter, Input, Output } from '@angular/core';

import type { Nullable } from 'se-ui-components-mf-lib';
import { CalculTreeItem } from '../../models/calcul.model';

@Component({
  selector: 'app-calcul-tree-view',
  template: `
    <ng-container *ngIf="items && items.length">
      <ng-container *ngFor="let item of items">
        <app-calcul-tree-item
          *ngIf="!item?.separator; else separatorTemplate"
          [item]="item"
          (itemClick)="itemClick.emit($event)"
        />
      </ng-container>
    </ng-container>
    <ng-template #separatorTemplate>
      <hr />
    </ng-template>
  `,
})
export class CalculTreeViewComponent {
  @Input() items: Nullable<CalculTreeItem[]> = [];
  @Output() itemClick = new EventEmitter<CalculTreeItem>();
}
