import type { Column } from 'se-ui-components-mf-lib';

export const automaticComplementaryTableColumns: Column[] = [
  {
    header: 'SE_TRIBUTS_MF.COMPLEMENTARY.TABLE_COLUMNS_HEADERS.RECEIPT_ID',
    key: 'receiptId',
    resizable: true,
  },
  {
    header: 'SE_TRIBUTS_MF.COMPLEMENTARY.TABLE_COLUMNS_HEADERS.TAXPAYER',
    key: 'taxpayer',
    resizable: true,
  },
  {
    header: 'SE_TRIBUTS_MF.COMPLEMENTARY.TABLE_COLUMNS_HEADERS.FILING_DATE',
    key: 'submissionDate',
    cellComponentName: 'dateCellComponent',
    cellConfig: { dateFormat: 'dd/MM/yyyy' },
    resizable: true,
  },
  {
    header: 'SE_TRIBUTS_MF.COMPLEMENTARY.TABLE_COLUMNS_HEADERS.TYPE',
    key: 'tipus',
    resizable: true,
  },
  {
    header: 'SE_TRIBUTS_MF.COMPLEMENTARY.TABLE_COLUMNS_HEADERS.TOTAL_AMOUNT',
    key: 'totalAmount',
    resizable: true,
    cellComponentName: 'currencyCellComponent',
  },
  {
    header: 'SE_TRIBUTS_MF.COMPLEMENTARY.TABLE_COLUMNS_HEADERS.STATE',
    key: 'state',
    resizable: true,
  },
  {
    header: 'SE_TRIBUTS_MF.COMPLEMENTARY.TABLE_COLUMNS_HEADERS.ACTIONS',
    key: 'actions',
    size: 24.29, // tamaño mínimo para que los dos botones quepan horizontalmente
    resizable: false,
  },
];
