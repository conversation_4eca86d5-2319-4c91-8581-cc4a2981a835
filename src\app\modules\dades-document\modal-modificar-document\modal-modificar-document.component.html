<se-modal
  [data]="data"
  (modalSecondaryButtonEvent)="closeModal()"
  (modalOutputEvent)="saveData($event)"
>
  <div class="mt-3">
    <app-form-document
      [formDocumentValues]="formDocumentValues"
      [tooltipPrivatReference]="tooltipPrivatReference"
      (componentFormOutput)="getFormData($event)"
    ></app-form-document>
  </div>

  <div *ngIf="foundSessions" class="text-danger">
    {{
      'SE_TRIBUTS_MF.DADES_DOCUMENT.MODAL_EDIT.ERROR_FOUND_SESSION' | translate
    }}
  </div>
</se-modal>
