import { HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { SeHttpRequest, SeHttpService } from 'se-ui-components-mf-lib';

import { environment } from 'src/environments/environment';
import { SelfAssessmentResponse } from './model/self-assessment.model';

@Injectable({
  providedIn: 'root',
})
export class WorkingSessionsEndpointsService {
  constructor(private httpService: SeHttpService) {}

  deleteWorkingSession(processId: string) {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlTributs,
      url: `/${processId}`,
      method: 'delete',
      clearExceptions: true,
    };

    return this.httpService.delete(httpRequest);
  }

  getSelfAssessments(tax: string): Observable<SelfAssessmentResponse> {
    const params = new HttpParams().set('impost', tax);

    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlTributs,
      url: `/autoliquidacions-pendent`,
      method: 'get',
      clearExceptions: true,
      params,
    };

    return this.httpService.get(httpRequest);
  }
}
