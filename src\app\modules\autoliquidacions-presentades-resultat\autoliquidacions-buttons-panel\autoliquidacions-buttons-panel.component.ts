import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  Output,
} from '@angular/core';
import { Autoliquidacio } from '@core/models/autoliquidacio.model';
import { TranslateService } from '@ngx-translate/core';
import { MenuItem } from 'primeng/api';
import { catchError, of, Subject, takeUntil } from 'rxjs';
import {
  Nullable,
  SeButton,
  SeDocumentsService,
  SeMessageService,
  SeModal,
  SeModalService,
} from 'se-ui-components-mf-lib';
import { AutoPresResultatEndpointService } from '../autoliquidacions-presentades-resultat-endpoints.service';
import { ModalSendDocumentationComponent } from './modal-send-documentation/modal-send-documentation.component';

@Component({
  selector: 'app-autoliquidacions-buttons-panel',
  template: `<div class="d-flex gap-2">
    <se-button-dropdown [buttonOptions]="buttonOptions" [items]="actions">
      {{
        'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PRESENTADES.PANEL_ACTIONS.ALTRES_ACCIONS'
          | translate
      }}
    </se-button-dropdown>
    <se-button
      *ngIf="!isPagat"
      class="button-align"
      [btnTheme]="'primary'"
      [type]="'button'"
      size="small"
      (onClick)="onActionButton()"
    >
      {{
        'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PRESENTADES.PANEL_ACTIONS.PAGAR'
          | translate
      }}
    </se-button>
    <se-button
      *ngIf="isPagat"
      class="button-align"
      [btnTheme]="'primary'"
      [type]="'button'"
      size="small"
      (onClick)="downloadAllZip()"
    >
      {{
        'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PRESENTADES.PANEL_ACTIONS.DESCARREGAR_DOCUMENTACIO'
          | translate
      }}
    </se-button>
  </div>`,
  styles: [],
})
export class AutoliquidacionsButtonsPanelComponent implements OnDestroy {
  BASE_TRANSLATION = 'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PRESENTADES.PANEL_ACTIONS';

  @Input() isPagat: boolean = false;

  idTramit: string = '';
  _autoliquidacions: Nullable<Autoliquidacio[]>;
  @Input() set autoliquidacions(autoliquidacions: Autoliquidacio[]) {
    this._autoliquidacions = autoliquidacions;
    this.idTramit = autoliquidacions[0]?.idTramit || '';
    this.setActions();
  }

  get autoliquidacions(): Nullable<Autoliquidacio[]> {
    return this._autoliquidacions || [];
  }
  @Output() payButton = new EventEmitter<boolean>();

  @Output() redirectSessionTreballEvent: EventEmitter<boolean> =
    new EventEmitter<boolean>();

  actions: MenuItem[] | undefined;

  get buttonOptions(): SeButton {
    return {
      icon: 'matKeyboardArrowDownOutline',
      iconSize: '20px',
      size: 'small',
      iconPosition: 'right',
      btnTheme: 'secondary',
    };
  }

  private readonly unsubscribe: Subject<void> = new Subject();

  constructor(
    private readonly translateService: TranslateService,
    private readonly autoPresResultatEndpointService: AutoPresResultatEndpointService,
    private readonly seDocumentsService: SeDocumentsService,
    private readonly seMessageService: SeMessageService,
    private readonly seModalService: SeModalService,
  ) {}

  ngOnDestroy(): void {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }

  onActionButton(): void {
    this.payButton.emit();
  }

  setActions(): void {
    this.actions = [];

    !this.isPagat &&
      this.actions.push({
        checkDisabled: true,
        label: this.translateService.instant(
          `${this.BASE_TRANSLATION}.DESCARREGAR_DOCUMENTACIO`,
        ),
        command: (): void => this.downloadAllZip(),
      });

    this.actions.push({
      checkDisabled: true,
      label: this.translateService.instant(
        `${this.BASE_TRANSLATION}.ENVIAR_DOCUMENTACIO`,
      ),
      command: (): void => this.sendDocumentation(),
    });

    this.isPagat &&
      this.actions.push({
        checkDisabled: true,
        label: this.translateService.instant(
          `${this.BASE_TRANSLATION}.DESCARREGAR_DILIGENCIA`,
        ),
        command: (): void => this.downloadDiligenciaPdf(),
      });
    this.actions.push({
      checkDisabled: true,
      label: this.translateService.instant(
        `${this.BASE_TRANSLATION}.COPIAR_SESSION`,
      ),
      command: (): void => this.openModalCopiar(),
    });
  }

  downloadDiligenciaPdf(): void {
    if (this.autoliquidacions?.length) {
      this.autoPresResultatEndpointService
        .downloadDiligenciaPdf(
          this.autoliquidacions.map((auto) => auto.idMfpt)[0],
        )
        .pipe(
          takeUntil(this.unsubscribe),
          catchError(() => {
            this.captureErrorDiligencia();
            return of(undefined);
          }),
        )
        .subscribe((result) => {
          if (result?.content?.base64File) {
            this.seDocumentsService.openFile(
              result.content.base64File,
              result.content.format,
              result.content.nom,
            );
          } else {
            this.captureErrorDiligencia();
          }
        });
    }
  }

  downloadAllZip(): void {
    if (this.autoliquidacions?.length) {
      const docsNegoci = this.mapDocsNegoci(this.autoliquidacions);
      if (docsNegoci.length) {
        this.autoPresResultatEndpointService
          .downloadAllZipPadoct(docsNegoci)
          .pipe(takeUntil(this.unsubscribe))
          .subscribe((result) => {
            if (result?.content) {
              this.seDocumentsService.downloadBinariFile(
                result.content,
                'documentacio.zip',
              );
            }
          });
      }
    }
  }

  sendDocumentation(): void {
    const modalData: SeModal = {
      component: ModalSendDocumentationComponent,
    };
    const modalRef = this.seModalService.openModal(modalData);
    modalRef.componentInstance.modalOutput.subscribe(
      (email: Nullable<string>) => {
        if (email) {
          this.sendJustificant(email);
        }
        modalRef.close();
      },
    );
  }

  sendJustificant(email: string): void {
    if (this.autoliquidacions?.length) {
      const docsNegoci = this.mapDocsNegoci(this.autoliquidacions);
      if (docsNegoci.length) {
        this.seDocumentsService
          .sendEmailDocumentIdsZip(email, docsNegoci)
          .pipe(takeUntil(this.unsubscribe))
          .subscribe();
      }
    }
  }

  /**
   * Mapeo, filtro repetidos y posibles nullos de los id padocts
   * @param autoliquidacions
   * @returns
   */
  mapDocsNegoci(autoliquidacions: Autoliquidacio[]): string[] {
    return [
      ...new Set(
        autoliquidacions
          .map((auto) => auto.docsNegoci)
          .filter((doc) => doc)
          .flat(),
      ),
    ];
  }

  captureErrorDiligencia(): void {
    this.seMessageService.resetMessages();
    this.seMessageService.addMessages([
      {
        severity: 'error',
        title: 'UI_COMPONENTS.EXCEPTIONS.CODES.EXECUTION_NOK',
        subtitle:
          'UI_COMPONENTS.EXCEPTIONS.MSG.COMMONS.ERROR_WHILE_CALLING_DOCUMENTS_MS',
      },
    ]);
  }

  openModalCopiar(): void {
    const modalData: SeModal = {
      title: `${this.BASE_TRANSLATION}.MODAL_COPIAR.TITLE`,
      subtitle: `${this.BASE_TRANSLATION}.MODAL_COPIAR.SUBTITLE`,
      closable: true,
      closableLabel: `${this.BASE_TRANSLATION}.BUTTONS.COPIAR_SESSIO`,
      secondaryButton: true,
      secondaryButtonLabel: `${this.BASE_TRANSLATION}.BUTTONS.CANCEL`,
    };
    const modalRef = this.seModalService.openModal(modalData);

    modalRef.componentInstance.modalOutputEvent
      .pipe(takeUntil(this.unsubscribe))
      .subscribe(() => {
        modalRef.close();
        this.copiarFormulari();
      });
    modalRef.componentInstance.modalSecondaryButtonEvent
      .pipe(takeUntil(this.unsubscribe))
      .subscribe(() => {
        modalRef.close();
      });
  }

  copiarFormulari(): void {
    if (this.idTramit) {
      this.autoPresResultatEndpointService
        .copiarFormulari(this.idTramit)
        .pipe(takeUntil(this.unsubscribe))
        .subscribe((response) => {
          if (response?.content) {
            this.openModalSuccess(
              `${this.BASE_TRANSLATION}.MODAL_COPIAR.TITLE_SUCCESS`,
              `${this.BASE_TRANSLATION}.MODAL_COPIAR.SUBTITLE_SUCCESS`,
            );
          }
        });
    }
  }

  openModalSuccess(title: string, subtitle: string): void {
    const modalData: SeModal = {
      severity: 'success',
      title,
      subtitle,
      closable: true,
      closableLabel: `${this.BASE_TRANSLATION}.BUTTONS.TANCAR`,
    };
    const modalRef = this.seModalService.openModal(modalData);

    modalRef.componentInstance.modalOutputEvent
      .pipe(takeUntil(this.unsubscribe))
      .subscribe(() => {
        modalRef.close();
        this.redirectSessionTreballEvent.emit();
      });
  }
}
