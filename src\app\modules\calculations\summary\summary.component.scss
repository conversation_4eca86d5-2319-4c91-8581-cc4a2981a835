@import 'bootstrap/scss/functions';
@import 'bootstrap/scss/variables';
@import 'bootstrap/scss/mixins/breakpoints';

::ng-deep .text-end .se-input > .input-element > .input {
  text-align: end;
}

.calculations_hr {
  color: #106bc4;
  opacity: 1;
}

.total-card {
  width: 100%;
  background-color: #ebf6ff;
  border: 1px solid #ddd;
  padding: 24px;
  font-weight: 600;
  display: flex;
  justify-content: end;
  gap: 1rem;
  white-space: nowrap;

  @include media-breakpoint-down(sm) {
    font-size: 14px;
  }
}
