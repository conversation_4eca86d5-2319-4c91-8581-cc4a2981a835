import { Component, EventEmitter, Input, Output } from '@angular/core';

import { Tax } from '../models';
import { TaxFormFieldChange } from './models';
import { TaxFormGroupHeaderLabels } from '../tax-form-group';

@Component({
  selector: 'app-tax-form-field',
  template: `
    <app-tax-input
      *ngFor="let tax of taxes; let i = index; trackBy: trackByDates"
      [id]="createIdFor(tax)"
      [label]="label"
      [labelInvisible]="i > 0"
      [value]="tax.value"
      [units]="units"
      [pricePerUnit]="tax.pricePerUnit"
      [startDate]="tax.startDate!"
      [endDate]="tax.endDate!"
      [hideDates]="hideDates"
      [headerLabels]="headerLabels"
      (onChange)="handleChange($event)"
    ></app-tax-input>
  `,
})
export class TaxFormFieldComponent {
  @Input({ required: true }) id!: string;

  @Input() label!: string;

  @Input({ required: true }) units!: string;

  @Input() set taxes(newTaxes: Tax[]) {
    newTaxes ??= [];
    this.hideDates = newTaxes.length === 1;
    this._taxes = newTaxes;
  }

  @Input() headerLabels: TaxFormGroupHeaderLabels = {
    datesHeaderLabel: '',
    pricePerUnitHeaderLabel: '',
    totalPriceHeaderLabel: '',
  };

  get taxes() {
    return this._taxes;
  }

  @Output() onChange = new EventEmitter<TaxFormFieldChange>();

  protected _taxes: Tax[] = [];

  protected hideDates = false;

  protected trackByDates(_index: number, item: Tax): string {
    return `${item.startDate}-${item.endDate}`;
  }

  protected createIdFor(tax: Tax): string {
    return `${this.id}-from-${tax.startDate}-to-${tax.endDate}`;
  }

  protected handleChange(changedTax: Tax): void {
    const index = this.taxes.findIndex(
      (tax) =>
        tax.pricePerUnit === changedTax.pricePerUnit &&
        tax.startDate === changedTax.startDate &&
        tax.endDate === changedTax.endDate
    );

    this._taxes[index] = changedTax;

    this.onChange.emit({ id: this.id, taxes: this.taxes });
  }
}
