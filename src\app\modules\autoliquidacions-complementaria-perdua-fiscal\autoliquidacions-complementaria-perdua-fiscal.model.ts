import { Nullable } from 'se-ui-components-mf-lib';

export interface AutoliquidacionsComplementariaPerduaFiscalData {
  complementaria: Complementaria;

  dataMeritacio: string;
  dataFinalitzacioTerme: string;

  interessosForaTermini: Interessos;
  interessosDemora: Interessos;
}

export interface RequestUpdateData {
  dataTerminiPresentacio: Nullable<string>;
  dataIncumpliment: Nullable<string>;
  dataFinTermini: Nullable<string>;
}

export interface Complementaria {
  dataTerminiPresentacio: string;
  dataIncompliment: string;
  dataFinTermini: string;
}

export interface Interessos {
  dataIniciComput: Nullable<string>;
  dataFiComput: Nullable<string>;
}

export interface RequestCalculInteressos {
  dataIniciComput: Nullable<string>;
  dataFiComput: Nullable<string>;
  interessoType: InteressoType;
  indForaTermini: boolean;
}

export enum InteressoType {
  DEMORA = 'DEMORA',
  FORA_TERMINI = 'FORA_TERMINI',
}
