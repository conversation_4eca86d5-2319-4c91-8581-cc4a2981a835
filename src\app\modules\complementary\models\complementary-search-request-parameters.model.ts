import type { Nullable } from 'se-ui-components-mf-lib';
import type { Period } from '../enums';

export interface SearchSelfassessmentsRequestParameters {
  /**
   * Identificador único alfanumérico del trámite/borrador que se
   * está realizando. Ej.: `'656dc5e49163133ad6d8decd'`.
   */
  idTramit?: Nullable<string>;
  /**
   * Identificador del impuesto que se está pagando. Ej.: `'IIIMA'`.
   */
  impost?: Nullable<string>;
  /**
   * Ejercicio fiscal (el año). Ej.: `'2024'`, `'2023'`, etc.
   */
  exercici?: Nullable<string | number>;
  /**
   * Periodo del año: identificador del trimestre o anual. Posibles
   * valores:
   * - Primer trimestre (enero-marzo): `'1T'`
   * - Segundo trimestre (abril-junio): `'2T'`
   * - Te<PERSON>er trimestre (julio-septiembre): `'3T'`
   * - Anual: `'0A'`
   */
  periodi?: Nullable<Period>;
  /**
   * Identificador numérico del modelo de declaración.
   * Ejs.: `'550'`, `'560'`, `'042'`, etc.
   */
  model?: Nullable<string>;
}
