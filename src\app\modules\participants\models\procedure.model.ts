import { SeHttpResponse } from 'se-ui-components-mf-lib';

export interface TaxablePerson {
  nom: string;
  idDocument: string;
  declaracioResponsable: boolean;
}

export interface CreateProcedureRequest {
  impost: string;
  model: string;
  subjectesPassius: TaxablePerson[];
  declaracioExempcio?: boolean;
}

export interface PostCreateProcedureHttpResponse extends SeHttpResponse {
  content?: string;
}
