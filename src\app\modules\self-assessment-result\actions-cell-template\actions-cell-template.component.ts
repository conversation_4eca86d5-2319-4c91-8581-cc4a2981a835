/* eslint-disable @typescript-eslint/no-explicit-any */
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { MenuItem } from 'primeng/api';
import {
  ButtonCellConfig,
  CellComponent,
  CellConfig,
  CellEventService,
  CellEventTypes,
  Column,
  FlattenedCell,
  FlattenedRow,
  SeButton,
} from 'se-ui-components-mf-lib';

@Component({
  // eslint-disable-next-line @angular-eslint/component-selector
  selector: 'actions-cell',
  template: `
    <div class="button-row-container d-flex flex-wrap gap-2">
      <se-button-dropdown
        *ngIf="actions"
        [buttonOptions]="buttonOptions"
        [items]="actions"
      >
        {{ 'SE_TRIBUTS_MF.SELF_ASSESSMENT_RESULT.HEADER.ACTIONS' | translate }}
      </se-button-dropdown>
      <se-button
        class="button-align"
        [btnTheme]="button.buttonConfig.btnTheme ?? 'secondary'"
        [disabled]="!!button.buttonConfig.disabled"
        [icon]="button.buttonConfig.icon ?? ''"
        [iconSize]="button.buttonConfig.iconSize ?? ''"
        [iconPosition]="button.buttonConfig.iconPosition ?? 'left'"
        [type]="button.buttonConfig.type ?? 'button'"
        (onClick)="onActionButton()"
        [size]="'small'"
      >
        {{ button.buttonConfig.label || '' | translate }}
      </se-button>
    </div>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ResultActionsCellComponent implements CellComponent {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  @Input() value: any;
  @Input() cell!: FlattenedCell;
  @Input() column!: Column;
  @Input() row!: FlattenedRow;
  @Input() cellConfig!: CellConfig;

  get button(): ButtonCellConfig {
    return this.cellConfig.buttonCell!;
  }

  get actions(): MenuItem[] {
    return this.cellConfig['actions'];
  }

  get buttonOptions(): SeButton {
    return {
      icon: 'matAddSharp',
      size: 'small',
      disabled: this.cellConfig['isDropdownDisabled'],
    };
  }

  constructor(private cellEventService: CellEventService) {}

  async onActionButton() {
    if (!this.button.buttonCallback)
      return this.throwEvent(
        CellEventTypes.ACTION_BUTTON_ROW,
        'buttonCellComponent',
      );
    const { data } = await this.button.buttonCallback(
      this.row,
      this.cell,
      this.column,
    );

    if (data) {
      this.throwEvent(CellEventTypes.ACTION_BUTTON_ROW, 'buttonCellComponent', {
        newData: data,
        rowId: this.row.id,
      });
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private throwEvent(
    type: CellEventTypes,
    cellName: string,
    data?: any,
    apply = false,
  ) {
    this.cellEventService.emitEvent({
      type,
      cellName,
      cell: this.cell,
      data: data ? { ...data, apply } : { apply },
    });
  }
}
