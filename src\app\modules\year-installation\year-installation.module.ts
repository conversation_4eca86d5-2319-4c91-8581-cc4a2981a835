import { LazyElementsModule } from '@angular-extensions/elements';
import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule, Routes } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import {
  SeButtonModule,
  SeInputModule,
  SePanelModule,
  SeRadioModule,
  SpinnerComponent,
} from 'se-ui-components-mf-lib';

import { environment } from 'src/environments/environment';
import { YearInstallationComponent } from './year-installation.component';

const routes: Routes = [
  {
    path: '',
    component: YearInstallationComponent,
    data: {
      title: 'SE_GASOS_MF.APP_TITLE',
      stepId: 'REPOSITION_RESOURCE_DESKTOP_STEP2',
      stepperId: 'REPOSITION_RESOURCE_ENABLE',
    },
  },
];

@NgModule({
  declarations: [YearInstallationComponent],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule.forChild(),
    RouterModule.forChild(routes),
    LazyElementsModule.forFeature({
      elementConfigs: [
        {
          tag: 'mf-tributs-year-period',
          url: environment.mfTributsURL,
          loadingComponent: SpinnerComponent,
        },
        {
          tag: 'mf-tributs-automatic-complementary',
          url: environment.mfTributsURL,
          loadingComponent: SpinnerComponent,
        },
      ],
    }),
    SePanelModule,
    SeButtonModule,
    SeRadioModule,
    SeInputModule,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class YearInstallationModule {}
