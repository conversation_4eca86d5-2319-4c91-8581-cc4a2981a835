import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { ReactiveFormsModule } from '@angular/forms';
import { NgIconsModule } from '@ng-icons/core';
import { TranslateModule } from '@ngx-translate/core';
import {
  SeDatepickerModule,
  SePanelModule,
  SeTooltipAccessibleModule,
} from 'se-ui-components-mf-lib';
import { ModifySaveButtonsModule } from '../modify-save-buttons/modify-save-buttons.module';
import { AutoliquidacionsComplementariaPerduaFiscalComponent } from './autoliquidacions-complementaria-perdua-fiscal.component';

@NgModule({
  declarations: [AutoliquidacionsComplementariaPerduaFiscalComponent],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule,
    SePanelModule,
    SeDatepickerModule,
    NgIconsModule,
    SeTooltipAccessibleModule,
    ModifySaveButtonsModule,
  ],
  exports: [AutoliquidacionsComplementariaPerduaFiscalComponent],
})
export class AutoliquidacionsComplementariaPerduaFiscalModule {}
