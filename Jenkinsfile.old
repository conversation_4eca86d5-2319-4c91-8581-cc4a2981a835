pipeline {
    agent { 
        label 'ssl08'
    }

    environment {

        PROJECT_NAME = "se-gasos-mf"

        /******************************
            ENVIRONMENT VARIABLES
        ******************************/
        SSH_USER_CREDENTIALS_ID = "atc-dev-server-user"
        SERVER_HOST = "***************"
        SERVER_HOME = "/home/<USER>/ptatc"
        BUILD_USER_EMAIL = "<EMAIL>"
        RECIPIENTS_ON_ERROR = "<EMAIL>"
    }

    tools {
        jdk "JDK 11"
        maven "maven 3.6.2"
    }

    options {
        buildDiscarder(logRotator(numToKeepStr: '10', artifactNumToKeepStr: '10'))
    }

    stages {
        stage('Build') {
            steps {    
                sh '''
                    echo "Packaging building project..."
                    npm install --userconfig=/dev/null 
                    npm run build:elements
                '''
            }
        }

        stage('Test') {
            steps {
                catchError(buildResult: 'SUCCESS', stageResult: 'FAILURE') {
                    sh '''
                        echo "Running unit-tests..."
                        #npm test
                    '''
                }
            }
        }

        stage('Scan') {
            steps {
                    echo "Running sonarQube-scan..."
                    withSonarQubeEnv('Produccion Sonar CE') {
                        sh '''
                            # creating pom.xml file with name sonar-pom.xml
                            echo '<?xml version="1.0" encoding="UTF-8"?><project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd"><modelVersion>4.0.0</modelVersion><groupId>cat.gencat.atc.pt</groupId><artifactId>sonarqube-scan-project</artifactId><version>1.0.0-SNAPSHOT</version><packaging>pom</packaging></project>' > sonar-pom.xml

                            VERSION=$(grep -oP '"version":\\K.*' package.json | tr -d '"' | tr -d , | tr -d '[:space:]')
                            echo "Version: ${VERSION}"

                            mvn sonar:sonar -f sonar-pom.xml -Dmaven.test.skip=true -Dsonar.qualitygate.wait=true -Dsonar.projectKey=ATCMP_${PROJECT_NAME} -Dsonar.projectName=${PROJECT_NAME} -Dsonar.projectVersion=${VERSION} -Dsonar.sources=src
                        '''
                    }
            }
        }

        stage('Publish') {
            steps {
                sh '''
                    echo "Preparing new snapshot build version..."

                    # add timestamp to version
                    VERSION_NUMBER=$(grep -oP '"version":\\K.*' package.json | tr -d '"' | tr -d , | tr -d '[:space:]' | sed 's/-.*$//')
                    echo "Version number: ${VERSION_NUMBER}"
                    TIMESTAMP=$(date +%Y%m%d%H%M%S)
                    VERSION="${VERSION_NUMBER}-snapshot.${TIMESTAMP}"
                    echo "New version (with timestamp): ${VERSION}"

                    # apply version to project
                    echo "Applying version..."
                    npm --no-git-tag-version version ${VERSION}

                    echo "Publishing artifacts to Nexus..."
                    cp package.json elements/package.json
                    sed -i 's/ATCMP_Npm_Group/ATCMP_Npm/g' .npmrc
                    #npm publish elements --userconfig=/dev/null 

                    echo "Cleaning workspace"
                    rm -r elements/package.json
                    git checkout .

                    echo "Creating temporary file with the full version published to Nexus for mailing notification"
                    echo "${PROJECT_NAME}@${VERSION}" > artifact-version-name.txt
                '''
            }
        }

        stage('Deploy') {
            steps {
                withCredentials([sshUserPrivateKey(credentialsId: "${SSH_USER_CREDENTIALS_ID}", keyFileVariable: 'keyfile')]) {
                    sh '''
                        lastCommitter=$(git --no-pager show -s --format='%ae')
                        if [[ ${lastCommitter} == ${BUILD_USER_EMAIL} ]] ; then
                            echo "Skipping deploy: The build-user is the last committer"

                        else
                            echo "Deploying code to server..."

                            if [[ ${BRANCH_NAME} == "master" ]] ; then
                                environment="dev"
                            else
                                environment="test"
                            fi
                            echo "Environment: ${environment}"

                            VERSION=$(grep -oP '"version":\\K.*' package.json | tr -d '"' | tr -d , | tr -d '[:space:]')
                            echo "Version: ${VERSION}"

                            # compress files
                            echo "Compressing files..."
                            tar -czvf ${PROJECT_NAME}-${VERSION}.tar.gz elements/*

                            # Upload war file to server
                            scp -o StrictHostKeyChecking=no -i ${keyfile} ${PROJECT_NAME}-${VERSION}.tar.gz gestio@${SERVER_HOST}:${SERVER_HOME}/envs/${environment}/front
                            ssh -o StrictHostKeyChecking=no -i ${keyfile} gestio@${SERVER_HOST} "ls ${SERVER_HOME}/envs/${environment}/front"

                            # Remove old deploy-log-file
                            ssh -o StrictHostKeyChecking=no -i ${keyfile} gestio@${SERVER_HOST} "rm -f ${SERVER_HOME}/envs/${environment}/front/log/deploy-${PROJECT_NAME}.log"

                            # Execute deploy script
                            ssh -o StrictHostKeyChecking=no -i ${keyfile} gestio@${SERVER_HOST} "${SERVER_HOME}/envs/deploy-front-service.sh ${PROJECT_NAME} ${VERSION} ${environment} >> ${SERVER_HOME}/envs/${environment}/front/log/deploy-${PROJECT_NAME}.log 2>&1 &"
                        fi
                    '''
                }
            }
        }
		stage('Record Release') {
            steps {
                sh '''
					lastCommitter=$(git --no-pager show -s --format='%ae')
					if [[ ${lastCommitter} == ${BUILD_USER_EMAIL} ]] ; then
						echo "Skipping Record Release: The build-user is the last committer"
					else
						echo "Sending package file to pt-release management ms"
						#curl -L -X POST -k "https://dev.seu2.atc.intranet.gencat.cat/api/release-management/package" -F "package=@\"package.json\""
					fi
                '''
            }
        }
    }

    post {

        success {
            echo "Sending notification email"
            script {
                COMMITTER_EMAIL = sh (
                    script: 'git --no-pager show -s --format=\'%ae\'',
                    returnStdout: true).trim()

                PUBLISHED_ARTIFACT = sh(
                    script: 'cat artifact-version-name.txt',
                    returnStdout: true).trim()

                echo "Published artifact: ${PUBLISHED_ARTIFACT}"
            }
            mail body: "<h3>Build succeeded</h3><p>The branch <em>${env.BRANCH_NAME}</em> of the component <em>${PROJECT_NAME}</em> has been successfully deployed.</p><p>Details:</p><ul><li>Project: ${env.JOB_NAME}</li><li>Build number: ${env.BUILD_NUMBER}</li><li>URL: ${env.BUILD_URL}</li><li>Published artifact: ${PUBLISHED_ARTIFACT}</li></ul>", charset: "UTF-8", mimeType: "text/html", subject: "ATCMP-Pipeline: Build succeeded -> ${PROJECT_NAME}", to: "${COMMITTER_EMAIL}";
        }

        failure {
            script {
                // send error e-mails always to the last commiter
                RECIPIENTS = sh (
                    script: 'git --no-pager show -s --format=\'%ae\'',
                    returnStdout: true).trim()
                // send error e-mails also to the RECIPIENTS_ON_ERROR
                if (RECIPIENTS_ON_ERROR?.trim()) {
                    RECIPIENTS = RECIPIENTS + ',' + RECIPIENTS_ON_ERROR
                }
            }
            step([$class: 'Mailer',
                notifyEveryUnstableBuild: true,
                recipients: "${RECIPIENTS}",
                sendToIndividuals: true])
        }
    }
}
