import { LazyElementsModule } from '@angular-extensions/elements';
import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { TranslateModule } from '@ngx-translate/core';

import {
  SeCheckboxModule,
  SePanelModule,
  SpinnerComponent,
} from 'se-ui-components-mf-lib';
import { environment } from 'src/environments/environment';
import { TaxpayerComponent } from './taxpayer.component';

@NgModule({
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  declarations: [TaxpayerComponent],
  imports: [
    CommonModule,
    BrowserAnimationsModule,
    ReactiveFormsModule,
    TranslateModule,
    LazyElementsModule.forFeature({
      elementConfigs: [
        {
          tag: 'mf-seguretat-scoring',
          url: environment.mfSeguretatURL,
          loadingComponent: SpinnerComponent,
        },
        {
          tag: 'mf-seguretat-declaracio-responsable',
          url: environment.mfSeguretatURL,
          loadingComponent: SpinnerComponent,
        },
      ],
    }),
    SePanelModule,
    SeCheckboxModule,
  ],
})
export class TaxpayerModule {}
