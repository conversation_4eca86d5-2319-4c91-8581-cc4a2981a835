<!--  ALERTS -->
<se-alert
  [title]="'SE_GASOS_MF.MODULE_TAX_DECLARATION.ALERT_INFO' | translate"
  type="info"
  [closeButton]="true"
>
</se-alert>
<form [formGroup]="taxForm" class="d-flex gap-2 flex-column">
  <se-panel
    [title]="
      'SE_GASOS_MF.MODULE_TAX_DECLARATION.MASS_EMISSIONS.PANEL_TITLE'
        | translate
    "
    [colapsible]="false"
  >
    <p>
      {{
        "SE_GASOS_MF.MODULE_TAX_DECLARATION.MASS_EMISSIONS.PANEL_DESCRIPTION"
          | translate
      }}
    </p>
    <app-mass-form-template
      subGroupName="dioxidSofre"
      [parentForm]="taxForm"
      [gassTypeName]="
        'SE_GASOS_MF.MODULE_TAX_DECLARATION.MASS_EMISSIONS.SULPHUR_DIOXIDE'
          | translate
      "
    >
    </app-mass-form-template>
    <app-mass-form-template
      subGroupName="oxidNitrogen"
      [parentForm]="taxForm"
      [gassTypeName]="
        'SE_GASOS_MF.MODULE_TAX_DECLARATION.MASS_EMISSIONS.NITROGEN_OXIDES'
          | translate
      "
    >
    </app-mass-form-template>
    <app-mass-form-template
      subGroupName="particules"
      [parentForm]="taxForm"
      [gassTypeName]="
        'SE_GASOS_MF.MODULE_TAX_DECLARATION.MASS_EMISSIONS.PARTICLES'
          | translate
      "
    >
    </app-mass-form-template>
    <app-mass-form-template
      subGroupName="carboniOrganic"
      [parentForm]="taxForm"
      [gassTypeName]="
        'SE_GASOS_MF.MODULE_TAX_DECLARATION.MASS_EMISSIONS.TOTAL_ORGANIC_CARBON'
          | translate
      "
    >
    </app-mass-form-template>
    <div class="row justify-content-end">
      <se-input
        class="col-auto col-md-2 w-100 total"
        formControlName="sumaQuotes"
        [label]="
          'SE_GASOS_MF.MODULE_TAX_DECLARATION.MASS_EMISSIONS.TOTAL_AMOUNT'
            | translate
        "
        [id]="'sumaQuotesId'"
        [currencyMode]="true"
        [currencySymbol]="'€'"
        [decimals]="2"
        [labelAlign]="'right'"
      ></se-input>
    </div>
    <hr />
    <p class="fw-bold">
      {{ "SE_GASOS_MF.MODULE_TAX_DECLARATION.DOCUMENTS.TITLE" | translate }}
    </p>
    <div class="mb-3">
      <span [innerHTML]="linkDocument"></span>
      <ng-icon class="link-style" name="matSimCardDownloadOutline"></ng-icon>
      <span>{{
        "SE_GASOS_MF.MODULE_TAX_DECLARATION.DOCUMENTS.DESCRIPTION_2" | translate
      }}</span>
    </div>

    <!-- documentacion a aportar -->
    <mf-documents-upload-files
      *axLazyElement
      [id]="idTramit + functionalModule"
      [hasActions]="true"
      [panelMode]="false"
      [key]="'0'"
      [idFunctionalModule]="functionalModule"
      [sigedaDescriptions]="contestableActDocument"
      [idEntity]="idTramit"
      [tableColumns]="tableColumns"
      [required]="true"
      [modalTableColumns]="modalTableColumns"
      [accept]="acceptedFiles"
      [panelDescription]="
        'SE_GASOS_MF.MODULE_TAX_DECLARATION.DOCUMENTS.UPLOAD_DESCRIPTION'
          | translate
      "
      [dropAreaTitlePreLinkText]="
        'SE_COMPONENTS.FILE_UPLOADER.ARROSSEGUEU' | translate
      "
      [dropAreaTitleLinkText]="'SE_COMPONENTS.FILE_UPLOADER.CLICK' | translate"
      [dropAreaTitlePostLinkText]="
        'SE_COMPONENTS.FILE_UPLOADER.CARGAR' | translate
      "
      [multiple]="false"
      [maxFiles]="1"
      (addedFiles)="onFilesLoaded($event)"
    >
    </mf-documents-upload-files>
  </se-panel>

  <se-panel
    [title]="'SE_GASOS_MF.MODULE_TAX_DECLARATION.BONUSES.TITLE' | translate"
    [colapsible]="false"
    [tooltip]="true"
    [tooltipText]="
      'SE_GASOS_MF.MODULE_TAX_DECLARATION.BONUSES.TOOLTIP' | translate
    "
    class="mt-3"
  >
    <p class="fw-bold">
      {{ "SE_GASOS_MF.MODULE_TAX_DECLARATION.BONUSES.SUBTITLE" | translate }}
    </p>
    <p>
      {{ "SE_GASOS_MF.MODULE_TAX_DECLARATION.BONUSES.DESCRIPTION" | translate }}
    </p>
    <se-switch
      [id]="'indBonificacioCombustible'"
      [label]="
        'SE_GASOS_MF.MODULE_TAX_DECLARATION.BONUSES.FUEL_SWITCH' | translate
      "
      formControlName="indBonificacioCombustible"
    >
    </se-switch>
    <ng-container *ngIf="showFuelBonificationFields">
      <ng-container *ngTemplateOutlet="fuel"></ng-container>
    </ng-container>

    <hr />
    <p class="fw-bold">
      {{
        "SE_GASOS_MF.MODULE_TAX_DECLARATION.BONUSES.INVESTMENT.TITLE"
          | translate
      }}
    </p>

    <p>
      {{
        "SE_GASOS_MF.MODULE_TAX_DECLARATION.BONUSES.INVESTMENT.DESCRIPTION"
          | translate
      }}
    </p>

    <se-switch
      [id]="'indBonificacioInversio'"
      [label]="
        'SE_GASOS_MF.MODULE_TAX_DECLARATION.BONUSES.INVESTMENT.INVESTMENT_SWITCH'
          | translate
      "
      formControlName="indBonificacioInversio"
    >
    </se-switch>
    <ng-container *ngIf="showindBonificacioInversioFields">
      <ng-container *ngTemplateOutlet="investment"></ng-container>
    </ng-container>
  </se-panel>
</form>

<!--  BUTTONS -->
<section class="mt-3 d-flex justify-content-between flex-row">
  <se-button (onClick)="goBack()" [btnTheme]="'secondary'">
    {{ "UI_COMPONENTS.BUTTONS.PREVIOUS" | translate }}
  </se-button>
  <se-button [disabled]="!taxForm.valid" (onClick)="submit()">
    {{ "UI_COMPONENTS.BUTTONS.NEXT" | translate }}
  </se-button>
</section>

<ng-template #fuel>
  <form [formGroup]="taxForm" class="row mt-3">
    <se-input
      class="col-5 col-md col-lg label-text-overflow"
      formControlName="quotaIntegraBonificable"
      [label]="
        'SE_GASOS_MF.MODULE_TAX_DECLARATION.BONUSES.FUEL.QUOTA_INTEGRA'
          | translate
      "
      [id]="'quotaIntegraBonificable'"
      [currencyMode]="true"
      [currencySymbol]="'%'"
      [decimals]="2"
      [tooltip]="true"
      [tooltipText]="
        'SE_GASOS_MF.MODULE_TAX_DECLARATION.BONUSES.FUEL.QUOTA_INTEGRA_TOOLTIP'
          | translate
      "
      [labelAlign]="'right'"
    ></se-input>
    <span
      class="col-1 col-md-auto col-lg-auto d-flex align-items-center justify-content-center"
    >
      x
    </span>
    <se-input
      class="col-4 col-md col-lg-auto"
      formControlName="bonificacioPercentCombustible"
      [label]="
        'SE_GASOS_MF.MODULE_TAX_DECLARATION.BONUSES.FUEL.BONUS' | translate
      "
      [id]="'bonificacioPercentCombustible'"
      [currencyMode]="true"
      [currencySymbol]="'%'"
      [decimals]="2"
      [labelAlign]="'right'"
    ></se-input>
    <span
      class="col-1 col-md-auto col-lg-auto d-flex align-items-center justify-content-end justify-content-lg-center"
    >
      =
    </span>
    <se-input
      class="col-12 col-lg"
      formControlName="bonificacioPercentAplicable"
      [label]="
        'SE_GASOS_MF.MODULE_TAX_DECLARATION.BONUSES.FUEL.BONUS_APLICABLE'
          | translate
      "
      [id]="'bonificacioPercentAplicable'"
      [currencyMode]="true"
      [currencySymbol]="'%'"
      [decimals]="2"
      [labelAlign]="'right'"
    ></se-input>
    <se-input
      class="col-12 col-lg"
      formControlName="totalBonificacioCombustible"
      [label]="
        'SE_GASOS_MF.MODULE_TAX_DECLARATION.BONUSES.FUEL.TOTAL_BONUS'
          | translate
      "
      [id]="'totalBonificacioCombustible'"
      [currencyMode]="true"
      [currencySymbol]="'€'"
      [decimals]="2"
      [tooltip]="true"
      [tooltipText]="
        'SE_GASOS_MF.MODULE_TAX_DECLARATION.BONUSES.FUEL.TOTAL_BONUS_TOOLTIP'
          | translate
      "
      [labelAlign]="'right'"
    ></se-input>
  </form>
</ng-template>

<ng-template #investment>
  <form [formGroup]="taxForm" class="row mt-3">
    <se-input
      class="col-5 col-md col-lg label-text-overflow"
      formControlName="inversioRealizada"
      [label]="
        'SE_GASOS_MF.MODULE_TAX_DECLARATION.BONUSES.INVESTMENT.INVESTMENT_AMOUNT'
          | translate
      "
      [id]="'inversioRealizada'"
      [currencyMode]="true"
      [currencySymbol]="'€'"
      [decimals]="2"
      [labelAlign]="'right'"
    ></se-input>
    <span
      class="col-1 col-md-auto col-lg-auto d-flex align-items-center justify-content-center"
    >
      x
    </span>
    <se-input
      class="col-4 col-md col-lg-auto"
      formControlName="bonificacioPercentInversio"
      [label]="
        'SE_GASOS_MF.MODULE_TAX_DECLARATION.BONUSES.INVESTMENT.BONUS_APPLICABLE'
          | translate
      "
      [id]="'bonificacioPercentInversio'"
      [currencyMode]="true"
      [currencySymbol]="'%'"
      [decimals]="2"
      [labelAlign]="'right'"
    ></se-input>
    <span
      class="col-1 col-md-auto col-lg-auto d-flex align-items-center justify-content-end justify-content-lg-center"
    >
      =
    </span>
    <se-input
      class="col-12 col-lg"
      formControlName="bonificacioInversioResultat"
      [label]="
        'SE_GASOS_MF.MODULE_TAX_DECLARATION.BONUSES.INVESTMENT.RESULT'
          | translate
      "
      [id]="'bonificacioInversioResultat'"
      [currencyMode]="true"
      [currencySymbol]="'€'"
      [decimals]="2"
      [labelAlign]="'right'"
      [tooltip]="true"
      [tooltipText]="
        'SE_GASOS_MF.MODULE_TAX_DECLARATION.BONUSES.INVESTMENT.RESULT_TOOLTIP'
          | translate
      "
    ></se-input>
    <se-input
      class="col-12 col-lg"
      formControlName="totalBonificacioInversio"
      [label]="
        'SE_GASOS_MF.MODULE_TAX_DECLARATION.BONUSES.INVESTMENT.BONUS'
          | translate
      "
      [id]="'totalBonificacioInversio'"
      [currencyMode]="true"
      [currencySymbol]="'€'"
      [decimals]="2"
      [tooltip]="true"
      [tooltipText]="
        'SE_GASOS_MF.MODULE_TAX_DECLARATION.BONUSES.INVESTMENT.BONUS_TOOLTIP'
          | translate
      "
      [labelAlign]="'right'"
    ></se-input>
  </form>
</ng-template>
