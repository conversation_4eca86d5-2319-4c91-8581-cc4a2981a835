import { Injectable } from '@angular/core';
import { SelfAssessmentStatus } from '@core/models/self-assessment-status.model';
import {
  Nullable,
  SeDataStorageService,
  SeHeaderInfoItem,
} from 'se-ui-components-mf-lib';
import { TaxPayer } from 'src/app/modules/participants/models';

/* Storage keys */
const ID_TRAMIT = 'IEGI_ID_TRAMIT';
const SELECTED_TAXPAYER = 'IEGI_SELECTED_TAXPAYER';
const HEADER = 'IEGI_HEADER';
const STATUS = 'IEGI_STATUS';
const RESPONSIBLE_DECLARATION_CHECKED = 'IEGI_RESPONSIBLE_DECLARATION_CHECKED';
const PRESENTATION_DATE = 'IEGI_PRESENTATION_DATE';
const TAX_YEAR = 'IEGI_TAX_YEAR';
const AMOUNT_TO_PAY = 'IEGI_AMOUNT_TO_PAY';
const SELF_ASSESSMENT_ID = 'IEGI_SELF_ASSESSMENT_ID';
const WORKING_SESSION_HAS_APPEARED = 'IEGI_WORKING_SESSION_HAS_APPEARED';
const EMIS_CODE = 'IEGI_EMIS_CODE';
const CURRENT_LANG = 'IEGI_CURRENT_LANG';
const PRESENTED_AL = 'IEGI_PRESENTED_AL';
const PAYMENT_ID = 'IEGI_PAYMENT_ID';
const NUM_JUSTIFICANT_COMPLEMENTARI = 'IEGI_NUM_JUSTIFICANT_COMPLEMENTARI';

@Injectable({
  providedIn: 'root',
})
export class StoreService {
  constructor(private storage: SeDataStorageService) {
    // Intencionadamente vacío
  }

  get idTramit(): Nullable<string> {
    return this.storage.getItem(ID_TRAMIT);
  }
  set idTramit(idTramit: Nullable<string>) {
    this.storage.setItem(ID_TRAMIT, idTramit);
  }

  get selectedTaxpayer(): Nullable<TaxPayer> {
    return this.storage.getItem(SELECTED_TAXPAYER);
  }
  set selectedTaxpayer(taxpayer: Nullable<TaxPayer>) {
    this.storage.setItem(SELECTED_TAXPAYER, taxpayer);
  }

  get header(): Nullable<SeHeaderInfoItem>[] {
    return this.storage.getItem(HEADER);
  }
  set header(header: Nullable<SeHeaderInfoItem>[]) {
    this.storage.setItem(HEADER, header);
  }
  get status(): SelfAssessmentStatus {
    return this.storage.getItem(STATUS);
  }
  set status(header: SelfAssessmentStatus) {
    this.storage.setItem(STATUS, header);
  }
  get responsibleDeclaration(): Nullable<boolean> {
    return this.storage.getItem(RESPONSIBLE_DECLARATION_CHECKED);
  }
  set responsibleDeclaration(check: Nullable<boolean>) {
    this.storage.setItem(RESPONSIBLE_DECLARATION_CHECKED, check);
  }
  get presentationDate(): Nullable<string> {
    return this.storage.getItem(PRESENTATION_DATE);
  }
  set presentationDate(presentationDate: Nullable<string>) {
    this.storage.setItem(PRESENTATION_DATE, presentationDate);
  }
  get taxYear(): Nullable<number> {
    return this.storage.getItem(TAX_YEAR);
  }
  set taxYear(taxYear: Nullable<number>) {
    this.storage.setItem(TAX_YEAR, taxYear);
  }
  get amountToPay(): Nullable<number> {
    return this.storage.getItem(AMOUNT_TO_PAY);
  }
  set amountToPay(amount: Nullable<number>) {
    this.storage.setItem(AMOUNT_TO_PAY, amount);
  }
  get selfAssessmentId(): Nullable<string> {
    return this.storage.getItem(SELF_ASSESSMENT_ID);
  }
  set selfAssessmentId(selfassessmentId: Nullable<string>) {
    this.storage.setItem(SELF_ASSESSMENT_ID, selfassessmentId);
  }
  get hasAppearedWorkingSession(): Nullable<boolean> {
    return this.storage.getItem(WORKING_SESSION_HAS_APPEARED);
  }
  set hasAppearedWorkingSession(hasAppeared: Nullable<boolean>) {
    this.storage.setItem(WORKING_SESSION_HAS_APPEARED, hasAppeared);
  }
  get codEmis(): Nullable<string> {
    return this.storage.getItem(EMIS_CODE);
  }
  set codEmis(codEmis: Nullable<string>) {
    this.storage.setItem(EMIS_CODE, codEmis);
  }
  get currentLang(): Nullable<string> {
    return this.storage.getItem(CURRENT_LANG);
  }
  set currentLang(lang: Nullable<string>) {
    this.storage.setItem(CURRENT_LANG, lang);
  }
  get presentedAl(): Nullable<boolean> {
    return this.storage.getItem(PRESENTED_AL);
  }
  set presentedAl(presented: Nullable<boolean>) {
    this.storage.setItem(PRESENTED_AL, presented);
  }
  get paymentId(): Nullable<string> {
    return this.storage.getItem(PAYMENT_ID);
  }
  set paymentId(paymentId: Nullable<string>) {
    this.storage.setItem(PAYMENT_ID, paymentId);
  }
  get numJustificantComplementari(): Nullable<string> {
    return this.storage.getItem(NUM_JUSTIFICANT_COMPLEMENTARI);
  }
  set numJustificantComplementari(
    numJustificantComplementari: Nullable<string>,
  ) {
    this.storage.setItem(
      NUM_JUSTIFICANT_COMPLEMENTARI,
      numJustificantComplementari,
    );
  }
}
