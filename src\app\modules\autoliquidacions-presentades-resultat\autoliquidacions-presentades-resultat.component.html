<div class="app-autoliquidacions-presentades-resultat">
  <se-alert
    *ngIf="alertData && showAlerts"
    [title]="alertData.title"
    [type]="alertData.type"
    [list]="alertData.list"
    [closeButton]="closeButtonAlert"
  >
    <span *ngIf="alertContent" [innerHTML]="alertContent | translate"></span
  ></se-alert>
  <app-informacio-pagament *ngIf="showInfoPagament"></app-informacio-pagament>

  <se-panel
    [id]="'summaryPanel'"
    [title]="titleLabelPanel | translate"
    [panelTheme]="panelTheme"
    [colapsible]="false"
    class="panel-0-padding"
    [labelButton]="
      'SE_TRIBUTS_MF.SELF_ASSESSMENT_RESULT.BUTTONS.DOWNLOAD' | translate
    "
    [customActions]="customButton"
  >
    <se-table
      *ngIf="tableData"
      [selectable]="true"
      (onSelectionChange)="handleSelectionChange($event)"
      [columns]="tableHeader"
      [paginationSelectedText]="selectedText"
      [showPagination]="tableData.length > 10"
      [showRowsPerPage]="true"
      [data]="tableData"
      [itemsPerPage]="10"
      [showEmptyState]="false"
    ></se-table>

    <ng-template #customButton>
      <app-autoliquidacions-buttons-panel
        [isPagat]="isPagat"
        [autoliquidacions]="autoliquidacions"
        (payButton)="handlePayButton()"
        (redirectSessionTreballEvent)="redirectSessionTreballHandler()"
      ></app-autoliquidacions-buttons-panel>
    </ng-template>
  </se-panel>
  <div
    class="d-flex justify-content-end align-items-end mt-4 mb-4"
    *ngIf="showNovaAutoliquidacioButton"
  >
    <se-button type="submit" (onClick)="novaAutoliquidacio()">
      {{
        'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PRESENTADES.NOVA_AUTOLIQUIDACIO'
          | translate
      }}
    </se-button>
  </div>
</div>
