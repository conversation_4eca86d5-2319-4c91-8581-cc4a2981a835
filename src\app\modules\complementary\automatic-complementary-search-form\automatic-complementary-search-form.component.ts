import { formatDate } from '@angular/common';
import {
  Component,
  EventEmitter,
  Inject,
  Input,
  LOCALE_ID,
  Output,
  type OnDestroy,
} from '@angular/core';
import { FormBuilder, FormControl, Validators } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';

import { SeValidations, type Nullable } from 'se-ui-components-mf-lib';
import { SeComplementaryEndpointsService } from '../complementary-endpoints.service';
import type {
  AutoliquidacioComplementaria,
  SearchSelfassessmentsRequestParameters,
} from '../models';

@Component({
  selector: 'se-automatic-complementary-search-form',
  templateUrl: './automatic-complementary-search-form.component.html',
  styleUrls: ['./automatic-complementary-search-form.component.scss'],
})
export class SeAutomaticComplementarySearchFormComponent implements OnDestroy {
  /**
   * Parámetros necesarios para la búsqueda de autoliquidaciones previamente
   * presentadas y que no dependen directamente del componente, sino que deben
   * establecerse desde fuera (desde un componente padre).
   */
  @Input()
  searchRequestParameters: Nullable<SearchSelfassessmentsRequestParameters>;

  @Input() isOptional: Nullable<boolean>;

  /**
   * Emite cuando la búsqueda obtiene un resultado.
   */
  @Output() searchEnd = new EventEmitter<AutoliquidacioComplementaria>();

  /**
   * En caso de isOptional = true. Si el usuario decide no informar una complementaria se emite el valor
   * true para dejarle continuar
   */
  @Output() continueWithoutSearch = new EventEmitter<boolean>();

  protected readonly currentDate = new Date();

  protected readonly searchForm = this.fb.group({
    receiptId: new FormControl<Nullable<string>>(
      null,
      SeValidations.listValidations([
        {
          validator: Validators.required,
          translation:
            'SE_TRIBUTS_MF.COMPLEMENTARY.SEARCH_FORM_VALIDATIONS.RECEIPT_ID.REQUIRED',
        },
      ])
    ),
    filingDate: new FormControl<Nullable<Date>>(
      null,
      SeValidations.listValidations([
        {
          validator: Validators.required,
          translation:
            'SE_TRIBUTS_MF.COMPLEMENTARY.SEARCH_FORM_VALIDATIONS.PRESENTATION_DATE.REQUIRED',
        },
      ])
    ),
    amount: new FormControl<Nullable<number>>(
      null,
      SeValidations.listValidations([
        {
          validator: Validators.required,
          translation:
            'SE_TRIBUTS_MF.COMPLEMENTARY.SEARCH_FORM_VALIDATIONS.AMOUNT.REQUIRED',
        },
      ])
    ),
    searchComplementary: new FormControl<Nullable<boolean>>(false),
  });

  protected isErrorAlertVisible = false;

  private isLoading = false;

  private destroyed$ = new Subject<void>();

  constructor(
    private fb: FormBuilder,
    private endpoints: SeComplementaryEndpointsService,
    @Inject(LOCALE_ID) private locale: string
  ) {
    // Intencionadamente vacío
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.unsubscribe();
  }

  protected searchSelfassessment(): void {
    if (this.isSearchButtonDisabled) return;

    if (
      !this.searchRequestParameters?.exercici ||
      !this.searchRequestParameters?.idTramit ||
      !this.searchRequestParameters?.impost ||
      !this.searchRequestParameters?.model ||
      !this.searchRequestParameters?.periodi
    ) {
      return; // no se han pasado todos los parámetros de búsqueda obligatorios
    }

    this.isErrorAlertVisible = false;
    this.isLoading = true;

    this.endpoints
      .getAutoliquidacioByNumJustificant({
        idTramit: this.searchRequestParameters?.idTramit,
        impost: this.searchRequestParameters?.impost,
        exercici: String(this.searchRequestParameters?.exercici),
        periodi: this.searchRequestParameters?.periodi,
        model: this.searchRequestParameters?.model,
        numJustificant: this.receiptId!,
        dataPresentacio: this.filingDate!,
        quotaTotal: this.amount!,
      })
      .pipe(takeUntil(this.destroyed$))
      .subscribe({
        next: (response) => {
          this.isErrorAlertVisible = !response.content;
          this.searchEnd.emit(response?.content);
        },
        error: (error) => {
          console.error(error);
          this.isErrorAlertVisible = true;
        },
        complete: () => {
          this.isLoading = false;
        },
      });
  }

  private get receiptId(): Nullable<string> {
    return this.searchForm.get('receiptId')?.value;
  }

  private get filingDate(): Nullable<string> {
    const filingDate = this.searchForm.get('filingDate')?.value;
    if (!filingDate) return null;
    return formatDate(filingDate, 'yyyy-MM-dd', this.locale);
  }

  private get amount(): Nullable<number> {
    return this.searchForm.get('amount')?.value;
  }

  protected get searchComplementary(): Nullable<boolean> {
    return this.searchForm.get('searchComplementary')?.value;
  }

  protected emitSearchComplementaryChange(event: boolean): void {
    this.continueWithoutSearch.emit(event)        
  }

  protected get isSearchButtonDisabled(): boolean {
    return (
      !this.searchForm.valid ||
      !this.searchRequestParameters?.exercici ||
      !this.searchRequestParameters?.idTramit ||
      !this.searchRequestParameters?.impost ||
      !this.searchRequestParameters?.model ||
      !this.searchRequestParameters?.periodi ||
      this.isLoading
    );
  }
}
