<!-- SAVE -->
<se-button
  *ngIf="!readonly; else modifyBtn"
  type="submit"
  btnTheme="secondary"
  [disabled]="isDisabled"
  (onClick)="save()"
>
  {{ 'SE_TRIBUTS_MF.BUTTONS.SAVE' | translate }}
</se-button>
<!-- MODIFY -->
<ng-template #modifyBtn>
  <se-button (onClick)="toggleEdit()" type="button" btnTheme="secondary">
    {{ 'SE_TRIBUTS_MF.BUTTONS.MODIFY' | translate }}
  </se-button>
</ng-template>
