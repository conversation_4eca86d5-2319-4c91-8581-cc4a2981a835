import { CommonModule, DecimalPipe } from '@angular/common';
import { NgModule } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import {
	SeButtonModule,
  SeModalModule,
  SePanelModule,
  SeTooltipAccessibleModule,
} from 'se-ui-components-mf-lib';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { TooltipModule } from 'primeng/tooltip';
import { NgIconsModule } from '@ng-icons/core';
import { NgbModalModule } from '@ng-bootstrap/ng-bootstrap';

import { SummaryComponent } from './summary.component';
import { ModalIntegratedQuotaCalculationDetailComponent } from './modal-integrated-quota-calculation-detail/modal-integrated-quota-calculation-detail.component';

@NgModule({
  declarations: [SummaryComponent, ModalIntegratedQuotaCalculationDetailComponent],
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    FormsModule,
    ReactiveFormsModule,
    BrowserAnimationsModule,
    SePanelModule,
    SeButtonModule,
    TooltipModule,
    NgIconsModule,
    SeTooltipAccessibleModule,
    SeModalModule,
    NgbModalModule,
  ],
  providers: [DecimalPipe],
  bootstrap: [SummaryComponent],
  exports: [SummaryComponent],
})
export class SummaryModule {}
