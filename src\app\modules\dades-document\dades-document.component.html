<div class="app-dades-document mt-4">
  <se-panel [title]="'SE_TRIBUTS_MF.DADES_DOCUMENT.TITLE' | translate">
    <!-- FORM DOCUMENT -->
    <ng-container *ngIf="!showResum; else resum">
      <p>{{"SE_TRIBUTS_MF.DADES_DOCUMENT.SUBTITLE" | translate}}</p>
      <app-form-document [tooltipPrivatReference]="tooltipPrivatReference" (componentFormOutput)="getFormData($event)"></app-form-document>
    </ng-container>
    <!-- RESUM -->
    <ng-template #resum>
      <se-selective-card
        [items]="cardDocument"
        [enableSelected]="false"
        [enableRequestInfo]="false"
        [enableDownload]="false"
        (toggleButton)="openModifyModalDocument()"
        [toggleButtonText]="'SE_TRIBUTS_MF.DADES_DOCUMENT.BUTTONS.MODIFY' | translate">
      </se-selective-card>
    </ng-template>
  </se-panel>
  <!-- FOOTER -->
  <div class="d-flex justify-content-end mt-4 mb-4" *ngIf="!showResum">
    <se-button
      type="submit"
      [btnTheme]="'primary'"
      [disabled]="isValidForm()"
      (onClick)="validateForm()">
      {{ "SE_TRIBUTS_MF.DADES_DOCUMENT.BUTTONS.CONTINUE" | translate }}
    </se-button>
  </div>
</div>
