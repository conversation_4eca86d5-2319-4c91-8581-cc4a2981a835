<se-panel
  [id]="'taxpayerPanel'"
  [title]="title || 'SE_TRIBUTS_MF.TAXPAYER.TITLE' | translate"
>
  <section class="mb-4 pb-4 border-bottom" [formGroup]="taxpayerForm">
    <p>
      {{
        description ||
          'SE_TRIBUTS_MF.TAXPAYER.COPY_PRESENTER_DATA_CHECKBOX.TITLE'
          | translate
      }}
    </p>
    <se-checkbox
      [label]="
        'SE_TRIBUTS_MF.TAXPAYER.COPY_PRESENTER_DATA_CHECKBOX.LABEL' | translate
      "
      [id]="'copy-presenter-data-checkbox-input'"
      [formControlName]="'copyPresenterDataCheckbox'"
      (onClick)="onCheckboxClicked($event)"
    >
    </se-checkbox>
  </section>
  <mf-seguretat-scoring
    *axLazyElement
    class="mb-4 mb-md-0"
    [disabled]="isScoringDisabled"
    [taxpayer]="scoringTaxpayer"
    [taxPayerDocumentLabel]="taxPayerDocumentLabel"
    [taxPayerNameLabel]="taxPayerNameLabel"
    (onValidationChange)="onScoringValidationChange($event)"
    (onTaxpayerChange)="onScoringTaxpayerChange($event)"
  >
  </mf-seguretat-scoring>
  <ng-container *ngIf="isVisibleDeclarationResponsible">
    <mf-seguretat-declaracio-responsable
      *axLazyElement
      [ngClass]="{ 'mt-22px': declaracioResponsable.visible }"
      [procedureCode]="procedureCode"
      [taxpayers]="declaracioResponsableTaxpayers"
      [value]="declaracioResponsable.checked"
      (onChange)="onDeclaracioResponsableChange($event)"
    >
    </mf-seguretat-declaracio-responsable>
  </ng-container>
</se-panel>
