import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { SeHttpRequest, SeHttpResponse, SeHttpService } from 'se-ui-components-mf-lib';
import { environment } from 'src/environments/environment';
import { HttpResponseSelfAssessment, HttpResponseSelfAssessments } from './self-assessment-result.model';

@Injectable({
  providedIn: 'root',
})
export class SelfAssessmentResultEndpointService {
  constructor(private httpService: SeHttpService) {}

  // Request GET - SelfAssessment ID
  getSummarySelfAssessment(idSelfassessment: string): Observable<HttpResponseSelfAssessment> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlTributs,
      url: `/autoliquidacio/${idSelfassessment}`,
      method: 'get',
      clearExceptions: true,
    };
    return this.httpService.get(httpRequest);
  }

  // Request GET - List SelfAssessment ID
  getSummarySelfAssessments(autoliquidacions: string[]): Observable<HttpResponseSelfAssessments> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlTributs,
      url: `/autoliquidacions`,
      method: 'post',
      body: { autoliquidacions },
      clearExceptions: true,
    };
    return this.httpService.post(httpRequest);
  }

  downloadDiligenciaPdf(idMFPT: string): Observable<SeHttpResponse> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlPresentacions,
      url: `/autoliquidacio/${idMFPT}/diligencia`,
      method: 'get'
    };
    return this.httpService.get(httpRequest)
  }

  downloadAllZipPadoct(request: string[]): Observable<SeHttpResponse> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlDocuments,
      url: `/padoct-zip`,
      method: 'post',
      body: request,
    };
    return this.httpService.post(httpRequest)
  }
  
  downloadAllZip(request: string[]): Observable<SeHttpResponse> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlTributs,
      url: `/zip`,
      method: 'post',
      body: request
    };
    return this.httpService.post(httpRequest)
  }

  sendDocumentacio(email: string, idAutoliquidacions: string[]): Observable<SeHttpResponse> {
    const body = { autoliquidacions: idAutoliquidacions, email };
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlTributs,
      url: `/correu-electronic`,
      method: 'post',
      body,
    };

    return this.httpService.post(httpRequest);
  }
}
