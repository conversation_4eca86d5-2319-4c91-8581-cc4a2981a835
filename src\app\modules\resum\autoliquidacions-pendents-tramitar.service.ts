import { CurrencyPipe } from '@angular/common';
import { Injectable } from '@angular/core';
import { SelfAssessmentEstat } from '@core/models';
import { Autoliquidacio } from '@core/models/autoliquidacio.model';
import { TranslateService } from '@ngx-translate/core';
import {
  Column,
  DeleteCallbackReturn,
  DownloadCallbackReturn,
  FlattenedCell,
  FlattenedRow,
  Row,
} from 'se-ui-components-mf-lib';
import { ColumnPosition } from './autoliquidacions-pendents-tramitar.model';

@Injectable({
  providedIn: 'root',
})
export class AutoPendentsTramitarService {
  BASE_TRANSLATE = 'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PENDENTS_TRAMITAR';
  constructor(
    private readonly currencyPipe: CurrencyPipe,
    private readonly translateService: TranslateService,
  ) {}

  getAutoliquidacionsRows(
    autoliquidacions: Autoliquidacio[],
    rowsAdd: Row[],
  ): Row[] {
    let rows = autoliquidacions.map((autoliquidacio) => {
      return {
        rowConfig: {
          background: !autoliquidacio.errors.length
            ? ''
            : 'var(--color-orange-50)',
        },
        data: {
          numJustificant: {
            value: autoliquidacio.numJustificant,
          },
          idAutoliquidacio: {
            value: autoliquidacio.idAutoliquidacio,
          },
          sp: {
            value: `${autoliquidacio.subjectePassiu.nif} - ${autoliquidacio.subjectePassiu.nom}`,
          },
          quotaResultant: {
            value: this.currencyPipe.transform(
              autoliquidacio.calculs.quotaResultante,
            ),
          },
          interessosDemora: {
            value: this.currencyPipe.transform(
              autoliquidacio.calculs.interessosDemora.interessos,
            ),
          },
          recarrec: {
            value: this.currencyPipe.transform(
              autoliquidacio.calculs.recarrec.totalRecarrec,
            ),
          },
          interessosDemoraForaTermini: {
            value: this.currencyPipe.transform(
              autoliquidacio.calculs.interessosForaTermini.interessos,
            ),
          },
          importTotalIngressar: {
            value: this.currencyPipe.transform(
              autoliquidacio.calculs.totalIngressar,
            ),
          },
        },
      };
    });
    // añado las columnas extras que se podrian mandar
    rows = rows.map((row) => {
      const autoFind = rowsAdd.find(
        (r) =>
          r.data['idAutoliquidacio'].value ===
          row.data['idAutoliquidacio'].value,
      );
      if (autoFind) {
        Object.keys(autoFind.data).forEach((key) => {
          // Si no existe la columna en la linea la añado
          if (!(key in row.data)) {
            row.data[key as keyof typeof row.data] = autoFind.data[key];
          }
        });
      }
      return row;
    });
    return rows;
  }

  getAutoliquidacionsColumns(
    deleteCallback: (
      row: FlattenedRow,
      cell: FlattenedCell,
      column: Column,
    ) => DeleteCallbackReturn | Promise<DeleteCallbackReturn>,
    downloadCallback: (
      row: FlattenedRow,
      cell: FlattenedCell,
      column: Column,
    ) => DownloadCallbackReturn | Promise<DownloadCallbackReturn>,
    columnsAdd: ColumnPosition[],
  ): Column[] {
    const columns: Column[] = [
      {
        key: 'sp',
        header: this.translateService.instant(
          `${this.BASE_TRANSLATE}.PANEL_BE.TABLE.SP`,
        ),
        size: 25,
      },
      {
        key: 'quotaResultant',
        header: this.translateService.instant(
          `${this.BASE_TRANSLATE}.PANEL_BE.TABLE.QUOTA_RESULTANT`,
        ),
        size: 10,
      },
      {
        key: 'interessosDemora',
        header: this.translateService.instant(
          `${this.BASE_TRANSLATE}.PANEL_BE.TABLE.INTERESSOS_DEMORA`,
        ),
        size: 10,
      },
      {
        key: 'recarrec',
        header: this.translateService.instant(
          `${this.BASE_TRANSLATE}.PANEL_BE.TABLE.RECARREC`,
        ),
        size: 10,
      },
      {
        key: 'interessosDemoraForaTermini',
        header: this.translateService.instant(
          `${this.BASE_TRANSLATE}.PANEL_BE.TABLE.INTERESSOS_DEMORA_FORA_TERMINI`,
        ),
        size: 10,
      },
      {
        key: 'importTotalIngressar',
        header: this.translateService.instant(
          `${this.BASE_TRANSLATE}.PANEL_BE.TABLE.IMPORTE_TOTAL_INGRESSAR`,
        ),
        size: 10,
      },
      {
        header: '',
        size: 7,
        key: 'action',
        resizable: false,
        cellComponentName: 'downloadCellComponent',
        cellConfig: {
          downloadTitle: `${this.BASE_TRANSLATE}.PANEL_BE.TABLE.DOWNLOAD_TITLE`,
          hasDownload: true,
          hasConfirmation: false,
          downloadCallback,
          deleteCallback,
        },
      },
    ];

    columnsAdd.forEach((columnAdd) => {
      // Inserta la columna en la posición indicada
      if (typeof columnAdd.position === 'number') {
        columns.splice(columnAdd.position, 0, columnAdd.column);
      } else {
        // si no se indica posicio significa que se va a sobreescirbir la columna
        const index = columns.findIndex(
          (col) => col.key === columnAdd.column.key,
        );
        if (index !== -1) {
          columns[index] = columnAdd.column;
        }
      }
    });

    return columns;
  }

  checkEveryStat(
    autoliquidacions: Autoliquidacio[],
    estat: SelfAssessmentEstat,
  ): boolean {
    return autoliquidacions.every((auto) => auto.estat === estat);
  }
}
