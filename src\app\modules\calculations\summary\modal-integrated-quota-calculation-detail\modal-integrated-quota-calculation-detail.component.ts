import { Component, Input } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { SeModal } from 'se-ui-components-mf-lib';

import { ModalTaxRateDetail } from '../../calculations.model';
import { DecimalPipe } from '@angular/common';

@Component({
  selector: 'app-modal-integrated-quota-calculation-detail',
  templateUrl: './modal-integrated-quota-calculation-detail.component.html',
})
export class ModalIntegratedQuotaCalculationDetailComponent {
  @Input() data!: SeModal;
  @Input() modalTaxRateDetail: ModalTaxRateDetail | undefined;

  constructor(private activatedModalService: NgbActiveModal, private decimalPipe: DecimalPipe) {}

  protected closeModal() {
    this.activatedModalService.close();
  }

  protected getPercentTipusAplicable(
    tipusAplicable: number | undefined
  ): string {
    return `${this.decimalPipe.transform(tipusAplicable || 0, '1.2-5')}%`;
  }
}
