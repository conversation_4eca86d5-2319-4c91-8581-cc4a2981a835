import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import { SelfAssessmentEstat } from '@core/models';
import { Autoliquidacio } from '@core/models/autoliquidacio.model';
import { AutoliquidacionsEndpointsService } from '@core/services/autoliquidacions-endpoints.service';
import { TranslateService } from '@ngx-translate/core';
import { Subject, takeUntil } from 'rxjs';
import {
  Column,
  Nullable,
  Row,
  SeAlertMessage,
  SeAlertType,
} from 'se-ui-components-mf-lib';
import { AutoliquidacionsService } from '../../core/services/autoliquidacions.service';
import { AutoPresResultatService } from './autoliquidacions-presentades-resultat.service';

@Component({
  selector: 'app-autoliquidacions-presentades-resultat',
  templateUrl: './autoliquidacions-presentades-resultat.component.html',
})
export class AutoPresResultatComponent implements OnInit, OnD<PERSON>roy {
  BASE_TRANSLATION = 'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PRESENTADES';
  @Input() titleLabelPanel = `${this.BASE_TRANSLATION}.TITLE`;
  @Input() showAlerts = true;
  @Input() showInfoPagament = true;
  @Input() showNovaAutoliquidacioButton = true;
  @Input() panelTheme: 'default' | 'secondary' | 'primary' = 'default';

  _idAutoliquidacions: string[] = [];

  get idAutoliquidacions(): string[] {
    return this._idAutoliquidacions;
  }

  @Input() set idAutoliquidacions(value: string[]) {
    this._idAutoliquidacions = value;
    this.idAutoliquidacions.length && this.setAutoliquidacions(value);
  }

  @Input() set model(model: string) {
    this.autoPresResultatService.initData(model, this.payButtonEvent);
  }

  @Output() payButtonEvent: EventEmitter<string[]> = new EventEmitter<
    string[]
  >();
  @Output() novaAutoliquidacioEvent: EventEmitter<boolean> =
    new EventEmitter<boolean>();

  @Output() redirectSessionTreballEvent: EventEmitter<boolean> =
    new EventEmitter<boolean>();

  tableHeader: Column[] = [];
  tableData: Row[] = [];

  isPagat = false;
  selectedRows: Row[] = [];
  autoliquidacions: Autoliquidacio[] = [];
  alertData: Nullable<SeAlertMessage>;
  alertContent: Nullable<string>;
  closeButtonAlert = false;
  selectedText: string = '';

  private readonly unsubscribe: Subject<void> = new Subject();

  constructor(
    private readonly autoPresResultatService: AutoPresResultatService,
    private readonly autoliquidacionsService: AutoliquidacionsService,
    private readonly autoliquidacionsEndpointsService: AutoliquidacionsEndpointsService,
    private readonly translateService: TranslateService,
  ) {}

  ngOnInit(): void {
    this.initialiceTable();
  }

  ngOnDestroy(): void {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }

  private setAutoliquidacions(idsSelfassessment: string[]): void {
    this.autoliquidacionsEndpointsService
      .getAutoliquidacions(idsSelfassessment)
      .pipe(takeUntil(this.unsubscribe))
      .subscribe({
        next: (result) => {
          if (result?.content?.length) {
            this.isPagat = result.content.every(
              (auto) =>
                auto.estat === SelfAssessmentEstat.PAGAT ||
                (auto.estat === SelfAssessmentEstat.PRESENTAT &&
                  auto.quotaLiquida === 0),
            );
            this.autoliquidacions = this.sortAutoliquidacions(result.content);
            this.setAlertData(this.autoliquidacions);
            this.setTableData(this.autoliquidacions);
            this.activateAutoCheckAutoliquidacions(this.autoliquidacions);
          } else {
            this.tableData = [];
          }
        },
        error: () => {
          this.tableData = [];
        },
      });
  }

  private sortAutoliquidacions(
    autoliquidaciones: Autoliquidacio[],
  ): Autoliquidacio[] {
    const orderMap = new Map<string, number>([
      [SelfAssessmentEstat.PAGAMENT_ERROR, 1],
      [SelfAssessmentEstat.PAGANT, 2],
      [SelfAssessmentEstat.PRESENTAT, 3],
    ]);
    return autoliquidaciones.sort((a, b) => {
      const orderA = orderMap.get(a.estat) || 4;
      const orderB = orderMap.get(b.estat) || 4;
      return orderA - orderB;
    });
  }

  private activateAutoCheckAutoliquidacions(
    autoliquidacions: Autoliquidacio[],
  ): void {
    const activateLoop = autoliquidacions.some(
      (auto) => auto.estat === SelfAssessmentEstat.PAGANT,
    );
    if (activateLoop) {
      setTimeout(() => {
        this.setAutoliquidacions(this.idAutoliquidacions);
      }, 5000);
    }
  }

  private setAlertData(autoliquidacions: Autoliquidacio[]): void {
    this.resetAlert();
    let status: SelfAssessmentEstat = autoliquidacions.some((auto) =>
      this.autoPresResultatService.hasError(auto.estat),
    )
      ? SelfAssessmentEstat.PAGAMENT_ERROR
      : SelfAssessmentEstat.PRESENTAT;

    if (
      autoliquidacions.every((auto) =>
        this.autoliquidacionsService.checkStatDraftFinalize(auto),
      )
    ) {
      status = SelfAssessmentEstat.PAGAT;
    }
    this.alertData = {
      title: '',
      type: SeAlertType.SUCCESS,
      list: [],
      id: status,
    };
    this.closeButtonAlert = true;
    switch (status) {
      case SelfAssessmentEstat.PAGAT:
        this.alertData.title = `${this.BASE_TRANSLATION}.ALERT.ALERT_TITLE_PAGAT`;
        this.alertContent = `${this.BASE_TRANSLATION}.ALERT.ALERT_DESCRIPTION_PAGAT`;
        break;
      case SelfAssessmentEstat.PRESENTAT:
        this.alertData.title = `${this.BASE_TRANSLATION}.ALERT.ALERT_TITLE_PRESENTAT`;
        this.alertContent = `${this.BASE_TRANSLATION}.ALERT.ALERT_DESCRIPTION_PRESENTAT`;
        break;
      case SelfAssessmentEstat.PAGAMENT_ERROR:
        this.closeButtonAlert = false;
        this.alertData = {
          ...this.alertData,
          title: `${this.BASE_TRANSLATION}.ALERT.ALERT_TITLE_ERROR`,
          type: SeAlertType.ERROR,
        };
        this.alertContent = `${this.BASE_TRANSLATION}.ALERT.ALERT_DESCRIPTION_ERROR`;
        break;
      default:
        this.alertData = null;
        this.alertContent = null;
    }
  }

  private setTableData(autoliquidacions: Autoliquidacio[]): void {
    this.tableData =
      this.autoPresResultatService.getTableData(autoliquidacions);
  }

  private initialiceTable(): void {
    const showActions = true;
    this.tableHeader = this.autoPresResultatService.getTableColums(showActions);
  }

  handlePayButton(): void {
    if (this.selectedRows.length) {
      // Filtrar autoliquidaciones que esten finalizadas pagadas o presentadas sin valor
      const selectedRows: string[] = this.selectedRows.map(
        (row) => row.data['idAutoliquidacio'].value,
      );
      const idAutos = this.autoliquidacions
        .filter(
          (auto) =>
            selectedRows.includes(auto.idAutoliquidacio) &&
            !this.autoliquidacionsService.checkStatDraftFinalize(auto),
        )
        .map((auto) => auto.idAutoliquidacio);

      idAutos.length && this.payButtonEvent.emit(idAutos);
    } else {
      this.resetAlert();
      this.closeButtonAlert = false;
      this.alertContent = null;
      this.alertData = {
        title: `${this.BASE_TRANSLATION}.ALERT.ALERT_TITLE_PAY`,
        type: SeAlertType.WARNING,
        list: [],
      };
    }
  }

  /**
   * Resetea el componente de la modal por si este se ha cerrado con su boton de cerrar asi vuelve a aparecer
   */
  resetAlert(): void {
    // Reviso que se quieran mostrar las alertas
    if (this.showAlerts) {
      this.showAlerts = false;
      setTimeout(() => {
        this.showAlerts = true;
      });
    }
  }

  handleSelectionChange(selectedRows: Row[]): void {
    this.selectedRows = selectedRows;
    this.selectedText = this.selectedRows.length
      ? this.translateService.instant(
          `${this.BASE_TRANSLATION}.SELECTED_AUTOLIQUIDACIONS`,
          {
            selected: this.selectedRows.length,
            total: this.autoliquidacions.length,
          },
        )
      : '';
  }

  redirectSessionTreballHandler(): void {
    this.redirectSessionTreballEvent.emit();
  }

  novaAutoliquidacio(): void {
    this.novaAutoliquidacioEvent.emit();
  }
}
