<form [formGroup]="componentForm">
  <div class="row">
    <div class="col-12 col-sm-6 col-md-4 col-lg-4">
      <se-dropdown
        id="documentType"
        [label]="
          'SE_TRIBUTS_MF.DADES_DOCUMENT.FORM_FIELDS.DOC_TYPE' | translate
        "
        formControlName="documentType"
        [tooltip]="true"
        [tooltipText]="tooltipTipusDocumentContent"
        (dropdownOutput)="documentTypeChanged($event)"
        [filter]="optionsDocumentType.length > 5"
        [options]="optionsDocumentType"
      >
      </se-dropdown>
      <!-- TOOLTIP -->
      <ng-template #tooltipTipusDocumentContent>
        <div class="text-sm">
          {{
            'SE_TRIBUTS_MF.DADES_DOCUMENT.FORM_FIELDS.DOC_TYPE_TOOLTIP_NT'
              | translate
          }}
        </div>
        <div class="text-sm">
          {{
            'SE_TRIBUTS_MF.DADES_DOCUMENT.FORM_FIELDS.DOC_TYPE_TOOLTIP_JT'
              | translate
          }}
        </div>
        <div class="text-sm">
          {{
            'SE_TRIBUTS_MF.DADES_DOCUMENT.FORM_FIELDS.DOC_TYPE_TOOLTIP_AD'
              | translate
          }}
        </div>
        <div class="text-sm">
          {{
            'SE_TRIBUTS_MF.DADES_DOCUMENT.FORM_FIELDS.DOC_TYPE_TOOLTIP_PR'
              | translate
          }}
        </div>
      </ng-template>
    </div>
    <!-- Date -->
    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-2">
      <se-datepicker
        id="documentDate"
        [label]="
          'SE_TRIBUTS_MF.DADES_DOCUMENT.FORM_FIELDS.DATE_DOCUMENT' | translate
        "
        [placeholder]="'dd/mm/aaaa'"
        (dateSelectedEvent)="documentDateChanged()"
        formControlName="documentDate"
        [maxDate]="maxDate"
      >
      </se-datepicker>
    </div>
  </div>
  <!-- ADDRESS -->
  <ng-container *ngIf="showDocumentForm()">
    <div class="row">
      <!-- Pais -->
      <div class="col-12 col-sm-6 col-md-4 col-lg-4">
        <se-dropdown
          id="pais"
          [label]="
            'SE_TRIBUTS_MF.DADES_DOCUMENT.FORM_FIELDS.RES_ESP' | translate
          "
          formControlName="pais"
          (dropdownOutput)="paisChanged($event)"
          [filter]="paisList.length > 5"
          [options]="paisList"
        >
        </se-dropdown>
      </div>
      <!-- NO EXTRANJERO -->
      <ng-container *ngIf="!isForeign">
        <!-- Provincia -->
        <div class="col-12 col-sm-6 col-md-4 col-lg-4">
          <se-dropdown
            id="provincia"
            [label]="
              'SE_TRIBUTS_MF.DADES_DOCUMENT.FORM_FIELDS.COD_PRV' | translate
            "
            formControlName="provincia"
            (dropdownOutput)="provinciaChanged($event)"
            [options]="provinciaList"
            [filter]="provinciaList.length > 5"
            [disabled]="!getFormControl('pais').value"
          >
          </se-dropdown>
        </div>
        <!-- Municipi -->
        <div class="col-12 col-sm-6 col-md-4 col-lg-4">
          <se-dropdown
            id="municipi"
            [label]="
              'SE_TRIBUTS_MF.DADES_DOCUMENT.FORM_FIELDS.COD_MUN' | translate
            "
            formControlName="municipi"
            (dropdownOutput)="municipiChanged($event)"
            [options]="municipiList"
            [filter]="municipiList.length > 5"
            [disabled]="!getFormControl('provincia').value"
          >
          </se-dropdown>
        </div>
      </ng-container>
    </div>
    <div class="row">
      <!-- NO PRIVADO -->
      <div
        class="col-12 col-sm-6 col-md-4 col-lg-4"
        *ngIf="getFormControl('documentType')?.value !== DocumentTypes.PRIVAT"
      >
        <ng-container
          *ngIf="
            getFormControl('documentType')?.value === DocumentTypes.NOTARIAL &&
            !isNotary1970
          "
        >
          <se-dropdown
            id="notary"
            *ngIf="!isForeign"
            [label]="
              'SE_TRIBUTS_MF.DADES_DOCUMENT.FORM_FIELDS.NOTARY' | translate
            "
            formControlName="notary"
            [options]="notaryList"
            [autoSelect]="true"
            [filter]="notaryList.length > 5"
            (dropdownOutput)="notaryChanged($event)"
            [showClear]="true"
            [disabled]="!getFormControl('municipi').value"
          >
          </se-dropdown>
          <se-input
            id="extranger"
            *ngIf="isForeign"
            [label]="
              'SE_TRIBUTS_MF.DADES_DOCUMENT.FORM_FIELDS.NOTARY' | translate
            "
            formControlName="notary_NAME"
            [disabled]="isForeign"
          >
          </se-input>
        </ng-container>
        <ng-container
          *ngIf="
            getFormControl('documentType')?.value === DocumentTypes.NOTARIAL &&
            isNotary1970
          "
        >
          <se-input
            id="notariy1970"
            [label]="
              'SE_TRIBUTS_MF.DADES_DOCUMENT.FORM_FIELDS.NOTARY' | translate
            "
            formControlName="notary"
          >
          </se-input>
        </ng-container>

        <se-input
          *ngIf="
            getFormControl('documentType')?.value ===
            DocumentTypes.ADMINISTRATIU
          "
          id="autor"
          [label]="'SE_TRIBUTS_MF.DADES_DOCUMENT.FORM_FIELDS.AUTOR' | translate"
          [tooltip]="true"
          [tooltipText]="
            'SE_TRIBUTS_MF.DADES_DOCUMENT.TOOLTIPS.AUTORITAT' | translate
          "
          [maxLength]="35"
          formControlName="autor"
        >
        </se-input>
        <se-input
          *ngIf="
            getFormControl('documentType')?.value === DocumentTypes.JUDICIAL
          "
          id="trib_jul"
          [label]="
            'SE_TRIBUTS_MF.DADES_DOCUMENT.FORM_FIELDS.TRIB_JUL' | translate
          "
          [tooltip]="true"
          [tooltipText]="toolTipJudicial"
          [maxLength]="35"
          formControlName="trib_jul"
        >
        </se-input>
      </div>
      <div
        class="col-12 col-sm-6 col-md-4 col-lg-4"
        *ngIf="getFormControl('documentType')?.value !== DocumentTypes.NOTARIAL"
      >
        <se-input
          id="reference"
          [label]="
            'SE_TRIBUTS_MF.DADES_DOCUMENT.FORM_FIELDS.REFERENCE' | translate
          "
          [tooltip]="true"
          [tooltipText]="toolTipReference"
          [maxLength]="30"
          formControlName="reference"
        >
        </se-input>
      </div>
      <ng-container
        *ngIf="getFormControl('documentType')?.value === DocumentTypes.NOTARIAL"
      >
        <!-- Protocol -->
        <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-2">
          <se-input
            id="PROT"
            [label]="
              'SE_TRIBUTS_MF.DADES_DOCUMENT.FORM_FIELDS.NUM_PROT' | translate
            "
            [maxLength]="6"
            type="number"
            [decimals]="0"
            formControlName="PROT"
          >
          </se-input>
        </div>
        <!-- Protocol bis -->
        <div
          class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3"
          *ngIf="!isForeign"
        >
          <se-input
            id="PROT_BIS"
            [label]="
              'SE_TRIBUTS_MF.DADES_DOCUMENT.FORM_FIELDS.NUM_PROT_BIS'
                | translate
            "
            [maxLength]="2"
            type="number"
            [decimals]="0"
            (onBlur)="cleanProtBis($event)"
            formControlName="PROT_BIS"
          >
          </se-input>
        </div>
      </ng-container>
    </div>
    <div
      class="row"
      *ngIf="getFormControl('documentType')?.value === DocumentTypes.NOTARIAL"
    >
      <div class="col-12 col-lg-5" *ngIf="checkNotary()">
        <se-alert
          [title]="
            'SE_TRIBUTS_MF.DADES_DOCUMENT.FORM_FIELDS.NOTARY_ALERT' | translate
          "
          [type]="SeAlertType.ERROR"
          [closeButton]="false"
        >
          <se-switch
            id="notaryOld"
            [label]="
              'SE_TRIBUTS_MF.DADES_DOCUMENT.FORM_FIELDS.NOTARY_OLD_INPUT'
                | translate
            "
            formControlName="notaryOld"
            (onToggle)="applyNotaryOld($event)"
          ></se-switch>
        </se-alert>
      </div>
    </div>
  </ng-container>
</form>
<ng-template #toolTipJudicial>
  <div
    [innerHTML]="
      'SE_TRIBUTS_MF.DADES_DOCUMENT.FORM_FIELDS.TRIB_JUL_TOOLTIP' | translate
    "
  ></div>
  <ul>
    <li>
      {{
        'SE_TRIBUTS_MF.DADES_DOCUMENT.FORM_FIELDS.TRIB_JUL_TOOLTIP_1'
          | translate
      }}
    </li>
    <li>
      {{
        'SE_TRIBUTS_MF.DADES_DOCUMENT.FORM_FIELDS.TRIB_JUL_TOOLTIP_2'
          | translate
      }}
    </li>
  </ul>
</ng-template>
<ng-template #toolTipReference>
  <div
    *ngIf="getFormControl('documentType')?.value === DocumentTypes.PRIVAT"
    [innerHTML]="
      tooltipPrivatReference ??
        'SE_TRIBUTS_MF.DADES_DOCUMENT.FORM_FIELDS.REFERENCE_PRIVAT_TOOLTIP'
        | translate
    "
  ></div>
  <div
    *ngIf="
      getFormControl('documentType')?.value === DocumentTypes.ADMINISTRATIU
    "
  >
    {{
      'SE_TRIBUTS_MF.DADES_DOCUMENT.FORM_FIELDS.REFERENCE_AD_TOOLTIP'
        | translate
    }}
  </div>
  <div *ngIf="getFormControl('documentType')?.value === DocumentTypes.JUDICIAL">
    <div
      [innerHTML]="
        'SE_TRIBUTS_MF.DADES_DOCUMENT.FORM_FIELDS.REFERENCE_JT_TOOLTIP'
          | translate
      "
    ></div>
    <ul>
      <li>
        {{
          'SE_TRIBUTS_MF.DADES_DOCUMENT.FORM_FIELDS.REFERENCE_JT_TOOLTIP_1'
            | translate
        }}
      </li>
      <li>
        {{
          'SE_TRIBUTS_MF.DADES_DOCUMENT.FORM_FIELDS.REFERENCE_JT_TOOLTIP_2'
            | translate
        }}
      </li>
    </ul>
    <div
      [innerHTML]="
        'SE_TRIBUTS_MF.DADES_DOCUMENT.FORM_FIELDS.REFERENCE_JT_TOOLTIP_3'
          | translate
      "
    ></div>
    <ul>
      <li>
        {{
          'SE_TRIBUTS_MF.DADES_DOCUMENT.FORM_FIELDS.REFERENCE_JT_TOOLTIP_4'
            | translate
        }}
      </li>
      <li>
        {{
          'SE_TRIBUTS_MF.DADES_DOCUMENT.FORM_FIELDS.REFERENCE_JT_TOOLTIP_5'
            | translate
        }}
      </li>
      <li>
        {{
          'SE_TRIBUTS_MF.DADES_DOCUMENT.FORM_FIELDS.REFERENCE_JT_TOOLTIP_6'
            | translate
        }}
      </li>
      <li>
        {{
          'SE_TRIBUTS_MF.DADES_DOCUMENT.FORM_FIELDS.REFERENCE_JT_TOOLTIP_7'
            | translate
        }}
      </li>
    </ul>
  </div>
</ng-template>
