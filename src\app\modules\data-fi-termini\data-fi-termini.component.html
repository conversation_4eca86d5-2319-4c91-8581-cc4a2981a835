<se-panel
  class="mt-2"
  [colapsible]="true"
  [title]="
    'SE_TRIBUTS_MF.CALCUL.DATA_FI_TERMINI.TITLE' +
      (isComplementaria ? '_COMPLEMENTARIA' : '') | translate
  "
  id="data-fi-termini-panel"
>
  <form *ngIf="componentForm" [formGroup]="componentForm">
    <div class="d-flex column-gap-1">
      <se-datepicker
        id="dataFinalitzacioTerme"
        [placeholder]="'dd/mm/aaaa'"
        formControlName="dataFinalitzacioTerme"
        [label]="
          (isComplementaria
            ? 'SE_TRIBUTS_MF.CALCUL.DATA_FI_TERMINI.DATA_FI_TERMINI_COMPLEMENTARIA'
            : ''
          ) | translate
        "
        [showIcon]="true"
        [disabled]="readonly"
      >
      </se-datepicker>
      <app-modify-save-buttons
        [ngClass]="{ 'pt-4': isComplementaria }"
        [(readonly)]="readonly"
        [isDisabled]="componentForm.invalid"
        (saveEvent)="saveData()"
      ></app-modify-save-buttons>
    </div>
  </form>
</se-panel>
