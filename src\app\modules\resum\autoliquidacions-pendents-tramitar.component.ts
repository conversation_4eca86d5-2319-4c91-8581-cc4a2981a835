import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  Output,
} from '@angular/core';
import { <PERSON>statOrder, SelfAssessmentEstat } from '@core/models';
import {
  Autoliquidacio,
  CalculErrors,
} from '@core/models/autoliquidacio.model';
import { CheckStatusAutoliquidacionsParameters } from '@core/models/check-status-autoliquidacions-parameters.model';
import { AutoliquidacionsEndpointsService } from '@core/services/autoliquidacions-endpoints.service';
import { AutoliquidacionsService } from '@core/services/autoliquidacions.service';
import { NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import { firstValueFrom, Subject, takeUntil } from 'rxjs';
import {
  Nullable,
  Row,
  SeAlertMessage,
  SeMessageService,
  SeModal,
  SeModalService,
} from 'se-ui-components-mf-lib';
import { AutoPendentsTramitarEndpointsService } from './autoliquidacions-pendents-tramitar-endpoints.service';
import {
  ColumnPosition,
  ModalTrasspassar,
  RequestPresentacio,
} from './autoliquidacions-pendents-tramitar.model';
import { AutoPendentsTramitarService } from './autoliquidacions-pendents-tramitar.service';
import { ModalScoringComponent } from './modal-scoring/modal-scoring.component';
import { ModalTramitarComponent } from './modal-tramitar/modal-tramitar.component';

@Component({
  selector: 'app-autoliquidacions-pendents-tramitar',
  templateUrl: './autoliquidacions-pendents-tramitar.component.html',
  styleUrls: ['./autoliquidacions-pendents-tramitar.component.scss'],
})
export class AutoPendentsTramitarComponent implements OnDestroy {
  BASE_TRANSLATE = 'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PENDENTS_TRAMITAR';

  autoliquidacions: Nullable<Autoliquidacio[]>;
  alertData: Nullable<SeAlertMessage>;
  alertContent: Nullable<string>;
  errorCodes: string[] = [];
  hasErrors: boolean = false;
  autoPresented: boolean = false;

  @Input() idTramit: string = '';
  @Input() rows: Row[] = [];
  @Input() columns: ColumnPosition[] = [];
  @Input() showDescargarDec = false;
  @Input() showTrasspasarFormulari = false;

  _idAutoliquidacions: string[] = [];

  get idAutoliquidacions(): string[] {
    return this._idAutoliquidacions;
  }

  @Input() set idAutoliquidacions(value: string[]) {
    this._idAutoliquidacions = value;
    this.idAutoliquidacions.length && this.setAutoliquidacions(value);
  }

  @Input() titlePanelAutoliquidacio = `${this.BASE_TRANSLATE}.PANEL_BE.TITLE`;
  @Input() titlePanel = `${this.BASE_TRANSLATE}.PANEL_BORRADOR.TITLE`;

  @Output() nextEvent: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() backEvent: EventEmitter<boolean> = new EventEmitter<boolean>();

  @Output() downloadZipDecEvent: EventEmitter<boolean> =
    new EventEmitter<boolean>();

  @Output() redirectSessionTreballEvent: EventEmitter<boolean> =
    new EventEmitter<boolean>();

  @Output() setShowExceptionViewerEvent: EventEmitter<boolean> =
    new EventEmitter<boolean>();
  private readonly unsubscribe: Subject<void> = new Subject();

  constructor(
    private readonly autoliquidacionsEndpointsService: AutoliquidacionsEndpointsService,
    private readonly autoPendentsTramitarEndpointsService: AutoPendentsTramitarEndpointsService,
    private readonly autoliquidacionsService: AutoliquidacionsService,
    private readonly autoPendentsTramitarService: AutoPendentsTramitarService,
    private readonly seModalService: SeModalService,
    private readonly translateService: TranslateService,
    private readonly seMessageService: SeMessageService,
  ) {}

  ngOnDestroy(): void {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }

  setAutoliquidacions(idsSelfassessment: string[]): void {
    this.autoliquidacionsEndpointsService
      .getAutoliquidacions(idsSelfassessment)
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((result) => {
        if (result?.content) {
          this.autoliquidacions = result.content;
          !this.autoliquidacions?.length && this.navigateBackwards();
        }
      });
  }

  async continue(): Promise<void> {
    if (
      this.autoliquidacions &&
      this.autoPendentsTramitarService.checkEveryStat(
        this.autoliquidacions,
        SelfAssessmentEstat.PRESENTAT,
      )
    ) {
      this.nextEvent.emit();
    } else {
      this.openModalPresentacio();
    }
  }

  openModalPresentacio(): void {
    const modalData: SeModal = {
      component: ModalTramitarComponent,
    };
    const modalRef = this.seModalService.openModal(modalData);

    modalRef.componentInstance.modalOutput
      .pipe(takeUntil(this.unsubscribe))
      .subscribe(async () => {
        // Cmpruebo si alguna autoliquidacio esta en estado agrupando y si es asi abro la modal para no lanzar la agrupacion pro duplicado
        if (
          this.autoliquidacions?.every(
            (auto) => EstatOrder[auto.estat] >= EstatOrder.ESBORRANY_VALIDAT,
          ) &&
          this.autoliquidacions.every(
            (auto) => EstatOrder[auto.estat] < EstatOrder.ESBORRANY_AGRUPANT,
          )
        ) {
          const listFormularis = Array.from(
            new Set(this.autoliquidacions?.map((auto) => auto.idTramit)),
          );
          const result = await firstValueFrom(
            this.autoPendentsTramitarEndpointsService.agruparAutoliquidaciones(
              listFormularis ?? [],
            ),
          );
          if (!result?.content) {
            this.hasErrors = true;
            return;
          }
        }
        this.openProgressModal();
      });
  }

  async openProgressModal(): Promise<void> {
    this.autoPresented = false;
    const progressValue$ = new Subject<number>();
    const modalRef = this.seModalService.openProgressModal(
      5,
      'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_PENDENTS_TRAMITAR.MODAL_AGRUPAR.MESSAGE_MODAL',
      '',
      progressValue$,
    );
    const data = new CheckStatusAutoliquidacionsParameters({
      modalRef,
      statValidat: SelfAssessmentEstat.PRESENTAT,
      progressValue$: progressValue$,
      closeModal: false,
      idsAutoliquidacio:
        this.autoliquidacions?.map((auto) => auto.idAutoliquidacio) ?? [],
    });
    // Pruebo a presentar antes de obtener las autoliquidaciones por si estas ya estan agrupadas o con error de presentacion
    // Asi se resetean los errores y puede fluir con normalidad
    await this.presentAutoliquidacions(modalRef, data);
    modalRef.componentInstance.intervalOutput
      .pipe(takeUntil(this.unsubscribe))
      .subscribe(async () => {
        const [calculsErrors, autoliquidacions] =
          await this.autoliquidacionsService.checkStatusAutoliquidaciones(data);
        this.setErrorCodes(calculsErrors);
        this.autoliquidacions = autoliquidacions;
        await this.presentAutoliquidacions(modalRef, data);
      });
    modalRef.closed.pipe(takeUntil(this.unsubscribe)).subscribe((result) => {
      if (typeof result === 'boolean') {
        this.nextEvent.emit();
      }
    });
  }

  async presentAutoliquidacions(
    modalRef: NgbModalRef,
    data: CheckStatusAutoliquidacionsParameters,
  ): Promise<void> {
    const autosAgrupats = this.autoliquidacions?.every(
      (auto) => EstatOrder[auto.estat] >= EstatOrder.ESBORRANY_AGRUPAT,
    );
    if (
      autosAgrupats &&
      this.autoliquidacions?.every(
        (auto) => EstatOrder[auto.estat] < EstatOrder.PRESENTANT,
      ) &&
      !this.autoPresented
    ) {
      const request: RequestPresentacio[] = this.autoliquidacions.map(
        (auto) => ({
          idSelfAssessment: auto.idAutoliquidacio,
          idReceipt: auto.numJustificant,
        }),
      );
      const result = await firstValueFrom(
        this.autoPendentsTramitarEndpointsService.presentatAutoliquidaciones(
          request,
        ),
      );
      if (result?.content) {
        this.autoPresented = true;
      } else {
        this.hasErrors = true;
        modalRef.close();
      }
    }
    if (autosAgrupats && !data.closeModal) {
      // Permito que la modal de progreso se cierre ya que esta en el paso de presentacion
      data.closeModal = true;
    }
  }

  setErrorCodes(calculsErrors: CalculErrors[]): void {
    this.errorCodes = calculsErrors.reduce<string[]>((codes, { errors }) => {
      errors.forEach(({ code }) => {
        if (
          !!code &&
          !codes.includes(code) &&
          !this.translateService
            .instant(`UI_COMPONENTS.MODAL_ERRORS.MAPPED_ERRORS.${code}`)
            .includes('UI_COMPONENTS.MODAL_ERRORS.MAPPED_ERRORS')
        ) {
          codes.push(code);
        }
      });
      return codes;
    }, []);
  }

  openModalTraspassar(): void {
    const modalData: SeModal = {
      component: ModalScoringComponent,
    };
    const modalRef = this.seModalService.openModal(modalData);
    modalRef.componentInstance.trasspasarFormulariEvent
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((modalTrasspassar: ModalTrasspassar) => {
        this.trasspasarFormulari(modalTrasspassar, modalRef);
      });
    modalRef.componentInstance.setShowExceptionViewerEvent
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((showExceptionViewer: boolean) => {
        this.setShowExceptionViewerEvent.emit(showExceptionViewer);
      });
  }

  trasspasarFormulari(
    modalTrasspassar: ModalTrasspassar,
    modalRef: NgbModalRef,
  ): void {
    if (this.idTramit) {
      this.autoPendentsTramitarEndpointsService
        .trasspasarFormulari(this.idTramit, modalTrasspassar)
        .pipe(takeUntil(this.unsubscribe))
        .subscribe((response) => {
          if (response?.content) {
            modalRef.close();
            this.openModalSuccess(
              `${this.BASE_TRANSLATE}.MODAL_SCORING.TITLE_SUCCESS`,
              this.translateService.instant(
                `${this.BASE_TRANSLATE}.MODAL_SCORING.SUBTITLE_SUCCESS`,
                {
                  nom: modalTrasspassar.taxPayer.name,
                },
              ),
            );
          } else if (
            response?.messages?.length &&
            response?.messages[0]?.subtitle?.includes('TP000083')
          ) {
            this.seMessageService.resetMessages();
            this.seMessageService.addMessages([
              {
                severity: 'error',
                subtitle: `${this.BASE_TRANSLATE}.MODAL_SCORING.SCORING_ERROR`,
              },
            ]);
          }
        });
    }
  }

  openModalSuccess(title: string, subtitle: string): void {
    const modalData: SeModal = {
      severity: 'success',
      title,
      subtitle,
      closable: true,
      closableLabel: `${this.BASE_TRANSLATE}.BUTTONS.TANCAR`,
    };
    const modalRef = this.seModalService.openModal(modalData);

    modalRef.componentInstance.modalOutputEvent
      .pipe(takeUntil(this.unsubscribe))
      .subscribe(() => {
        modalRef.close();
        this.redirectSessionTreballEvent.emit();
      });
  }

  navigateBackwards(): void {
    this.backEvent.emit(true);
  }

  downloadDecZip(): void {
    this.downloadZipDecEvent.emit();
  }
}
