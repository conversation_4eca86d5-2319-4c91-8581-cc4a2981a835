{"/api/tributs/*": {"target": "http://localhost:8080/api/tributs", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/tributs": ""}}, "/mf/pt-documents-mf/*": {"target": "https://dev.seu2.atc.intranet.gencat.cat/mf/pt-documents-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/pt-documents-mf": ""}}, "/api/documents/*": {"target": "http://dev.seu2.atc.intranet.gencat.cat/api/documents", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/documents": ""}}, "/api/seguretat/*": {"target": "https://dev.seu2.atc.intranet.gencat.cat/api/seguretat", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/seguretat": ""}}, "/mf/pt-seguretat-mf/*": {"target": "https://dev.seu2.atc.intranet.gencat.cat/mf/pt-seguretat-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/pt-seguretat-mf": ""}}, "/api/pagaments/*": {"target": "https://dev.seu2.atc.intranet.gencat.cat/api/pagaments", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/pagaments": ""}}, "/mf/pt-pagaments-mf/*": {"target": "https://dev.seu2.atc.intranet.gencat.cat/mf/pt-pagaments-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/pt-pagaments-mf": ""}}, "/api/presentacions/*": {"target": "https://dev.seu2.atc.intranet.gencat.cat/api/presentacions", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/presentacions": ""}}, "/mf/pt-commons-mf/*": {"target": "https://dev.seu2.atc.intranet.gencat.cat/mf/pt-commons-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/pt-commons-mf": ""}}, "/api/recurs/*": {"target": "http://localhost:8080/api/recurs", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/recurs": ""}}, "/mf/se-seguretat-mf/*": {"target": "https://dev.seu2.atc.intranet.gencat.cat/mf/se-seguretat-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/se-seguretat-mf": ""}}, "/mf/se-documents-mf/*": {"target": "https://dev.seu2.atc.intranet.gencat.cat/mf/se-documents-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/se-documents-mf": ""}}, "/api/dades-referencia/*": {"target": "https://dev.seu2.atc.intranet.gencat.cat/api/dades-referencia", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/dades-referencia": ""}}}