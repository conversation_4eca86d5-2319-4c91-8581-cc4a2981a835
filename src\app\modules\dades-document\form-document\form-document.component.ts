import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import {
  Form<PERSON>uilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { Subject, firstValueFrom } from 'rxjs';
//LIBRARY
import {
  DateUtilsService,
  Nullable,
  SeAlertType,
  SeDropdownOption,
  SeValidations,
} from 'se-ui-components-mf-lib';
// APP
import { DadesDocumentEndpointsService } from '../dades-document-endpoints.service';
import {
  EXTRANGER_NOTARI,
  FormDocumentValues,
  RequestGetNotary,
  ResponseDocumentType,
  ResponseNotaryData,
} from '../models/dades-document.model';
import {
  BasicListResponse,
  DocumentTypes,
  SPAIN_CODE,
} from '../models/shared.model';
import { RequestGetMunicipalities } from '../services/address/address-endpoints.model';
import { AddressEndpointsService } from '../services/address/address-endpoints.service';
import { UtilsService } from '../services/utils.service';

@Component({
  selector: 'app-form-document',
  templateUrl: './form-document.component.html',
})
export class FormDocumentComponent implements OnInit, OnDestroy {
  @Input() formDocumentValues: Nullable<FormDocumentValues>;
  @Input() tooltipPrivatReference: Nullable<string>;

  @Output() componentFormOutput = new EventEmitter<FormGroup>();

  SeAlertType = SeAlertType;
  DocumentTypes = DocumentTypes;

  componentForm!: FormGroup;
  documentDate!: string;
  isForeign!: boolean;
  notaryOld!: boolean;
  maxDate: Date = new Date();
  paisList: SeDropdownOption[] = [];
  provinciaList: SeDropdownOption[] = [];
  municipiList: SeDropdownOption[] = [];
  optionsDocumentType: SeDropdownOption[] = [];
  notaryList: SeDropdownOption[] = [];

  isNotary1970 = false;
  private unsubscribe: Subject<void> = new Subject();

  constructor(
    private fb: FormBuilder,
    private endpointsService: AddressEndpointsService,
    private endpointsDocumentService: DadesDocumentEndpointsService,
    private translate: TranslateService,
    private utilsService: UtilsService,
    private dateUtilsService: DateUtilsService,
  ) {}

  async ngOnInit(): Promise<void> {
    this.setForm();
    await this.getDocumentType();
    await this.getPaisList();

    if (this.formDocumentValues) {
      this.setFormDocumentValues(this.formDocumentValues);
    }
  }

  ngOnDestroy(): void {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }

  private setForm(): void {
    this.componentForm = this.fb.group({
      documentType: [null, Validators.required],
      documentType_NAME: [null],
      documentDate: [null, Validators.required],
      pais: [null, Validators.required],
      municipi: [null],
      provincia: [null],
      pais_NAME: [null],
      municipi_NAME: [null],
      provincia_NAME: [null],
      notary: [null],
      notaryOld: [null],
      notary_NAME: [null],
      autor: [null],
      trib_jul: [null],
      reference: [null],
      PROT: [null],
      PROT_BIS: [null, [Validators.min(1), Validators.max(25)]],
      documentInfo: [null],
    });

    this.componentFormOutput.emit(this.componentForm);
  }

  private setValidatorsByDocumentType = (documentType: string): void => {
    this.utilsService.clearAndResetFormControl(this.componentForm, 'notaryOld');
    this.utilsService.clearAndResetFormControl(this.componentForm, 'notary');
    this.utilsService.clearAndResetFormControl(this.componentForm, 'reference');
    this.utilsService.clearAndResetFormControl(this.componentForm, 'autor');
    this.utilsService.clearAndResetFormControl(this.componentForm, 'trib_jul');
    this.utilsService.clearAndResetFormControl(this.componentForm, 'PROT');

    if (documentType === DocumentTypes.NOTARIAL) {
      this.utilsService.addRequiredValidator(this.componentForm, 'PROT', [
        Validators.required,
        SeValidations.greaterThan(0),
      ]);
      this.utilsService.addRequiredValidator(this.componentForm, 'notary');
    } else if (documentType === DocumentTypes.ADMINISTRATIU) {
      this.utilsService.addRequiredValidator(this.componentForm, 'reference');
      this.utilsService.addRequiredValidator(this.componentForm, 'autor');
    } else if (documentType === DocumentTypes.JUDICIAL) {
      this.utilsService.addRequiredValidator(this.componentForm, 'reference');
      this.utilsService.addRequiredValidator(this.componentForm, 'trib_jul');
    } else if (documentType === DocumentTypes.PRIVAT) {
      this.utilsService.addRequiredValidator(this.componentForm, 'reference');
    }
  };

  /**
   * List: Documen type
   */
  private getDocumentType = async (): Promise<void> => {
    this.optionsDocumentType = [];
    const response: ResponseDocumentType = await firstValueFrom(
      this.endpointsDocumentService.getDocumentType(),
    );
    if (response?.content) {
      this.optionsDocumentType = response.content;
    }
  };

  /**
   * List: pais
   * @description Get the country list (paisList)
   */
  private getPaisList = async (): Promise<void> => {
    this.paisList = [];
    const response: BasicListResponse = await firstValueFrom(
      this.endpointsService.getCountries$,
    );
    if (response?.content) {
      this.paisList = response.content;
    }
  };

  // Provinces (Províncies)
  private getProvinces = async (): Promise<void> => {
    this.provinciaList = [];
    const response: BasicListResponse = await firstValueFrom(
      this.endpointsService.getSpainProvinces$,
    );
    if (response?.content) {
      this.provinciaList = response.content;
    }
  };

  // Form field: municipi
  private getMunicipiList = async (
    provincia: string,
    cleanMunicipi = true,
  ): Promise<void> => {
    cleanMunicipi && this.cleanMunicipi();
    const request: RequestGetMunicipalities = { COD_PRV: provincia };
    this.municipiList = [];
    const response: BasicListResponse = await firstValueFrom(
      this.endpointsService.getMunicipalities(request),
    );

    if (response?.content) {
      this.municipiList = response.content;
    }
  };

  // Form field: Notari
  async getNotary(municipio: string, cleanNotary = true): Promise<void> {
    cleanNotary && this.cleanNotary();
    const request: RequestGetNotary = {
      province: this.componentForm.get('provincia')!.value,
      municipality: municipio,
      date: this.componentForm.get('documentDate')!.value,
    };

    const response: ResponseNotaryData = await firstValueFrom(
      this.endpointsDocumentService.getNotary(request),
    );
    this.notaryList = [];
    if (response?.content) {
      this.notaryList = response.content;
    }
  }

  documentDateChanged = async (): Promise<void> => {
    if (
      this.componentForm.get('municipi')?.value &&
      this.componentForm.get('documentType')!.value === DocumentTypes.NOTARIAL
    ) {
      await this.getNotary(this.componentForm.get('municipi')?.value);
    }
  };

  documentTypeChanged = (value: string): void => {
    if (value !== null) {
      //SET LABEL COUNTRY
      const documentType_NAME: string | undefined =
        this.utilsService.findListById(value, this.optionsDocumentType)?.label;
      this.componentForm.get('documentType_NAME')?.setValue(documentType_NAME);
      this.componentForm.get('pais')!.patchValue(SPAIN_CODE);
      this.paisChanged(SPAIN_CODE);
      this.setValidatorsByDocumentType(value);
    }
  };

  // Form: property, Field: pais
  paisChanged = async (value: string): Promise<void> => {
    //SET LABEL COUNTRY
    const pais_NAME: string | undefined = this.utilsService.findListById(
      value,
      this.paisList,
    )?.label;
    this.componentForm.get('pais_NAME')?.setValue(pais_NAME);
    //SET IS TRANGER
    this.isForeign = value !== SPAIN_CODE && value !== null;

    this.utilsService.clearAndResetFormControl(this.componentForm, 'municipi');
    this.utilsService.clearAndResetFormControl(this.componentForm, 'provincia');
    if (this.isForeign) {
      //IF FOREIGN AND NOTARY = NOTARY -- STRANGER VALUE
      if (
        this.componentForm.get('documentType')!.value === DocumentTypes.NOTARIAL
      ) {
        this.componentForm.get('notary')!.setValue(EXTRANGER_NOTARI);
        this.componentForm
          .get('notary_NAME')
          ?.setValue(
            this.translate.instant(
              'SE_TRIBUTS_MF.DADES_DOCUMENT.FORM_FIELDS.EXTRANGER',
            ),
          );
        this.componentForm.get('notary')!.disable();
      }
    } else {
      this.utilsService.addRequiredValidator(this.componentForm, 'municipi');
      this.utilsService.addRequiredValidator(this.componentForm, 'provincia');

      if (
        this.componentForm.get('documentType')!.value === DocumentTypes.NOTARIAL
      ) {
        this.componentForm.get('notary')!.enable();
        this.componentForm
          .get('notary')!
          .setValue(this.formDocumentValues?.notary ?? '');
        this.utilsService.addRequiredValidator(this.componentForm, 'PROT');
      }

      await this.getProvinces();
    }
  };

  // Form: property, Field: provincia
  provinciaChanged = async (value: string): Promise<void> => {
    // Update municipi
    if (value) {
      await this.getMunicipiList(value);
      //SET LABEL MUNICIPALITY
      const provincia_NAME: string | undefined = this.utilsService.findListById(
        value,
        this.provinciaList,
      )?.label;
      this.componentForm.get('provincia_NAME')?.setValue(provincia_NAME);
      //clean notarios
      this.cleanNotary();
    } else {
      this.municipiList.length = 0;
    }
  };

  // Form: property, Field: provincia
  async municipiChanged(value: string): Promise<void> {
    // Update municipi
    if (value) {
      //SET LABEL PROVINCE
      const mun_NAME: string | undefined = this.utilsService.findListById(
        value,
        this.municipiList,
      )?.label;
      this.componentForm.get('municipi_NAME')?.setValue(mun_NAME);
      //GET NOTARY
      if (this.componentForm.get('documentType')!.value === 'NT') {
        await this.getNotary(value);
      }
    } else {
      this.notaryList.length = 0;
    }
  }

  notaryChanged = (value: string): void => {
    const notary_NAME: string | undefined = this.utilsService.findListById(
      value,
      this.notaryList,
    )?.label;
    this.componentForm.get('notary_NAME')?.setValue(notary_NAME);
  };

  checkNotary = (): boolean => {
    return (
      !this.isForeign &&
      ((this.isControlValid('documentType') &&
        this.isControlValid('municipi') &&
        this.isControlValid('PROT') &&
        this.isControlValid('documentDate') &&
        !this.isControlValid('notary') &&
        (this.componentForm.get('notary')!.dirty ||
          this.componentForm.get('notary')!.touched)) ||
        this.componentForm.get('notaryOld')?.value)
    );
  };

  private isControlValid = (name: string): boolean => {
    return this.componentForm.get(name)!.valid;
  };

  applyNotaryOld = (value: boolean): void => {
    this.isNotary1970 = value;
    this.utilsService.clearAndResetFormControl(this.componentForm, 'notary');
    if (value) {
      this.componentForm.get('notary')!.disable();
      this.componentForm
        .get('notary')!
        .setValue(
          this.translate.instant(
            'SE_TRIBUTS_MF.DADES_DOCUMENT.FORM_FIELDS.NOTARY_OLD_INPUT',
          ),
        );
    } else {
      this.utilsService.addRequiredValidator(this.componentForm, 'notary');
      this.componentForm.get('notary')!.enable();
    }
  };

  async setFormDocumentValues(
    formDocumentValues: FormDocumentValues,
  ): Promise<void> {
    //INIT VALUES AND VALIDATIONS
    this.setValidatorsByDocumentType(formDocumentValues.documentType);
    this.paisChanged(formDocumentValues.pais);
    //SET VALUES
    const documentDate =
      typeof formDocumentValues.documentDate === 'string'
        ? this.dateUtilsService.parseDate(formDocumentValues.documentDate)
        : formDocumentValues.documentDate;
    this.componentForm.patchValue(
      {
        ...this.formDocumentValues,
        documentDate,
      },
      {
        emitEvent: false,
        onlySelf: true,
      },
    );
    //GET LISTS
    if (formDocumentValues.provincia) {
      await this.getMunicipiList(formDocumentValues.provincia, false);
    }
    //OTHER CONF
    if (
      formDocumentValues.documentType === DocumentTypes.NOTARIAL &&
      formDocumentValues.municipi
    ) {
      if (formDocumentValues.notaryOld) {
        this.applyNotaryOld(true);
      }
      await this.getNotary(formDocumentValues.municipi, false);
    }
    this.setUbicacioName(formDocumentValues);
  }

  setUbicacioName(formDocumentValues: FormDocumentValues): void {
    const documentType_NAME: string | undefined =
      this.utilsService.findListById(
        formDocumentValues.documentType,
        this.optionsDocumentType,
      )?.label;
    this.componentForm.get('documentType_NAME')?.setValue(documentType_NAME);
    const pais_NAME: string | undefined = this.utilsService.findListById(
      formDocumentValues.pais,
      this.paisList,
    )?.label;
    this.componentForm.get('pais_NAME')?.setValue(pais_NAME);
    const provincia_NAME: string | undefined = this.utilsService.findListById(
      formDocumentValues.provincia,
      this.provinciaList,
    )?.label;

    this.componentForm.get('provincia_NAME')?.setValue(provincia_NAME);
    const mun_NAME: string | undefined = this.utilsService.findListById(
      formDocumentValues.municipi,
      this.municipiList,
    )?.label;
    this.componentForm.get('municipi_NAME')?.setValue(mun_NAME);
    const notary_NAME: string | undefined = this.utilsService.findListById(
      formDocumentValues.notary ?? '',
      this.notaryList,
    )?.label;
    this.componentForm.get('notary_NAME')?.setValue(notary_NAME);
  }

  showDocumentForm(): void {
    return (
      this.componentForm.get('documentType')!.value &&
      this.componentForm.get('documentDate')!.value
    );
  }

  cleanProtBis(event: Event): void {
    if (event) {
      this.componentForm
        .get('PROT_BIS')
        ?.setValue(event.toString().replace(/^0+/, ''));
    }
  }

  cleanNotary(): void {
    this.utilsService.clearAndResetFormControl(this.componentForm, 'notary');
    this.utilsService.clearAndResetFormControl(this.componentForm, 'notaryOld');
    this.componentForm.get('documentType')?.value === DocumentTypes.NOTARIAL &&
      this.utilsService.addRequiredValidator(this.componentForm, 'notary');
    this.notaryList = [];
    this.isNotary1970 = false;
  }

  cleanMunicipi(): void {
    this.utilsService.clearAndResetFormControl(this.componentForm, 'municipi');
    this.utilsService.addRequiredValidator(this.componentForm, 'municipi');
    this.municipiList = [];
  }

  getReferenceTooltip(): string {
    const tooltip =
      this.componentForm.get('documentType')!.value ===
      DocumentTypes.ADMINISTRATIU
        ? 'REFERENCE_AD'
        : 'REFERENCE_JT';
    return 'SE_TRIBUTS_MF.DADES_DOCUMENT.TOOLTIPS.' + tooltip;
  }

  getFormControl(name: string): FormControl {
    return this.componentForm.get(name) as FormControl;
  }
}
