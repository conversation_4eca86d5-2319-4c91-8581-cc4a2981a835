import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';

import { TranslateModule } from '@ngx-translate/core';
import { SeInputModule } from 'se-ui-components-mf-lib';
import { TaxFormFieldComponent } from './tax-form-field';
import { TaxFormGroupComponent } from './tax-form-group';
import { TaxInputComponent } from './tax-input';

const components = [
  TaxInputComponent,
  TaxFormFieldComponent,
  TaxFormGroupComponent,
];

@NgModule({
  declarations: [
    TaxInputComponent,
    TaxFormFieldComponent,
    TaxFormGroupComponent,
  ],
  imports: [CommonModule, ReactiveFormsModule, TranslateModule, SeInputModule],
  exports: [TaxFormGroupComponent],
})
export class TaxFormsModule {}
