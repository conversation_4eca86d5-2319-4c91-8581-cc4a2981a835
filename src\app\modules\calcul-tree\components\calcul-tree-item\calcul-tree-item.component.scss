@import 'bootstrap/scss/functions';
@import 'bootstrap/scss/variables';
@import 'bootstrap/scss/variables-dark';
@import 'bootstrap/scss/mixins/breakpoints';

:host {
  display: block;

  &:not(:last-of-type) {
    margin-bottom: 0.75rem;
  }
}

.calcul-tree-item {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  gap: 10px;
  align-items: center;

  &__key {
    color: var(--color-gray-700);

    &.secondary {
      color: var(--color-gray-600);
    }

    &.bold {
      font-weight: bold;
    }
  }

  &__value {
    font-size: var(--text-sm);
    line-height: var(--line-sm);
    min-width: fit-content;

    @include media-breakpoint-up(sm) {
      max-width: 50%;
    }

    @include media-breakpoint-up(md) {
      max-width: 75%;
    }
  }

  &__separator {
    position: relative;
    flex: 1 0 0%;
    min-width: 15%;

    &::before {
      content: '';
      position: absolute;
      border-top: 2px dotted var(--color-gray-400);
      left: 0;
      width: 100%;
    }
  }

  &__children {
    padding: 0.75rem 0 0 1rem;
  }

  &__square {
    display: inline-block;
    border: 1px solid currentcolor;
    padding: 0 0.125rem; /* 0 4px 0 4px */
  }

  &__clickable {
    border: none;
    padding: 0;
    background: transparent;
    text-decoration: underline;
    color: var(--color-blue-500);
    font-size: var(--text-sm);
    line-height: var(--line-sm);
  }
}
