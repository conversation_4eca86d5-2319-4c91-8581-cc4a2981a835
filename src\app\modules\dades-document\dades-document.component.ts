import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  Output,
} from '@angular/core';
import { FormGroup } from '@angular/forms';
import { TAX_MODEL } from '@core/models';
import { Subject, takeUntil } from 'rxjs';
import {
  ItemSelectableCard,
  Nullable,
  SeModal,
  SeModalService,
} from 'se-ui-components-mf-lib';
import { ModalModificarDocumentComponent } from './modal-modificar-document/modal-modificar-document.component';
import { Autoliquidacions } from './models/autoliquidacions.model';
import {
  FormDocumentValues,
  RequestFormDocument,
} from './models/dades-document.model';
import { DadesDocumentService } from './services/dades-document.service';

@Component({
  selector: 'app-dades-document',
  templateUrl: './dades-document.component.html',
  styleUrls: ['./dades-document.component.scss'],
})
export class DadesDocumentComponent implements OnDestroy {
  @Input() impost: Nullable<TAX_MODEL>;
  @Input() codiTipusOperacio: Nullable<string>;
  @Input() showResum: boolean = false;
  @Input() tooltipPrivatReference: Nullable<string>;

  _formDocumentValues: Nullable<FormDocumentValues>;

  @Input() set formDocumentValues(
    formDocumentValues: Nullable<FormDocumentValues>,
  ) {
    if (formDocumentValues) {
      this._formDocumentValues = formDocumentValues;
      this.checkAndLoadForm(formDocumentValues);
    }
  }

  get formDocumentValues(): Nullable<FormDocumentValues> {
    return this._formDocumentValues;
  }

  @Output() formDocumentValuesChange = new EventEmitter<
    Nullable<FormDocumentValues>
  >();
  @Output() updateFormulari: EventEmitter<RequestFormDocument> =
    new EventEmitter<RequestFormDocument>();
  autoliquidacions: Autoliquidacions[] = [];

  componentForm: FormGroup | undefined;

  cardDocument: ItemSelectableCard[] = [];

  private unsubscribe: Subject<void> = new Subject();

  constructor(
    private ddService: DadesDocumentService,
    private seModalService: SeModalService,
  ) {}

  ngOnDestroy(): void {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }

  async checkAndLoadForm(
    formDocumentValues: FormDocumentValues,
  ): Promise<void> {
    this.showResum = true;
    this.cardDocument =
      await this.ddService.getResumDocumentValues(formDocumentValues);
  }

  getFormData = (form: FormGroup): void => {
    this.componentForm = form;
  };

  isValidForm(): boolean {
    if (this.componentForm) {
      return !(
        this.componentForm.get('documentType')?.valid &&
        this.componentForm.get('documentDate')?.valid
      );
    }
    return true;
  }

  async validateForm(): Promise<void> {
    if (this.componentForm) {
      this.componentForm.markAsDirty();
      this.componentForm.markAllAsTouched();

      if (this.componentForm.valid) {
        this.formDocumentValues = this.componentForm.getRawValue();
        this.formDocumentValuesChange.emit(this.formDocumentValues);
      }
    }
  }

  openModifyModalDocument = async (): Promise<void> => {
    if (this.formDocumentValues) {
      const modalData: SeModal = {
        component: ModalModificarDocumentComponent,
        size: 'xl',
      };
      // Open modal
      const modalRef = this.seModalService.openModal(modalData);

      modalRef.componentInstance.formDocumentValues = this.formDocumentValues;
      modalRef.componentInstance.tooltipPrivatReference =
        this.tooltipPrivatReference;
      modalRef.componentInstance.impost = this.impost;
      modalRef.componentInstance.codiTipusOperacio = this.codiTipusOperacio;

      // Modal output data
      modalRef.componentInstance.modalOutput
        .pipe(takeUntil(this.unsubscribe))
        .subscribe((output: FormGroup) => {
          if (output) {
            //MODIFY
            this.getFormData(output);
            this.validateForm();
          }
        });
      modalRef.componentInstance.updateFormulari
        .pipe(takeUntil(this.unsubscribe))
        .subscribe((request: RequestFormDocument) => {
          this.updateFormulari.emit(request);
        });
    }
  };
}
