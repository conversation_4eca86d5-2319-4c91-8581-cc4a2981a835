import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';

import { ReactiveFormsModule } from '@angular/forms';
import { NgIconsModule } from '@ng-icons/core';
import { TranslateModule } from '@ngx-translate/core';
import {
  SeButtonModule,
  SeDatepickerModule,
  SeDisclosureModule,
  SeInputModule,
  SePanelModule,
  SeRadioModule,
  SeSwitchModule,
  SeTooltipAccessibleModule,
} from 'se-ui-components-mf-lib';
import { CalculResumComponent } from './components/calcul-resum/calcul-resum.component';
import { CalculTreeItemComponent } from './components/calcul-tree-item/calcul-tree-item.component';
import { CalculTreeViewComponent } from './components/calcul-tree-view/calcul-tree-view.component';

@NgModule({
  declarations: [
    CalculResumComponent,
    CalculTreeViewComponent,
    CalculTreeItemComponent,
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule,
    SePanelModule,
    SeButtonModule,
    SeDisclosureModule,
    SeInputModule,
    SeDatepickerModule,
    SeSwitchModule,
    SeRadioModule,
    SeTooltipAccessibleModule,
    NgIconsModule,
  ],
  exports: [CalculResumComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class CalculTreeModule {}
