import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { SeAlertModule, SeRadioModule } from 'se-ui-components-mf-lib';

import { ConfirmRecoverDraftComponent } from './confirm-recover-draft.component';
@NgModule({
  declarations: [
    ConfirmRecoverDraftComponent,
  ],
  imports: [CommonModule, ReactiveFormsModule, TranslateModule, SeAlertModule, SeRadioModule],
  exports: [ConfirmRecoverDraftComponent],
})
export class ConfirmRecoverDraftModule { }