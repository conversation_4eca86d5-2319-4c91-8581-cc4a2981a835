import {
  Component,
  EventEmitter,
  Input,
  Output,
  type OnDestroy,
} from '@angular/core';
import { Subject, takeUntil } from 'rxjs';

import {
  RequestInterestCalculator,
  RequestPeriodInfo,
  ResponsePeriodInfo,
  FormData,
  SendFormData,
  CalculInfo,
  SummaryTemplate,
  MODEL,
  ResumeData,
  ModalTaxRateDetail,
} from './calculations.model';
import { CalculationsEndpointService } from './calculations-endpoint.service';

@Component({
  selector: 'app-calculations',
  templateUrl: './calculations.component.html',
  styleUrls: ['./calculations.component.scss'],
})
export class CalculationsComponent implements OnDestroy {
  protected isResumeDetailsExpanded = false;
  protected isResumePanelVisible = false;

  // Parameters
  surcharge: number = 0.0;
  interest: number = 0.0;
  aplicarRecarrec: boolean = true;
  responsePeriodInfo: ResponsePeriodInfo | null = null;
  showSurchargeComponent: boolean = false;
  dataSurchargeComponent: SendFormData | null = null;
  @Output() formData: EventEmitter<SendFormData | boolean> = new EventEmitter<
    SendFormData | boolean
  >();

  calculationInfoValue?: CalculInfo;
  requestPeriodInfoValue?: RequestPeriodInfo;

  @Input() set calculationInfo(value: CalculInfo) {
    this.calculationInfoValue = value;
    this.getPeriodInfoAndSurcharge();
  }

  @Input() set requestPeriodInfo(value: RequestPeriodInfo) {
    this.requestPeriodInfoValue = value;
    this.getPeriodInfoAndSurcharge();
  }

  @Input() summaryTemplate: SummaryTemplate[] | undefined;

  @Input() modalTaxRateDetail: ModalTaxRateDetail | undefined;

  @Input() resumeList: ResumeData[] = [];

  // Flag to use common summary template. If false it will use only the specific template : @Input() summaryTemplate.
  @Input() useCommonSummaryTemplate: boolean = true;

  private destroyed$ = new Subject<void>();

  constructor(private calculationsService: CalculationsEndpointService) {
    console.log(
      'Webcomponent: SE Tributs > CalculationsComponent > constructor',
    );
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  protected get arrowIcon(): string {
    return this.isResumeDetailsExpanded
      ? 'matKeyboardArrowUpOutline'
      : 'matKeyboardArrowDownOutline';
  }

  private getPeriodInfoAndSurcharge(): void {
    if (
      this.calculationInfoValue !== undefined &&
      this.requestPeriodInfoValue !== undefined
    ) {
      this.isResumePanelVisible =
        this.requestPeriodInfoValue.model === MODEL.CUARENTA;

      this.calculationsService
        .getPeriodData(this.requestPeriodInfoValue)
        .pipe(takeUntil(this.destroyed$))
        .subscribe((response) => {
          const data: ResponsePeriodInfo = response.content;
          this.showSurchargeComponent =
            this.calculationsService.voluntaryPeriodEnd(data.dataFiVoluntaria);
          if (this.showSurchargeComponent) {
            this.calculationsService.emitPeriodData(data);
          } else {
            const formData: FormData = {
              amount: 0,
              applyDiscount: false,
              baseCalculation: 0,
              countingEndDate: new Date(data.dataFiVoluntaria),
              countingStartDate: new Date(data.dataIniciPeriode),
              deadLineDate: new Date(data.dataFiVoluntaria),
              import: 0,
              isRequirement: false,
              liquidateSurcharge: false,
              typeSurcharge: '',
              total: this.calculationInfoValue?.quotaTotal ?? 0,
            };
            this.getDataForm(formData);
          }
        });
    }
  }

  protected callInterestLatePayment(data: RequestInterestCalculator): void {
    const { request, execute } = data;
    if (!request || !execute) {
      this.calculationsService.emitInterestData({ totalAmount: 0.0 });
      return;
    }
    this.calculationsService
      .getInterestCalculator(request)
      .pipe(takeUntil(this.destroyed$))
      .subscribe((data) => {
        if (data?.content && data?.content?.length > 0) {
          this.interest = data.content[0].totalAmount;
          this.calculationsService.emitInterestData(data.content[0]);
        }
      });
  }

  protected getDataForm(form?: FormData): void {
    this.dataSurchargeComponent = new SendFormData(
      this.calculationInfoValue?.quotaLiquidades ?? 0,
      form,
    );
    this.aplicarRecarrec = this.dataSurchargeComponent.aplicarRecarrec ?? false;
    this.surcharge = this.dataSurchargeComponent.recarrec ?? 0;
    this.interest = this.dataSurchargeComponent.interessos ?? 0;
    this.formData.emit(this.dataSurchargeComponent);
  }
}
