import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
//LIBRARY
import {
  SeHttpRequest,
  SeHttpResponse,
  SeHttpService,
} from 'se-ui-components-mf-lib';

import { TAX_MODEL } from '@core/models';
import { environment } from 'src/environments/environment';
import {
  RequestFormDocument,
  RequestGetNotary,
  ResponseDocumentType,
  ResponseNotaryData,
} from './models/dades-document.model';
import { DraftTributs } from './models/draft-tributs.model';

@Injectable({
  providedIn: 'root',
})
export class DadesDocumentEndpointsService {
  constructor(private httpService: SeHttpService) {}

  /**
   * Get list of document types
   */
  getDocumentType = (): Observable<ResponseDocumentType> => {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlDadesReferencia,
      url: `/tipus-document-formulari`,
      method: 'get',
    };
    return this.httpService.get(httpRequest);
  };

  /**
   * Get list of notarys
   * @description
   */
  getNotary = (request: RequestGetNotary): Observable<ResponseNotaryData> => {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlTributs,
      url: `/notaris`,
      method: 'post',
      body: request,
    };
    return this.httpService.post(httpRequest);
  };

  /**
   * Get Working Session
   * Listado formularios
   * @description
   */
  getWorkingSession = (
    request: RequestFormDocument,
    IMPOST: TAX_MODEL,
  ): Observable<SeHttpResponse<DraftTributs[]>> => {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlTributs,
      url: `/sessions-treball/${IMPOST}/document-operacio`,
      method: 'post',
      body: request,
    };
    return this.httpService.post(httpRequest);
  };
}
