import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  Output,
} from '@angular/core';
import { Form<PERSON>uilder, FormControl, FormGroup } from '@angular/forms';
import { Subject } from 'rxjs';
import {
  DeclaracioResponsableChange,
  ScoringValidationChange,
  Taxpayer,
  TaxpayerChange,
} from './models';
import { UserService } from '@core/services';
import { Nullable } from 'se-ui-components-mf-lib';

@Component({
  selector: 'app-taxpayer',
  templateUrl: './taxpayer.component.html',
  styleUrls: ['./taxpayer.component.scss'],
})
export class TaxpayerComponent implements OnDestroy {
  @Input({ required: true }) procedureCode!: string;
  @Input() title!: Nullable<string>;
  @Input() description!: Nullable<string>;
  @Input() taxPayerDocumentLabel!: Nullable<string>;
  @Input() taxPayerNameLabel!: Nullable<string>;
  @Output() onChange = new EventEmitter<TaxpayerChange>();

  @Input() set taxPayerValidated(value: Nullable<Taxpayer>) {
    if (value?.name && value?.nif) {
      this.setTaxPayerValuesAlreadyLoadedInScoring(value);
      this.alreadyLoadedInScoring = true;
    }
  }

  protected declaracioResponsableTaxpayers: Taxpayer[];
  protected isScoringDisabled: boolean;
  protected scoringTaxpayer: Taxpayer | undefined;
  protected taxpayerForm: FormGroup<{
    copyPresenterDataCheckbox: FormControl<boolean>;
  }>;
  protected declaracioResponsable: { checked: boolean; visible: boolean };
  protected isVisibleDeclarationResponsible = false;

  private destroyed$: Subject<void>;
  private isScoringValid: boolean;
  private alreadyLoadedInScoring: boolean = false;

  constructor(private userService: UserService, private cdr: ChangeDetectorRef) {
    console.log('Webcomponent: SE Tributs > TaxpayerComponent > constructor');    

    this.declaracioResponsableTaxpayers = [];
    this.isScoringDisabled = false;

    const fb = new FormBuilder().nonNullable;
    this.taxpayerForm = fb.group({
      copyPresenterDataCheckbox: fb.control(false),
    });

    this.declaracioResponsable = { checked: false, visible: false };
    this.destroyed$ = new Subject<void>();
    this.isScoringValid = false;
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  protected onCheckboxClicked(checked: boolean): void {
    if (checked) {
      this.copyPresenterData();
    } else {
      this.resetScoringForm();
    }
  }

  private setTaxPayerValuesAlreadyLoadedInScoring(
    taxPayerValue: Taxpayer
  ): void {
    this.isScoringValid = true;
    this.scoringTaxpayer = { ...taxPayerValue };
    this.taxpayerForm.patchValue({
      copyPresenterDataCheckbox: taxPayerValue.isPresenter,
    });
    this.declaracioResponsableTaxpayers = [{ ...this.scoringTaxpayer }];    
    this.isVisibleDeclarationResponsible = !taxPayerValue.isPresenter;       
    this.declaracioResponsable = {
      checked: !!taxPayerValue.haveAcceptedDeclarationOfResponsibility,
      visible: !taxPayerValue.isPresenter,
    };    
  }

  private copyPresenterData(): void {
    this.isVisibleDeclarationResponsible = false;
    this.isScoringValid = true;
    this.scoringTaxpayer = {
      nif: this.userService.getNIF(),
      name: this.userService.getName(),
      isPresenter: true,
    };
    this.declaracioResponsableTaxpayers = [];
    this.declaracioResponsable = { checked: false, visible: false };
    this.isScoringDisabled = true;
    this.emitTaxpayerChange();
    this.cdr.detectChanges();
  }

  private resetScoringForm(): void {
    this.isVisibleDeclarationResponsible = true;
    this.isScoringValid = false;
    this.scoringTaxpayer = {
      nif: '',
      name: '',
      isPresenter: false,
      haveAcceptedDeclarationOfResponsibility: false,
    };
    this.declaracioResponsableTaxpayers = [];
    this.declaracioResponsable = { checked: false, visible: false };
    this.isScoringDisabled = false;
    this.emitTaxpayerChange();
  }

  protected onScoringValidationChange(event: Event) {
    const customEvent = event as CustomEvent<ScoringValidationChange>;
    const { valid } = customEvent.detail;
    this.isScoringValid = valid;
    this.declaracioResponsableTaxpayers = valid ? [{ ...this.scoringTaxpayer! }] : [];
    this.isVisibleDeclarationResponsible = valid;
    this.emitTaxpayerChange();
  }

  protected onScoringTaxpayerChange(event: Event): void {
    const customEvent = event as CustomEvent<Taxpayer>;
    this.scoringTaxpayer = { ...customEvent.detail };
    this.emitTaxpayerChange();
  }

  protected onDeclaracioResponsableChange(event: Event): void {    
    if (this.scoringTaxpayer?.isPresenter) {
      this.isVisibleDeclarationResponsible = false;
    } else if (!this.alreadyLoadedInScoring) {
      const customEvent = event as CustomEvent<DeclaracioResponsableChange>;  
      this.declaracioResponsable = { ...customEvent.detail };
      this.isVisibleDeclarationResponsible = customEvent.detail.visible;
      this.emitTaxpayerChange();
    } else {
      this.alreadyLoadedInScoring = false;
    }
  }

  private emitTaxpayerChange(): void {
    this.onChange.emit({
      declaracioResponsable: this.declaracioResponsable,
      isScoringValid: this.isScoringValid,
      name: this.scoringTaxpayer?.name!,
      nif: this.scoringTaxpayer?.nif!,
      isPresenter: this.taxpayerForm.get('copyPresenterDataCheckbox')?.value,
    });
  }
}
