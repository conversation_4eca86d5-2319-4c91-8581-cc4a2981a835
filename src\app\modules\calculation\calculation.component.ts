import { Component, OnInit, type <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { Subject, takeUntil } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { Router } from '@angular/router';
import { CurrencyPipe, DatePipe } from '@angular/common';

import { CalculationEndpointService } from './calculation-endpoint.service';
import {
  CalculInfo,
  RequestPeriodInfo,
  RequestSaveSelfAssessment,
  ResponseCalcul,
  SummaryTemplate,
} from './calculation.model';
import { Constant } from 'src/app/core/models/constants.enum';
import { StoreService } from '@core/services';
import { AppRoutes } from '@core/models/app-routes.enum';
import { Nullable } from 'se-ui-components-mf-lib';

@Component({
  selector: 'app-calculation',
  styleUrls: [],
  templateUrl: 'calculation.component.html',
})
export class CalculationComponent implements OnInit, OnDestroy {
  private destroyed$: Subject<void>;

  /* CC SURCHARGE/INTEREST/SUMMARY */
  protected requestPeriodInfo: RequestPeriodInfo | undefined;
  protected calculationInfo: CalculInfo | undefined;
  protected template: SummaryTemplate[] | undefined;

  /* INFORMATION CALCULATION */
  private allCalculationInfo: ResponseCalcul | undefined;

  /* SAVE SELFASSESSMENT */
  private selfAssessmentInfo: RequestSaveSelfAssessment;
  protected disabledButtonNext: boolean = true;

  protected alertTitle: string | undefined;

  constructor(
    private store: StoreService,
    private datePipe: DatePipe,
    private currencyPipe: CurrencyPipe,
    private calculationService: CalculationEndpointService,
    private translateService: TranslateService,
    private router: Router,
  ) {
    this.destroyed$ = new Subject<void>();
    this.selfAssessmentInfo = new RequestSaveSelfAssessment();
  }

  ngOnInit(): void {
    const idTramit = this.store.idTramit;
    const year = this.store.taxYear;

    if (idTramit && year) {
      this.addRequestPeriodInformation(year.toString());
      this.callCalcul(idTramit);
    }
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  protected closeAlert(): void {
    this.alertTitle = undefined;
  }

  private addRequestPeriodInformation(year: string): void {
    this.requestPeriodInfo = {
      impost: Constant.NAME,
      year: year,
      period: Constant.ANUAL_PERIOD,
      model: Constant.MODEL,
    };
  }

  private callCalcul(idTramit: string): void {
    this.calculationService
      .getCalcul(idTramit)
      .pipe(takeUntil(this.destroyed$))
      .subscribe((response) => {
        if (response?.content) {
          this.allCalculationInfo = response.content;
          this.calculationInfo = {
            quotaTotal: this.allCalculationInfo.quotaLiquida,
            quotaLiquidades:
              this.allCalculationInfo.quotesLiquidadesAnteriorment,
          };
          this.createSummaryTemplate();
        }
      });
  }

  private formatTemplateInfo(info: number | undefined): Nullable<string> {
    return info && info > 0
      ? this.currencyPipe.transform(info, 'EUR', 'symbol', '1.2-2')
      : '0,00 €';
  }

  private createSummaryTemplate(): void {
    this.template = [
      {
        translate: this.translateService.instant(
          'SE_GASOS_MF.MODULE_CALCULATION.TEMPLATE.QUOTA',
        ),
        value: this.formatTemplateInfo(
          this.allCalculationInfo?.quotaIntegra || 0,
        ),
      },
      {
        translate: this.translateService.instant(
          'SE_GASOS_MF.MODULE_CALCULATION.TEMPLATE.BONIFICATIONS',
        ),
        value: this.formatTemplateInfo(
          this.allCalculationInfo?.bonificacion || 0,
        ),
      },
      {
        translate: this.translateService.instant(
          'SE_GASOS_MF.MODULE_CALCULATION.TEMPLATE.LIQUID',
        ),
        value: this.formatTemplateInfo(
          this.allCalculationInfo?.quotaLiquida || 0,
        ),
      },
      {
        translate: this.translateService.instant(
          'SE_GASOS_MF.MODULE_CALCULATION.TEMPLATE.PREVIOUS',
        ),
        value: this.formatTemplateInfo(
          this.allCalculationInfo?.quotesLiquidadesAnteriorment || 0,
        ),
      },
      {
        translate: this.translateService.instant(
          'SE_GASOS_MF.MODULE_CALCULATION.TEMPLATE.RESULT',
        ),
        value: this.formatTemplateInfo(
          this.allCalculationInfo?.quotaResultant || 0,
        ),
      },
      {
        translate: this.translateService.instant(
          'SE_GASOS_MF.MODULE_CALCULATION.TEMPLATE.SURCHARGE',
        ),
        value: this.formatTemplateInfo(this.selfAssessmentInfo?.recarrec || 0),
      },
      {
        translate: this.translateService.instant(
          'SE_GASOS_MF.MODULE_CALCULATION.TEMPLATE.INTEREST',
        ),
        value: this.formatTemplateInfo(
          this.selfAssessmentInfo?.interessos || 0,
        ),
      },
    ];
  }

  private formatDates(): void {
    if (this.selfAssessmentInfo?.dataIniciComput) {
      const initComput = this.datePipe.transform(
        this.selfAssessmentInfo.dataIniciComput,
        'yyyy-MM-dd',
      );
      this.selfAssessmentInfo.dataIniciComput = initComput;
    }
    const endComput = this.datePipe.transform(
      this.selfAssessmentInfo.dataFiComput,
      'yyyy-MM-dd',
    );
    const endVoluntary = this.datePipe.transform(
      this.selfAssessmentInfo.dataFiTermini,
      'yyyy-MM-dd',
    );
    this.selfAssessmentInfo.dataFiComput = endComput!;
    this.selfAssessmentInfo.dataFiTermini = endVoluntary!;
  }

  protected handleButtonNext(): void {
    this.disabledButtonNext = !!(
      this.selfAssessmentInfo?.requerimentAdministracio === null ||
      !this.requestPeriodInfo ||
      !this.calculationInfo ||
      (this.selfAssessmentInfo.total <= 0 &&
        !this.store.numJustificantComplementari) ||
      (this.selfAssessmentInfo.total < 0 &&
        this.store.numJustificantComplementari)
    );
  }

  private setComplementariaMessageAlert(totalToPay: number): void {
    if (totalToPay < 0 && this.store.numJustificantComplementari) {
      // complementaria < 0
      this.alertTitle = this.translateService.instant(
        'SE_GASOS_MF.MODULE_CALCULATION.ALERTS.DISABLED_COMPLEMENTARY',
      );
    }
  }

  protected getDataFromTributs(event: Event): void {
    if (!(event as CustomEvent).detail) {
      this.disabledButtonNext = false;
    } else {
      this.selfAssessmentInfo = (event as CustomEvent).detail;
      if (this.selfAssessmentInfo.interessos === 0) {
        this.selfAssessmentInfo.dataIniciComput = null;
      }
      const total = this.getTotalValue();
      this.selfAssessmentInfo.total = total;
      this.handleButtonNext();
      this.createSummaryTemplate();
      this.setComplementariaMessageAlert(total);
    }
  }

  private getTotalValue(): number {
    const surcharge = +this.selfAssessmentInfo.recarrec || 0;
    const liquidades = +this.selfAssessmentInfo.quotasLiquidades || 0;
    const interest = +this.selfAssessmentInfo.interessos || 0;
    const totalToPay =
      (this.calculationInfo?.quotaTotal || 0) +
      surcharge +
      interest -
      liquidades;

    return Number(totalToPay.toFixed(2));
  }

  protected submit(): void {
    this.formatDates();
    this.selfAssessmentInfo.idTramit = this.store.idTramit!;
    this.selfAssessmentInfo.model = Constant.MODEL;
    this.selfAssessmentInfo.total =
      this.selfAssessmentInfo.total < 0 ? 0 : this.selfAssessmentInfo.total;
    this.calculationService
      .saveSelfAssessment(this.selfAssessmentInfo)
      .pipe(takeUntil(this.destroyed$))
      .subscribe((idAutoliquidacio) => {
        if (idAutoliquidacio?.content) {
          this.store.selfAssessmentId = idAutoliquidacio.content;
          this.store.amountToPay = this.selfAssessmentInfo.total;
          this.router.navigate([AppRoutes.PRESENTATION]);
        }
      });
  }

  protected goBack(): void {
    this.router.navigate([AppRoutes.TAX_DECLARATION]);
  }
}
