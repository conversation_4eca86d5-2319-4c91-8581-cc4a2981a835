const fs = require('fs-extra');  
const concat = require('concat');
var pjson = require('./package.json');

(async function build() {
    // Reset previous version
    await fs.emptyDir('./elements');

    // Concatenate Webcomponent JS files
    const files = [
        // './dist/se-tributs-mf/runtime.js',
        // './dist/se-tributs-mf/polyfills.js',
        // './dist/se-tributs-mf/scripts.js',
        // './dist/se-tributs-mf/main.js'
    ];

    await fs.ensureDir('./dist/se-tributs-mf');
    const files1 = await fs.readdir('./dist/se-tributs-mf/');
    files1.filter(file => file.match(/(\w*)\.js$/)).forEach(function (file) {
        // Do whatever you want to do with the file
        //console.log(file)
        files.push('./dist/se-tributs-mf/'+file)
    });
    
    await fs.ensureDir('./elements');
    await concat(files, './elements/se-tributs.js');

    // Copy styles
    await fs.copyFile('./dist/se-tributs-mf/styles.css', './elements/styles.css');

    // Copy primeng fonts & icons
    // await fs.copyFile('./dist/se-tributs-mf/primeicons.eot', './elements/primeicons.eot');
    // await fs.copyFile('./dist/se-tributs-mf/primeicons.svg', './elements/primeicons.svg');
    // await fs.copyFile('./dist/se-tributs-mf/primeicons.ttf', './elements/primeicons.ttf');
    // await fs.copyFile('./dist/se-tributs-mf/primeicons.woff', './elements/primeicons.woff');

    // Copy assets
    await fs.ensureDir('./elements/assets');
    await fs.copy('./dist/se-tributs-mf/assets', './elements/assets');

    var buildtime = new Date();
    var versionJson = "{\"build\":{\"version\":\""
        + pjson.version + "\",\"name\":\""
        + pjson.name + "\",\"time\":\""
        + buildtime.toISOString() + "\"}}";
    fs.writeFileSync('./elements/version.json', versionJson);
})();
