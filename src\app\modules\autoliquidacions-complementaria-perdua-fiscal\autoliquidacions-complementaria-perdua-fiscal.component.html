<se-panel
  class="mt-2"
  [colapsible]="true"
  [title]="
    'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_COMPLEMENTARIA_PERDUA_FISCAL.TITLE'
      | translate
  "
>
  <p>
    {{
      'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_COMPLEMENTARIA_PERDUA_FISCAL.SUBTITLE'
        | translate
    }}
  </p>
  <form [formGroup]="autoPerduaFiscalForm">
    <div class="row">
      <!-- Date incumpliment -->
      <div class="col-12 col-lg-6 col-xl-4 text-sm">
        <div class="text-nowrap">
          <span class="input-label d-inline">
            {{
              'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_COMPLEMENTARIA_PERDUA_FISCAL.TERMINI_PRESENTACIO_DATE'
                | translate
            }}
          </span>
          <ng-icon
            class="tooltip-icon"
            name="matInfo"
            [pTooltipAccessible]="
              'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_COMPLEMENTARIA_PERDUA_FISCAL.TERMINI_PRESENTACIO_DATE_TOOLTIP'
                | translate
            "
          ></ng-icon>
        </div>
        <div class="d-flex column-gap-1">
          <se-datepicker
            id="dataTerminiPresentacio"
            [placeholder]="'dd/mm/aaaa'"
            [showIcon]="true"
            formControlName="dataTerminiPresentacio"
            [disabled]="readonlyPresentacio"
          >
          </se-datepicker>
          <div>
            <app-modify-save-buttons
              [(readonly)]="readonlyPresentacio"
              [isDisabled]="!!getFormControl('dataTerminiPresentacio').invalid"
              (saveEvent)="saveData()"
            ></app-modify-save-buttons>
          </div>
        </div>
      </div>
      <!-- Date incumpliment -->
      <div class="col-12 col-lg-6 col-xl-4 text-sm">
        <div>
          <span class="input-label d-inline">
            {{
              'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_COMPLEMENTARIA_PERDUA_FISCAL.INCUMPLIMENT_DATE'
                | translate
            }}
          </span>
          <ng-icon
            class="tooltip-icon"
            name="matInfo"
            [pTooltipAccessible]="
              'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_COMPLEMENTARIA_PERDUA_FISCAL.INCUMPLIMENT_DATE_TOOLTIP'
                | translate
            "
          ></ng-icon>
        </div>
        <div class="d-flex column-gap-1">
          <se-datepicker
            id="dataIncompliment"
            [placeholder]="'dd/mm/aaaa'"
            [showIcon]="true"
            formControlName="dataIncompliment"
            [disabled]="readonlyIncumpliment"
          >
          </se-datepicker>
          <div>
            <app-modify-save-buttons
              [(readonly)]="readonlyIncumpliment"
              [isDisabled]="!!getFormControl('dataIncompliment').invalid"
              (saveEvent)="saveData()"
            ></app-modify-save-buttons>
          </div>
        </div>
      </div>
      <!-- Date termini -->
      <div class="col-12 col-lg-6 col-xl-4 text-sm">
        <div>
          <span class="input-label d-inline">
            {{
              'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_COMPLEMENTARIA_PERDUA_FISCAL.TERMINI_DATE_AUTOLIQUIDACIO'
                | translate
            }}
          </span>
        </div>
        <div class="d-flex column-gap-1">
          <se-datepicker
            id="dataFinTermini"
            [placeholder]="'dd/mm/aaaa'"
            [showIcon]="true"
            formControlName="dataFinTermini"
            [disabled]="readonlyFiTermini"
          >
          </se-datepicker>
          <div>
            <app-modify-save-buttons
              [(readonly)]="readonlyFiTermini"
              [isDisabled]="!!getFormControl('dataFinTermini').invalid"
              (saveEvent)="saveData()"
            ></app-modify-save-buttons>
          </div>
        </div>
      </div>
    </div>

    <div class="mb-2" *ngIf="showInteressosDemoraForaTermini">
      <hr />
      <h5 class="text-md mb-2">
        {{
          'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_COMPLEMENTARIA_PERDUA_FISCAL.TITLE_INTERESSOS_DEMORA'
            | translate
        }}
      </h5>
      <p>
        {{
          'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_COMPLEMENTARIA_PERDUA_FISCAL.SUBTITLE_INTERESSOS_DEMORA'
            | translate
        }}
      </p>
      <div class="row">
        <div class="col-12 col-md-6 col-lg-4 d-flex column-gap-1">
          <se-datepicker
            id="dataFiTermini"
            [placeholder]="'dd/mm/aaaa'"
            formControlName="dataIniciComput"
            [label]="
              'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_COMPLEMENTARIA_PERDUA_FISCAL.INTERESSOS.DATA_INICI_COMPUT'
                | translate
            "
            [showIcon]="true"
            [disabled]="readonlyDataInici"
          >
          </se-datepicker>
          <div class="pt-4">
            <app-modify-save-buttons
              [(readonly)]="readonlyDataInici"
              (saveEvent)="calculateInteressos()"
              [isDisabled]="autoPerduaFiscalForm.invalid"
            ></app-modify-save-buttons>
          </div>
        </div>
        <div class="col-12 col-md-6 col-lg-4 d-flex column-gap-1">
          <se-datepicker
            id="dataFiTermini"
            [placeholder]="'dd/mm/aaaa'"
            formControlName="dataFiComput"
            [label]="
              'SE_TRIBUTS_MF.AUTOLIQUIDACIONS_COMPLEMENTARIA_PERDUA_FISCAL.INTERESSOS.DATA_FI_COMPUT'
                | translate
            "
            [showIcon]="true"
            [disabled]="readonlyDataFi"
          >
          </se-datepicker>
          <div class="pt-4">
            <app-modify-save-buttons
              [(readonly)]="readonlyDataFi"
              (saveEvent)="calculateInteressos()"
              [isDisabled]="autoPerduaFiscalForm.invalid"
            ></app-modify-save-buttons>
          </div>
        </div>
      </div>
    </div>
  </form>
</se-panel>
